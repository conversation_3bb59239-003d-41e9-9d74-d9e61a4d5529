import type { CacheConfig } from '@rma-mono/cache-clearing-util'

/**
 * Mapping of collection names to their cache configurations
 * Only include collections that are connected to Directus
 */
const COLLECTIONS_MAPPING: Record<string, CacheConfig> = {
  espresso_article: {
    redisPrefix: 'espresso-article',
    cdnRoute: '/v1/espresso/article',
  },
  app_config: {
    redisPrefix: 'app-config',
    cdnRoute: '/v1/espresso/app-config',
  },
  peiq_article: {
    redisPrefix: 'peiq-article',
    cdnRoute: '/v1/espresso/peiq-article',
  },
  meinbezirk_app_config: {
    redisPrefix: 'meinbezirk-app-config',
    cdnRoute: '/v1/espresso/meinbezirk-app-config',
  },
}

/**
 * Returns the cache configuration for a specific collection
 */
export function getCollectionCacheObject(collection: string): CacheConfig | undefined {
  const collectionKey = collection as keyof typeof COLLECTIONS_MAPPING
  return COLLECTIONS_MAPPING[collectionKey]
}

/**
 * Returns all collection cache configurations
 */
export function getAllCollectionCacheObjects(): Record<string, CacheConfig> {
  return COLLECTIONS_MAPPING
}
