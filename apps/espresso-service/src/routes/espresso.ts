import { controller, middlewares } from '@regionalmedienaustria/microservice-utils'
import express from 'express'

import { checkVersion, getAppConfig } from '@/controllers/appConfigController.js'
import EspressoArticleController from '@/controllers/espressoArticleController.js'
import { getImages } from '@/controllers/espressoImageController.js'
import { getStreamingMedia } from '@/controllers/espressoStreamingMediaController.js'
import { getUser } from '@/controllers/espressoUserController.js'

import { cacheRouter } from './cache.js'

const router = express.Router()
const CACHE_15_MIN = 60 * 15
const CACHE_1_HOUR = 60 * 60
const CACHE_1_DAY = 60 * 60 * 24

router.get(
  '/articles',
  middlewares.withCache(CACHE_15_MIN),
  controller.wrapController(EspressoArticleController.getArticles),
)

router.get(
  '/article/:articleId',
  middlewares.withCache(CACHE_1_HOUR), // 1 hour is fine for a single article
  controller.wrapController(EspressoArticleController.getArticle),
)

router.get(
  '/article/:articleId/images',
  middlewares.withCache(CACHE_1_DAY), // it is cached for 1 day from the peiq-api-wrapper, so no need to hurry
  controller.wrapController(getImages),
)

router.get('/user/:userId', middlewares.withCache(CACHE_1_DAY), controller.wrapController(getUser))

router.get(
  '/streamingmedia/:mediaId',
  middlewares.withCache(CACHE_1_DAY),
  controller.wrapController(getStreamingMedia),
)

router.get(
  '/check/version/:version',
  middlewares.withCache(CACHE_15_MIN),
  controller.wrapController(checkVersion),
)

router.get(
  '/config/app',
  middlewares.withCache(CACHE_15_MIN),
  controller.wrapController(getAppConfig),
)

router.use('/cache', cacheRouter)

export default router
