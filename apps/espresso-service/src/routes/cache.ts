import { controller } from '@regionalmedienaustria/microservice-utils'
import { Router } from 'express'

import { CacheController } from '@/controllers/cacheController.js'

const cacheRouter = Router()

cacheRouter.get('/clear/:collection/:id', controller.wrapController(CacheController.clearCache))

cacheRouter.get(
  '/clear/:collection',
  controller.wrapController(CacheController.clearCollectionCache),
)

cacheRouter.get('/clear-all', controller.wrapController(CacheController.clearAllCache))

export { cacheRouter }
