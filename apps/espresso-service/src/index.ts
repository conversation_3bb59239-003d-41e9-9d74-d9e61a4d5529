import { app, logger } from '@regionalmedienaustria/microservice-utils'
import express from 'express'

import config from './config.js'
import routes from './routes/index.js'

const initConfig: app.InitConfig = {
  machine: config.machine,
  securityEnabled: false,
}

const appConfig: app.AppConfig = {
  serviceName: config.serviceName,
  port: config.port,
  root_folder: new URL('../', import.meta.url).pathname,
  routes,
  apiSpecInit: null,
  expressApp: express(),
  expressJson: express.json,
}

app
  .init(initConfig, config)
  .then(() => {
    logger.info(`Initialized ${config.serviceName} (machine: ${config.machine})`)
  })
  .then(() => {
    app.start(appConfig)
  })
  .catch((error: any) => {
    if (error && error.message) {
      logger.error(error.message)
    } else {
      logger.error('WTF')
    }
  })
