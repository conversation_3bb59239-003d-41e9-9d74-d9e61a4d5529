/* eslint-disable no-underscore-dangle */
import { requestUtil, Security } from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'

import { PeiqApiWrapper } from '@/utils/peiqApiWrapper.js'

import type {
  PeiqApiParams,
  PeiqApiWrapper as PeiqApiWrapperType,
} from '@regionalmedienaustria/peiq-api-wrapper'

export default class EspressoArticleController {
  static defaultPicks = [
    'id',
    'category_id',
    'location_id',
    'published',
    'visit_count',
    'comment_count',
    'image_count',
    'like_count',
    'tags',
    'static_tags',
    'title',
    'text_elements',
    'url',
    'hero_image',
    'streaming_media',
    'embeds',
    'user_id',
    'streaming_media_as_hero_image',
  ]

  @Security.Grant(Security.ApiKeys.Espresso)
  static async getArticles(req: Request, res: Response) {
    const { page, pageSize } = req.query
    let dateStart: string = requestUtil.getParameter(req, 'dateStart')
    if (!dateStart) {
      dateStart = requestUtil.getParameter(req, 'date_start')
    }
    let dateEnd: string = requestUtil.getParameter(req, 'dateEnd')
    if (!dateEnd) {
      dateEnd = requestUtil.getParameter(req, 'date_end')
    }
    const locationIds = requestUtil.getParameterAsArray<number>(req, 'locationIds', [])
    const categoryIds = requestUtil.getParameterAsArray<number>(
      req,
      'categoryIds',
      [2, 1, 3, 4, 5, 12, 13, 14, 15, 16, 8, 11, 10, 17],
    )
    let userSegments = requestUtil.getParameterAsArray<string>(req, 'userSegments', [
      'oesterreich',
      'redakteur',
      'freier-mitarbeiter',
    ])
    req.pick = req.pick ? req.pick : EspressoArticleController.defaultPicks

    /* if (!locationIds) {
      throw new Error({
        statusCode: 400,
        message: 'param locationIds is missing',
      }) 
    } */

    let pageSizeAsNumber = 20
    let pageAsNumber = 1

    if (typeof page === 'number') {
      pageAsNumber = page
    } else if (typeof page === 'string') {
      pageAsNumber = parseInt(page)
    }

    if (typeof pageSize === 'number') {
      pageSizeAsNumber = pageSize
    } else if (typeof pageSize === 'string') {
      pageSizeAsNumber = parseInt(pageSize)
    }

    const articleQueue = []
    for (let i = 0; i < locationIds.length; i++) {
      const apiParams: PeiqApiParams = {
        locationId: locationIds[i],
        categoryIds: categoryIds,
        userSegments: userSegments,
        page: pageAsNumber,
        pageSize: pageSizeAsNumber,
        dateStart: dateStart,
        dateEnd: dateEnd,
        dateFilterType: 'published',
      }
      articleQueue.push(PeiqApiWrapper.ArticleApi.fetchArticles(apiParams, false, true, true))
    }

    const results: PeiqApiWrapperType.PeiqArticlesResult[] = await Promise.all(articleQueue)
    const resultObj = EspressoArticleController.prepapreFinalResult(
      req,
      results,
      pageAsNumber,
      pageSizeAsNumber,
    )
    res.status(200).send(resultObj)
  }

  /**
   * Combines a list of peiq article result objects to a final result object.
   * @param req - The inital request
   * @param results - A list of PeiqArticleResults
   * @param page - Page as number
   * @param pageSize - Page size as number
   * @returns - The result object
   */
  private static prepapreFinalResult(
    req: Request,
    results: PeiqApiWrapperType.PeiqArticlesResult[],
    page: number,
    pageSize: number,
  ): PeiqApiWrapperType.PeiqArticlesResult {
    const resultObj: PeiqApiWrapperType.PeiqArticlesResult = {
      success: true,
      total_results: 0,
      total_pages: 0,
      page: page,
      page_size: pageSize * page,
      data: [],
    }

    results.forEach((articles) => {
      resultObj.data = resultObj.data.concat(articles.data)
      resultObj.total_results += articles.total_results
      resultObj.total_pages += articles.total_pages
    })
    resultObj.total_pages = parseInt((resultObj.total_results / pageSize).toFixed(0))

    resultObj.data.sort((a: any, b: any) => {
      const datA = new Date(a.published)
      const datB = new Date(b.published)
      return datA < datB ? 1 : datA > datB ? -1 : 0
    })

    resultObj.data = resultObj.data.slice(0, pageSize)
    resultObj.data = requestUtil.reduceData(req, resultObj.data)

    return resultObj
  }

  @Security.Grant(Security.ApiKeys.Espresso)
  static async getArticle(req: Request, res: Response) {
    const { articleId } = req.params

    let articleIdAsNumber = -1
    if (typeof articleId === 'number' || typeof articleId === 'string') {
      articleIdAsNumber = parseFloat(articleId)
    }

    req.pick = req.pick ? req.pick : EspressoArticleController.defaultPicks
    const article = await PeiqApiWrapper.ArticleApi.fetchArticle(articleIdAsNumber)

    const result = requestUtil.reduceData(req, [article])

    res.status(200).send(result[0])
  }
}
