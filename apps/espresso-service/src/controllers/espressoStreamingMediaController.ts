import { requestUtil } from '@regionalmedienaustria/microservice-utils'

import { PeiqApiWrapper } from '@/utils/peiqApiWrapper.js'

import type { Request, Response } from 'express'

export const getStreamingMedia = async (req: Request, res: Response) => {
  const { mediaId } = req.params
  req.pick = req.pick
    ? req.pick
    : ['remote_url', 'id', 'title', 'media_duration', 'copyright', 'preview_image', 'user_id']

  let mediaIdAsNumber = -1
  if (typeof mediaId === 'number' || typeof mediaId === 'string') {
    mediaIdAsNumber = parseFloat(mediaId)
  }

  let media = await PeiqApiWrapper.StreamingMediaApi.fetchStreamingMeadia(mediaIdAsNumber)

  const reducedMedia = requestUtil.reduceData(req, [media])

  res.status(200).send(reducedMedia && reducedMedia.length ? reducedMedia[0] : {})
}
