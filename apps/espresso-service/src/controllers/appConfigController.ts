import { requestUtil } from '@regionalmedienaustria/microservice-utils'

import AppConfigService from '../services/appConfigService.js'

import type { Request, Response } from 'express'

export const checkVersion = async (req: Request, res: Response) => {
  const appVersion = requestUtil.getParameter(req, 'version')

  const versioncheckResult = await AppConfigService.checkVersion(appVersion)

  res.status(200).send(versioncheckResult)
}

export const getAppConfig = async (req: Request, res: Response) => {
  const result = await AppConfigService.getAppConfig()

  res.status(200).send(result)
}
