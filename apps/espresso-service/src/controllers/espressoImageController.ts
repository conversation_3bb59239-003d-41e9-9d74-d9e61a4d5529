import { requestUtil } from '@regionalmedienaustria/microservice-utils'

import { PeiqApiWrapper } from '@/utils/peiqApiWrapper.js'

import type { Request, Response } from 'express'

export const getImages = async (req: Request, res: Response) => {
  const { articleId } = req.params
  req.pick = req.pick ? req.pick : ['id', 'caption', 'copyright', 'url_set']

  let articleIdAsNumber = -1
  if (typeof articleId === 'number' || typeof articleId === 'string') {
    articleIdAsNumber = parseFloat(articleId)
  }

  let images = await PeiqApiWrapper.ImageApi.fetchImages([articleIdAsNumber])

  images = requestUtil.reduceData(req, images)

  res.status(200).send(images)
}
