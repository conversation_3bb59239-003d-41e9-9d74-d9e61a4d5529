import { requestUtil } from '@regionalmedienaustria/microservice-utils'

import { PeiqApiWrapper } from '@/utils/peiqApiWrapper.js'

import type { Request, Response } from 'express'

export const getUser = async (req: Request, res: Response) => {
  const { userId } = req.params
  req.pick = req.pick
    ? req.pick
    : ['id', 'full_name', 'first_name', 'last_name', 'avatar', 'gender', 'user_segment']

  let userIdAsNumber = -1
  if (typeof userId === 'number' || typeof userId === 'string') {
    userIdAsNumber = parseFloat(userId)
  }

  const users = await PeiqApiWrapper.UserApi.fetchUsers([userIdAsNumber])

  users.data = requestUtil.reduceData(req, users.data)

  res.status(200).send(users.data[0])
}
