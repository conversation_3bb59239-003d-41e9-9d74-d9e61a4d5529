import { readSingleton } from '@rma-mono/directus-client'

import { directusClient } from '@/utils/directus/client.js'

type VersioncheckResult = {
  minReqVersion: string
  updateNeeded: boolean
  updateRecommended: boolean
}

type MajorMinorProps = {
  major: number
  minor: number
  configMajor: number
  configMinor: number
}

type MajorMinorPatchProps = MajorMinorProps & {
  patch: number
  configPatch: number
}

export default class AppConfigService {
  public static async checkVersion(appVersion: string): Promise<VersioncheckResult> {
    const configVersion = await this.getMinReqVersion()
    const result = {
      minReqVersion: configVersion,
      ...this.getVersionInfo({
        appVersion,
        configVersion,
      }),
    } satisfies VersioncheckResult

    return result
  }

  private static getVersionInfo({
    appVersion,
    configVersion,
  }: {
    appVersion: string
    configVersion: string
  }) {
    const [major, minor, patch] = appVersion.split('.').map((v) => parseInt(v))
    const [configMajor, configMinor, configPatch] = configVersion.split('.').map((v) => parseInt(v))

    return {
      updateNeeded: this.isUpdateNeeded({
        major,
        minor,
        configMajor,
        configMinor,
      }),
      updateRecommended: this.isUpdateRecommended({
        major,
        minor,
        patch,
        configMajor,
        configMinor,
        configPatch,
      }),
    }
  }

  private static async getMinReqVersion() {
    const version = await directusClient.request(
      readSingleton('meinbezirk_app_config', {
        fields: ['version'],
      }),
    )

    return version.version
  }

  private static isUpdateNeeded({ major, minor, configMajor, configMinor }: MajorMinorProps) {
    return major < configMajor || (major <= configMajor && minor < configMinor)
  }

  private static isUpdateRecommended({
    major,
    minor,
    patch,
    configMajor,
    configMinor,
    configPatch,
  }: MajorMinorPatchProps) {
    return (
      this.isUpdateNeeded({
        major,
        minor,
        configMajor,
        configMinor,
      }) ||
      (major <= configMajor && minor <= configMinor && patch < configPatch)
    )
  }

  public static async getAppConfig() {
    const appConfig = await directusClient.request(readSingleton('meinbezirk_app_config'))

    const result = [
      {
        id: 1,
        title: appConfig.title_about,
        content: appConfig.text_field_about,
      },
      {
        id: 2,
        title: appConfig.title_faq,
        content: appConfig.text_field_faq,
      },
      {
        id: 3,
        title: appConfig.title_feedback,
        content: appConfig.text_field_feedback,
      },
      {
        id: 4,
        title: appConfig.title_anb,
        content: appConfig.text_field_anb,
      },
      {
        id: 5,
        title: appConfig.title_data_privacy,
        content: appConfig.text_field_data_privacy,
      },
      {
        id: 6,
        title: appConfig.title_imprint,
        content: appConfig.text_field_imprint,
      },
    ]
    return result
  }
}
