import { logger } from '@regionalmedienaustria/microservice-utils'
import { CacheClearingService } from '@rma-mono/cache-clearing-util'

import { getCollectionCacheObject, getAllCollectionCacheObjects } from '@/cache/collections.js'

import type { CacheConfig } from '@rma-mono/cache-clearing-util'

/**
 * Service for cache management in the Espresso service
 */
export class CacheService {
  private static cacheClearingService: CacheClearingService | null = null

  /**
   * Initializes the CacheClearingService when needed
   */
  private static async getCacheClearingService(): Promise<CacheClearingService> {
    if (!this.cacheClearingService) {
      // CDN cache invalidation is the only functionality used in Espresso
      this.cacheClearingService = new CacheClearingService(undefined, {
        useRedis: false,
        useCdn: true,
      })
    }
    return this.cacheClearingService
  }

  /**
   * Deletes the cache for a specific item of a collection
   */
  public static async clearCollectionItemCache(collection: string, id: string): Promise<boolean> {
    const config = getCollectionCacheObject(collection)
    if (!config) {
      const errorMsg = `Collection ${collection} not configured for cache clearing`
      logger.error(errorMsg)
      throw new Error(errorMsg)
    }

    try {
      logger.info(`Clearing cache for collection ${collection} with ID ${id}`)
      const service = await this.getCacheClearingService()
      await service.clearCache(config, id)
      return true
    } catch (error) {
      logger.error(`Error clearing cache for collection ${collection} with ID ${id}:`, error)
      throw error
    }
  }

  /**
   * Deletes the entire cache for a specific collection
   */
  public static async clearAllCollectionCache(collection: string): Promise<boolean> {
    const config = getCollectionCacheObject(collection)
    if (!config) {
      const errorMsg = `Collection ${collection} not configured for cache clearing`
      logger.error(errorMsg)
      throw new Error(errorMsg)
    }

    try {
      logger.info(`Clearing all cache for collection ${collection}`)
      const service = await this.getCacheClearingService()
      await service.clearAllCollectionCache(config)
      return true
    } catch (error) {
      logger.error(`Error clearing all cache for collection ${collection}:`, error)
      throw error
    }
  }

  /**
   * Deletes the cache for all configured collections
   */
  public static async clearAllCaches(): Promise<Record<string, boolean>> {
    const collections = getAllCollectionCacheObjects()
    const results: Record<string, boolean> = {}

    for (const [collection, config] of Object.entries(collections)) {
      try {
        const service = await this.getCacheClearingService()
        await service.clearAllCollectionCache(config)
        logger.info(`Cleared all cache for collection: ${collection}`)
        results[collection] = true
      } catch (error) {
        logger.error(`Failed to clear cache for collection ${collection}:`, error)
        results[collection] = false
      }
    }

    return results
  }

  /**
   * Direct deletion of a specific cache entry
   * This method can be used for special cases
   */
  public static async clearSpecificCache(redisKey: string, cdnRoute: string): Promise<boolean> {
    const config: CacheConfig = {
      redisPrefix: redisKey,
      cdnRoute: cdnRoute,
    }

    try {
      logger.info(`Clearing specific cache with redisKey ${redisKey} and cdnRoute ${cdnRoute}`)
      const service = await this.getCacheClearingService()
      await service.clearCache(config, '') // Empty ID, since the keys are passed directly
      return true
    } catch (error) {
      logger.error(`Error clearing specific cache:`, error)
      throw error
    }
  }
}
