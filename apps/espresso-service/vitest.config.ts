import { resolve } from 'node:path'
import { defineConfig } from 'vitest/config'
import tsconfigPaths from 'vite-tsconfig-paths'
import { config } from 'dotenv'

// Load environment variables from .env.example
config({ path: '.env.example' })

export default defineConfig({
  test: {
    include: ['test/**/*.test.ts'],
    env: process.env,
    setupFiles: ['dotenv/config'], // Add this line to ensure dotenv is loaded before tests
  },
  plugins: [tsconfigPaths()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
})
