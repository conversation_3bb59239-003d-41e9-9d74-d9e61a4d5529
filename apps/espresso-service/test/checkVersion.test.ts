import * as dotenv from 'dotenv'
import sinon from 'sinon'
import { describe, it, expect, afterEach, beforeEach } from 'vitest'

import AppConfigService from '../src/services/appConfigService.js'
import * as directusModule from '../src/utils/directus/client.js'
// imports which require the .env file

dotenv.config({
  path: '.env.example',
})

describe('CheckService', () => {
  let directusClientStub: sinon.SinonStub

  beforeEach(() => {
    // Create a stub for the directusClient
    directusClientStub = sinon.stub(directusModule, 'directusClient').value({
      request: sinon.stub().resolves({
        version: '2.3.2',
      }),
    })
  })

  afterEach(() => {
    directusClientStub.restore()
  })

  describe('check equal versions', () => {
    it('update needed and recommended should be false', async () => {
      //version needs to be equal to latest version
      const data = await AppConfigService.checkVersion('2.3.2')
      expect(data.updateNeeded).toBe(false)
      expect(data.updateRecommended).toBe(false)
    })
  })

  describe('check lower MAJOR versions', () => {
    it('update needed and recommended should be true', async () => {
      //major version needs to be lower than current major version
      const data = await AppConfigService.checkVersion('1.0.2')
      expect(data.updateNeeded).toBe(true)
      expect(data.updateRecommended).toBe(true)
    })
  })

  describe('check lower MINOR versions', () => {
    it('update needed and recommended should be true', async () => {
      //major version needs to be equal to the current version and minor version needs to be lower than current minor version
      const data = await AppConfigService.checkVersion('2.-1.2')
      expect(data.updateNeeded).toBe(true)
      expect(data.updateRecommended).toBe(true)
    })
  })

  describe('check lower PATCH versions', () => {
    it('update needed should be false and recommended should be true', async () => {
      //major und minor version needs to be equal to the current version and fix needs to be smaller than current version
      const data = await AppConfigService.checkVersion('2.3.-1')
      expect(data.updateNeeded).toBe(false)
      expect(data.updateRecommended).toBe(true)
    })
  })
})
