import { describe, it, expect } from 'vitest'

import { SearchParamsBuilder } from '../src/utils/searchParamsBuilder.js'

import type { AutoContentCollection } from '../src/services/directusService.js'

describe('SearchParamsBuilder', () => {
  describe('extractLocationIds', () => {
    it('should extract numeric location IDs correctly', () => {
      const region = [
        { trees_id: 123 },
        { trees_id: 456 },
        { trees_id: 789 }
      ]
      
      const result = SearchParamsBuilder.extractLocationIds(region)
      expect(result).toEqual([123, 456, 789])
    })

    it('should handle string trees_id values', () => {
      const region = [
        { trees_id: '123' },
        { trees_id: '456' },
        { trees_id: '789' }
      ]
      
      const result = SearchParamsBuilder.extractLocationIds(region)
      expect(result).toEqual([123, 456, 789])
    })

    it('should handle mixed number and string types', () => {
      const region = [
        { trees_id: 123 },
        { trees_id: '456' },
        { trees_id: 789 }
      ]
      
      const result = SearchParamsBuilder.extractLocationIds(region)
      expect(result).toEqual([123, 456, 789])
    })

    it('should filter out invalid IDs (0 and negative)', () => {
      const region = [
        { trees_id: 123 },
        { trees_id: 0 },
        { trees_id: -1 },
        { trees_id: '456' },
        { trees_id: '-789' }
      ]
      
      const result = SearchParamsBuilder.extractLocationIds(region)
      expect(result).toEqual([123, 456])
    })

    it('should handle undefined and null trees_id values', () => {
      const region = [
        { trees_id: 123 },
        { trees_id: undefined },
        { trees_id: null },
        { trees_id: '456' }
      ]
      
      const result = SearchParamsBuilder.extractLocationIds(region)
      expect(result).toEqual([123, 456])
    })

    it('should handle empty string trees_id values', () => {
      const region = [
        { trees_id: 123 },
        { trees_id: '' },
        { trees_id: '   ' },
        { trees_id: '456' }
      ]
      
      const result = SearchParamsBuilder.extractLocationIds(region)
      expect(result).toEqual([123, 456])
    })

    it('should return undefined for undefined region', () => {
      const result = SearchParamsBuilder.extractLocationIds(undefined)
      expect(result).toBeUndefined()
    })

    it('should return empty array for empty region array', () => {
      const result = SearchParamsBuilder.extractLocationIds([])
      expect(result).toEqual([])
    })

    it('should handle Trees object type for trees_id', () => {
      const region = [
        { trees_id: { id: 123, title: 'Wien' } },
        { trees_id: 456 },
        { trees_id: '789' }
      ]
      
      const result = SearchParamsBuilder.extractLocationIds(region)
      // When trees_id is an object, toString() will return "[object Object]" 
      // which parseInt will convert to NaN, then 0, which gets filtered out
      expect(result).toEqual([456, 789])
    })
  })

  describe('buildSearchParams integration', () => {
    it('should use extractLocationIds in buildSearchParams', () => {
      const mockCollection: Partial<AutoContentCollection> = {
        id: 1,
        region: [
          { trees_id: 123 },
          { trees_id: '456' },
          { trees_id: 0 } // Should be filtered out
        ],
        category: ['lokales'],
        grouping: ['district'],
        time_in_past: 24,
        status: 'published',
        group: 'test-group',
        content: 'test content'
      }

      const result = SearchParamsBuilder.buildSearchParams(mockCollection as AutoContentCollection)
      
      expect(result.locationIds).toEqual([123, 456])
      expect(result.groupBy).toBe('district')
      expect(result.hoursBack).toBe(24)
    })
  })
})
