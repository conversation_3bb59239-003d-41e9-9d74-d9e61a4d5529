import { describe, it, expect, vi } from 'vitest'

import { CollectionValidator } from '@/helpers/collectionValidator.js'
import { ContentBuilder } from '@/helpers/contentBuilder.js'
import { ScheduleRules } from '@/helpers/scheduleRules.js'
import { CategoryMapper } from '@/utils/categoryMapper.js'
import { CollectionDataExtractor } from '@/utils/collectionDataExtractor.js'
import { PublishTimeCalculator } from '@/utils/publishTimeCalculator.js'
import { TimeHelpers } from '@/utils/timeHelpers.js'

// Mock DirectusService to avoid initialization issues in tests
vi.mock('@/services/directusService.js', () => ({
  DirectusService: {
    getFieldMetadata: vi.fn().mockResolvedValue({
      meta: {
        options: {
          choices: [
            { value: 'lokales', text: 'Lokales' },
            { value: 'sport', text: 'Sport' },
            { value: 'politik', text: 'Politik' }
          ]
        }
      }
    })
  }
}))

describe('TopArticlesCollectionService', () => {
  describe('ScheduleRules', () => {
    it('should validate day of week correctly', () => {
      expect(ScheduleRules.validateDayOfWeek(['Monday', 'Tuesday', 'Wednesday'], 1)).toBe(true) // Monday
      expect(ScheduleRules.validateDayOfWeek(['Monday', 'Tuesday', 'Wednesday'], 4)).toBe(false) // Thursday
      expect(ScheduleRules.validateDayOfWeek([], 1)).toBe(true) // Empty = all days allowed
    })

    it('should validate day of month correctly', () => {
      const testDate = new Date(2024, 0, 15) // January 15, 2024
      expect(ScheduleRules.validateDayOfMonth({
        allowedDays: ['1', '15', '30'],
        currentDate: 15,
        currentTime: testDate
      })).toBe(true)
      expect(ScheduleRules.validateDayOfMonth({
        allowedDays: ['1', '15', '30'],
        currentDate: 20,
        currentTime: testDate
      })).toBe(false)
      expect(ScheduleRules.validateDayOfMonth({
        allowedDays: [],
        currentDate: 15,
        currentTime: testDate
      })).toBe(true) // Empty = all days allowed
    })

    it('should validate time correctly using TimeHelpers', () => {
      expect(TimeHelpers.isTimeWithinTolerance({
        targetHour: 10, targetMinute: 30, currentHour: 10, currentMinute: 30, toleranceMinutes: 0
      })).toBe(true)
      expect(TimeHelpers.isTimeWithinTolerance({
        targetHour: 10, targetMinute: 30, currentHour: 10, currentMinute: 31, toleranceMinutes: 0
      })).toBe(false)
      expect(TimeHelpers.isTimeWithinTolerance({
        targetHour: 10, targetMinute: 30, currentHour: 11, currentMinute: 30, toleranceMinutes: 0
      })).toBe(false)
    })
  })

  describe('CollectionDataExtractor', () => {
    it('should extract region data correctly', () => {
      const mockCollection = {
        id: 1,
        region: [{ trees_id: { id: 1, title: 'Wien' } }],
        category: ['lokales'],
        status: 'published',
        group: 'test-group',
        content: 'test content'
      }
      
      const { regionName, regionId } = CollectionDataExtractor.extractRegionData(mockCollection as any)
      expect(regionName).toBe('Wien')
      expect(regionId).toBe(1)
    })

    it('should extract category data correctly', async () => {
      const mockCollection = {
        id: 1,
        region: [],
        category: ['lokales', 'sport'],
        status: 'published',
        group: 'test-group',
        content: 'test content'
      }
      
      const { categoryName, categoryNames } = await CollectionDataExtractor.extractCategoryData(mockCollection as any)
      expect(categoryName).toBe('Lokales')
      expect(categoryNames).toEqual(['lokales', 'sport'])
    })

    it('should handle missing data with fallbacks', async () => {
      const mockCollection = {
        id: 1,
        region: [],
        category: [],
        status: 'published',
        group: 'test-group',
        content: 'test content'
      }
      
      const { regionName, categoryName } = await CollectionDataExtractor.extractCollectionData(mockCollection as any)
      expect(regionName).toBe('Wien')
      expect(categoryName).toBe('Regionauten')
    })

  })

  describe('ContentBuilder', () => {
    it('should generate appropriate teaser for district grouping', () => {
      const mockCollection = {
        id: 1,
        grouping: ['district'] as string[],
        region: [{ trees_id: { id: 1, title: 'Wien' } }],
        category: ['lokales'],
        keywords: 'test',
        status: 'published',
        group: 'test-group',
        content: 'test content'
      }
      
      const teaser = ContentBuilder.generateTeaser(mockCollection as any, 5)
      expect(teaser).toContain('5')
      expect(teaser).toContain('Artikel')
      expect(teaser).toContain('Region')
    })

    it('should generate appropriate teaser for category grouping', () => {
      const mockCollection = {
        id: 1,
        grouping: ['category'] as string[],
        region: [{ trees_id: { id: 1, title: 'Wien' } }],
        category: ['sport'],
        keywords: 'test',
        status: 'published',
        group: 'test-group',
        content: 'test content'
      }
      
      const teaser = ContentBuilder.generateTeaser(mockCollection as any, 3)
      expect(teaser).toContain('3')
      expect(teaser).toContain('Artikel')
      expect(teaser).toContain('Kategorie')
    })
  })

  describe('PublishTimeCalculator', () => {

    it('should calculate publish time at specific time', () => {
      const mockCollection = {
        id: 1,
        publish_time: '14:30',
        status: 'published',
        group: 'test-group',
        content: 'test content'
      }
      
      const publishTime = PublishTimeCalculator.calculatePublishTime(mockCollection as any)
      expect(publishTime.getHours()).toBe(14)
      expect(publishTime.getMinutes()).toBe(30)
    })

    it('should throw error when no publish_time specified in strict mode', () => {
      const mockCollection = {
        id: 1,
        status: 'published',
        group: 'test-group',
        content: 'test content'
      }

      expect(() => {
        PublishTimeCalculator.calculatePublishTime(mockCollection as any)
      }).toThrow('Collection 1 has no publish_time configured')
    })

    it('should use fallback when explicitly allowed', () => {
      const mockCollection = {
        id: 1,
        status: 'published',
        group: 'test-group',
        content: 'test content'
      }

      const publishTime = PublishTimeCalculator.calculatePublishTime(
        mockCollection as any,
        { allowFallback: true }
      )
      expect(publishTime.getHours()).toBe(12) // Default noon
      expect(publishTime.getMinutes()).toBe(0)
    })
  })

  describe('CollectionValidator', () => {
    it('should allow proceeding with zero articles as per acceptance criteria', () => {
      const mockCollection = {
        id: 1,
        status: 'published',
        title: 'Test Collection',
        region: [{ trees_id: 1 }],
        category: ['lokales'],
        grouping: ['district'],
        keywords: 'test',
        content: 'test content',
        group: 'test',
        time_in_past: 24
      }
      
      // Test with 0 articles (below threshold of 3)
      const zeroResult = CollectionValidator.validateMinimumArticles(0, mockCollection as any, 3)
      expect(zeroResult.isValid).toBe(false)
      expect(zeroResult.shouldProceed).toBe(true) // Should proceed despite being invalid
      expect(zeroResult.warning).toContain('Not enough articles found')
      expect(zeroResult.warning).toContain('Found: 0, minimum: 3')
      
      // Test with 2 articles (below threshold of 3)
      const fewResult = CollectionValidator.validateMinimumArticles(2, mockCollection as any, 3)
      expect(fewResult.isValid).toBe(false)
      expect(fewResult.shouldProceed).toBe(true) // Should proceed despite being invalid
      expect(fewResult.warning).toContain('Found: 2, minimum: 3')
      
      // Test with 3 articles (meets threshold)
      const validResult = CollectionValidator.validateMinimumArticles(3, mockCollection as any, 3)
      expect(validResult.isValid).toBe(true)
      expect(validResult.shouldProceed).toBe(true)
      expect(validResult.warning).toBeUndefined()
      
      // Test with 5 articles (exceeds threshold)
      const manyResult = CollectionValidator.validateMinimumArticles(5, mockCollection as any, 3)
      expect(manyResult.isValid).toBe(true)
      expect(manyResult.shouldProceed).toBe(true)
      expect(manyResult.warning).toBeUndefined()
    })
  })

  describe('CategoryMapper', () => {
    it('should map common category names to correct IDs', () => {
      expect(CategoryMapper.mapCategoryNameToId('lokales')).toBe(2)
      expect(CategoryMapper.mapCategoryNameToId('sport')).toBe(3)
      expect(CategoryMapper.mapCategoryNameToId('wirtschaft')).toBe(4)
      expect(CategoryMapper.mapCategoryNameToId('politik')).toBe(1)
      expect(CategoryMapper.mapCategoryNameToId('gesundheit')).toBe(12)
      expect(CategoryMapper.mapCategoryNameToId('freizeit')).toBe(13)
    })

    it('should handle case-insensitive mapping', () => {
      expect(CategoryMapper.mapCategoryNameToId('Lokales')).toBe(2)
      expect(CategoryMapper.mapCategoryNameToId('SPORT')).toBe(3)
      expect(CategoryMapper.mapCategoryNameToId('WiRtScHaFt')).toBe(4)
    })

    it('should handle categories with special characters', () => {
      expect(CategoryMapper.mapCategoryNameToId('profis-aus-der-region')).toBe(8) // Exact match
      expect(CategoryMapper.mapCategoryNameToId('lokales')).toBe(2) // Direct match
      expect(CategoryMapper.mapCategoryNameToId('sport')).toBe(3) // Direct match
    })

    it('should map categories with name_norm differences', () => {
      expect(CategoryMapper.mapCategoryNameToId('motor')).toBe(15) // Mobilität
      expect(CategoryMapper.mapCategoryNameToId('gedanken')).toBe(10) // Meinung
      expect(CategoryMapper.mapCategoryNameToId('bauen')).toBe(14) // Bauen & Wohnen
    })

    it('should return null for unknown categories', () => {
      expect(CategoryMapper.mapCategoryNameToId('unknown')).toBeNull()
      expect(CategoryMapper.mapCategoryNameToId('')).toBeNull()
      expect(CategoryMapper.mapCategoryNameToId('invalid-category')).toBeNull()
    })

    it('should map arrays of category names', () => {
      const categories = ['lokales', 'sport', 'wirtschaft']
      const ids = CategoryMapper.mapCategoryNamesToIds(categories)
      expect(ids).toEqual([2, 3, 4])
    })

    it('should filter out unmapped categories from arrays', () => {
      const categories = ['lokales', 'unknown', 'sport', 'invalid']
      const ids = CategoryMapper.mapCategoryNamesToIds(categories)
      expect(ids).toEqual([2, 3])
    })

    it('should use fallback for unmapped categories', () => {
      expect(CategoryMapper.mapCategoryNameWithFallback('unknown')).toBe(18) // Regionauten-Community
      expect(CategoryMapper.mapCategoryNameWithFallback(null)).toBe(18)
      expect(CategoryMapper.mapCategoryNameWithFallback(undefined)).toBe(18)
    })
  })
})