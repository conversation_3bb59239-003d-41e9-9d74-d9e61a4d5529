import { describe, it, expect, vi } from 'vitest'

import { ValidationService } from '../src/services/validationService.js'

import type { AutoContentCollection } from '../src/services/directusService.js'
import type { TopArticlesCollectionData } from '../src/services/topArticlesService.js'

describe('ValidationService', () => {
  const mockCollection: Partial<AutoContentCollection> = {
    id: 123,
    title: 'Test Collection',
    content: 'Test Content',
    status: 'published',
    group: 'test-group',
    region: [{ trees_id: 1 }],
    category: ['lokales'],
  }

  const validData: TopArticlesCollectionData = {
    title: 'Die Top Beiträge aus Wien',
    subtitle: 'Die beliebtesten Artikel der Woche',
    content: '<h2>Lokales</h2><p>Die beliebtesten Artikel aus Wien diese Woche. Entdecken Sie spannende Geschichten und aktuelle Ereignisse aus Ihrer Region. Bleiben Sie informiert über lokale Neuigkeiten.</p>',
    categoryId: 2,
    locationId: 1,
    publishedDate: new Date().toISOString(),
    tags: ['test'],
    userId: 1,
    heroImageId: '123', // Add hero image to avoid warning
  }

  describe('validateCollectionData', () => {
    it('should pass validation for valid data', () => {
      const result = ValidationService.validateCollectionData(validData, mockCollection as AutoContentCollection)
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.warnings).toHaveLength(0)
    })

    it('should fail validation for unprocessed placeholders in title', () => {
      const invalidData = {
        ...validData,
        title: 'Die Top Beiträge aus {{region}}',
      }
      
      const result = ValidationService.validateCollectionData(invalidData, mockCollection as AutoContentCollection)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Title contains unprocessed placeholder: Die Top Beiträge aus {{region}}')
    })

    it('should fail validation for development placeholders', () => {
      const invalidData = {
        ...validData,
        title: 'Die Top Beiträge aus TEST_PLACEHOLDER',
      }
      
      const result = ValidationService.validateCollectionData(invalidData, mockCollection as AutoContentCollection)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Title contains development placeholder')
    })

    it('should fail validation for missing required fields', () => {
      const invalidData = {
        ...validData,
        title: '',
        content: '',
      }
      
      const result = ValidationService.validateCollectionData(invalidData, mockCollection as AutoContentCollection)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Title is required')
      expect(result.errors).toContain('Content is required')
    })

    it('should fail validation for title too long', () => {
      const invalidData = {
        ...validData,
        title: 'A'.repeat(201), // Maximum is 200 characters
      }
      
      const result = ValidationService.validateCollectionData(invalidData, mockCollection as AutoContentCollection)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Title too long (maximum 200 characters)')
    })

    it('should warn about missing H2 headers', () => {
      const dataWithoutH2 = {
        ...validData,
        content: '<p>Die beliebtesten Artikel aus Wien diese Woche. Entdecken Sie spannende Geschichten und aktuelle Ereignisse aus Ihrer Region. Bleiben Sie informiert über lokale Neuigkeiten und mehr.</p>',
      }
      
      const result = ValidationService.validateCollectionData(dataWithoutH2, mockCollection as AutoContentCollection)
      
      expect(result.isValid).toBe(true)
      expect(result.warnings).toContain('Content should contain H2 headers for better structure')
    })

    it('should fail validation for disallowed HTML tags', () => {
      const invalidData = {
        ...validData,
        content: '<h2>Header</h2><script>alert("XSS")</script><p>Die beliebtesten Artikel aus Wien diese Woche. Entdecken Sie spannende Geschichten und aktuelle Ereignisse aus Ihrer Region.</p>',
      }
      
      const result = ValidationService.validateCollectionData(invalidData, mockCollection as AutoContentCollection)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Content contains forbidden tags: script')
    })

    it('should handle validation errors gracefully', () => {
      // Mock logger to avoid console output
      vi.spyOn(console, 'error').mockImplementation(() => {})

      // Pass null data to trigger error
      expect(() => {
        ValidationService.validateCollectionData(null as any, mockCollection as AutoContentCollection)
      }).toThrow()
    })
  })
})