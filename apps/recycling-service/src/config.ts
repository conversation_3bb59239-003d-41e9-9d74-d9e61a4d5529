import type { app } from '@regionalmedienaustria/microservice-utils'

export const config: app.baseConfig.Serverless & { 
  PEIQ_CLIENT_ID: string; 
  PEIQ_CLIENT_SECRET: string;
  CMS_API_URL: string;
  DIRECTUS_RECYCLING_TOKEN: string;
  MIN_ARTICLES_THRESHOLD: number;
  APP_ENV: string;
} = {
  machine: process.env.MACHINE || 'local',
  port: process.env.PORT || '8080',
  serviceName: process.env.SERVICE_NAME || 'recycling-service',
  serviceApiKey: '',
  PEIQ_CLIENT_ID: process.env.PEIQ_CLIENT_ID || '',
  PEIQ_CLIENT_SECRET: process.env.PEIQ_CLIENT_SECRET || '',
  CMS_API_URL: process.env.CMS_API_URL || 'https://stage-cmsapi.meinbezirk.at',
  DIRECTUS_RECYCLING_TOKEN: process.env.DIRECTUS_RECYCLING_TOKEN || '',
  MIN_ARTICLES_THRESHOLD: parseInt(process.env.MIN_ARTICLES_THRESHOLD || '3', 10),
  APP_ENV: process.env.APP_ENV || 'development',
}