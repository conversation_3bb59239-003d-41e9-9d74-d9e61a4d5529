import { logger } from '@regionalmedienaustria/microservice-utils'

import type { AutoContentCollection } from './directusService.js'

/**
 * Service for validating Directus collection configurations.
 * Used by Directus hooks to prevent invalid data from being saved.
 */
export class DirectusValidationService {
  /**
   * Validates collection configuration before saving to Directus.
   *
   * Performs comprehensive validation including:
   * - Required fields (keywords, category, region, grouping)
   * - Field value constraints (enums, ranges)
   * - Placeholder detection in text fields
   * - Schedule configuration validation
   *
   * @param data - Collection data to validate
   * @returns Validation result with errors array
   */
  static validateAutoContentCollection(data: Partial<AutoContentCollection>): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    try {
      if (typeof data.keywords === 'string') {
        if (this.containsPlaceholder(data.keywords)) {
          errors.push(`Keywords field contains placeholder: ${data.keywords}`)
        }
      }

      if (
        !data.keywords ||
        (typeof data.keywords === 'string' && data.keywords.trim().length === 0)
      ) {
        errors.push('Keywords are required')
      }

      if (!data.category || data.category.length === 0) {
        errors.push('At least one category is required')
      }

      if (!data.region || data.region.length === 0) {
        errors.push('At least one region is required')
      }

      if (!data.grouping || data.grouping.length === 0) {
        errors.push('Grouping is required')
      } else {
        const validGroupings = ['district', 'category']
        const invalidGroupings = data.grouping.filter((g) => !validGroupings.includes(g))
        if (invalidGroupings.length > 0) {
          errors.push(`Invalid grouping values: ${invalidGroupings.join(', ')}`)
        }
      }

      if (typeof data.content_type === 'string') {
        const validContentTypes = ['gallery_collection', 'article_collection']
        if (!validContentTypes.includes(data.content_type)) {
          errors.push(
            `Invalid content_type: ${data.content_type}. Must be one of: ${validContentTypes.join(
              ', ',
            )}`,
          )
        }
      }

      if (typeof data.collection_criteria === 'string') {
        const validCriteria = ['most_likes', 'most_viewed', 'keywords']
        if (!validCriteria.includes(data.collection_criteria)) {
          errors.push(
            `Invalid collection_criteria: ${
              data.collection_criteria
            }. Must be one of: ${validCriteria.join(', ')}`,
          )
        }
      }

      if (typeof data.interval === 'string') {
        const validIntervals = ['daily', 'weekly', 'monthly']
        if (!validIntervals.includes(data.interval)) {
          errors.push(
            `Invalid interval: ${data.interval}. Must be one of: ${validIntervals.join(', ')}`,
          )
        }
      }

      if (typeof data.monthly_occurrence === 'string') {
        const validOccurrences = ['first', 'last']
        if (!validOccurrences.includes(data.monthly_occurrence)) {
          errors.push(
            `Invalid monthly_occurrence: ${
              data.monthly_occurrence
            }. Must be one of: ${validOccurrences.join(', ')}`,
          )
        }
      }

      if (data.days && Array.isArray(data.days)) {
        const validDays = [
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
          'sunday',
        ]
        const invalidDays = data.days.filter((day) => !validDays.includes(day))
        if (invalidDays.length > 0) {
          errors.push(
            `Invalid days: ${invalidDays.join(', ')}. Must be one of: ${validDays.join(', ')}`,
          )
        }
      }

      if (typeof data.status === 'string') {
        const validStatuses = ['published', 'draft', 'archived']
        if (!validStatuses.includes(data.status)) {
          errors.push(`Invalid status: ${data.status}. Must be one of: ${validStatuses.join(', ')}`)
        }
      }

      if (data.time_in_past !== undefined && data.time_in_past !== null && data.time_in_past < 1) {
        errors.push('Time in past must be at least 1 hour')
      }

      if (typeof data.negative_keywords === 'string') {
        if (this.containsPlaceholder(data.negative_keywords)) {
          errors.push(`Negative keywords field contains placeholder: ${data.negative_keywords}`)
        }
      }

      const valid = errors.length === 0

      if (!valid) {
        logger.warn('Directus validation failed for auto_content_collection', {
          errors,
          dataId: data.id,
        })
      }

      return { valid, errors }
    } catch (error) {
      logger.error('Error during Directus validation', { error, dataId: data.id })
      return {
        valid: false,
        errors: ['Validation process failed. Please check your input and try again.'],
      }
    }
  }

  /**
   * Detects placeholder patterns in text.
   *
   * Checks for:
   * - Bracket patterns: {}, [], <>
   * - Common placeholders: XXX, TODO, FIXME
   * - Lorem ipsum text
   *
   * @param text - Text to check
   * @returns True if placeholders found
   */
  static containsPlaceholder(text: string): boolean {
    if (!text || typeof text !== 'string') {
      return false
    }

    const placeholderPatterns = [
      /\{[^}]*\}/,
      /\[[^\]]*\]/,
      /\<[^>]*\>/,
      /XXX/gi,
      /TODO/gi,
      /FIXME/gi,
      /placeholder/gi,
      /lorem ipsum/gi,
    ]

    return placeholderPatterns.some((pattern) => pattern.test(text))
  }

  /**
   * Parses time string to minutes.
   *
   * @param timeString - Time in HH:MM format
   * @returns Total minutes or 0 on error
   */
  static parseTime(timeString: string): number {
    try {
      const [hours, minutes] = timeString.split(':').map(Number)
      if (isNaN(hours) || isNaN(minutes)) {
        throw new Error('Invalid time format')
      }
      return hours * 60 + minutes
    } catch (error) {
      logger.warn('Failed to parse time string', { timeString, error })
      return 0
    }
  }

  /**
   * Formats errors for Directus API response.
   *
   * @param errors - Array of error messages
   * @returns Directus-compatible error object
   */
  static formatDirectusError(errors: string[]): object {
    return {
      errors: errors.map((error) => ({
        message: error,
        extensions: {
          code: 'VALIDATION_ERROR',
        },
      })),
    }
  }
}
