import { logger } from '@regionalmedienaustria/microservice-utils'

import { ContentBuilder } from '../helpers/contentBuilder.js'
import { TopArticlesLogger } from '../helpers/topArticlesLogger.js'
import { ArticleValidator } from '../utils/articleValidator.js'
import { CategoryMapper } from '../utils/categoryMapper.js'
import { peiqApiCircuitBreaker } from '../utils/circuitBreaker.js'
import { CollectionDataExtractor } from '../utils/collectionDataExtractor.js'
import { PeiqApiWrapper } from '../utils/peiq/peiqApiWrapper.js'
import { PublishTimeCalculator } from '../utils/publishTimeCalculator.js'
import { SearchParamsBuilder } from '../utils/searchParamsBuilder.js'

import { DirectusService, type AutoContentCollection } from './directusService.js'
import { ValidationService } from './validationService.js'

import type { <PERSON>eiqApiWrapper as PeiqApiWrapperType } from '@regionalmedienaustria/peiq-api-wrapper'

export type TopArticlesCollectionData = {
  title: string
  subtitle?: string
  content: string
  categoryId: number
  locationId: number
  publishedDate: string
  heroImageId?: string
  tags: string[]
  userId: number
}

/**
 * Service for finding and creating top articles collections.
 * Integrates with PEIQ API for article search and creation.
 */
export class TopArticlesService {
  /**
   * Searches for top articles based on collection criteria.
   *
   * Uses PEIQ API to find articles matching:
   * - Time range (time_in_past)
   * - Regions (location IDs)
   * - Categories
   * - Keywords
   * - Grouping strategy (district/category)
   * - Sort criteria (views/likes/comments)
   *
   * @param collection - Collection configuration from Directus
   * @returns Array of validated articles sorted by relevance
   * @throws Error if PEIQ API is unavailable or search fails
   */
  static async findTopArticles(
    collection: AutoContentCollection,
  ): Promise<PeiqApiWrapperType.ArticleData[]> {
    try {
      const startTime = Date.now()
      TopArticlesLogger.logSearchStart(collection)

      const searchParams = SearchParamsBuilder.buildSearchParams(collection)

      let groupedResults: PeiqApiWrapperType.GroupedArticles
      try {
        groupedResults = await peiqApiCircuitBreaker.execute(async () =>
          PeiqApiWrapper.ArticleSearchService.searchArticles(searchParams),
        )
      } catch (error) {
        if (error instanceof Error && error.message.includes('Circuit breaker')) {
          logger.warn('PEIQ API Circuit breaker is OPEN - skipping article search', {
            collectionId: collection.id,
            circuitBreakerStatus: peiqApiCircuitBreaker.getStatus(),
          })
        }
        throw error
      }

      const allArticles = PeiqApiWrapper.ArticleSearchService.flattenGroupedArticles(
        groupedResults,
        100,
      )

      const { valid: validArticles, invalidCount } = ArticleValidator.filterValidArticles(
        allArticles,
        collection.id.toString(),
      )

      TopArticlesLogger.logSearchResults({
        collection,
        allArticles,
        validArticles,
        invalidCount,
        startTime,
        groupedResults,
      })
      return validArticles
    } catch (error) {
      logger.error('Failed to find top articles', { error, collectionId: collection.id })
      throw new Error('Failed to search top articles')
    }
  }

  /**
   * Creates a new article collection in PEIQ.
   *
   * The created article includes:
   * - Hero image from the top-viewed article
   * - BBCode-formatted list of articles
   * - Dynamic tags from keywords_to_publish
   * - Custom category and location
   *
   * @param collection - Collection configuration
   * @param topArticles - Articles to include (can be empty)
   * @param testMode - Optional test configuration for draft articles
   * @returns Object with created article ID and URL
   * @throws Error if article creation fails
   */
  static async createTopArticlesCollection(
    collection: AutoContentCollection,
    topArticles: PeiqApiWrapperType.ArticleData[],
    testMode?: {
      title: string
      content: string
      status: 'draft' | 'published'
      userId: number
    },
  ): Promise<{ articleId: string; url: string | null }> {
    try {
      TopArticlesLogger.logCreationStart(collection, topArticles)

      // SAFETY: Use strict validation for article creation (addresses Christian's concern)
      const publishTime = PublishTimeCalculator.calculatePublishTimeForArticleCreation(collection)
      const collectionData = await this.buildCollectionData(collection, topArticles, publishTime)

      const articleParams = testMode
        ? ContentBuilder.buildTestModeParams(testMode, collectionData)
        : ContentBuilder.buildProductionParams(collectionData, topArticles, collection)

      // Pre-submission validation as per requirement
      if (!testMode) {
        const validationResult = ValidationService.validateCollectionData(collectionData, collection)
        
        if (!validationResult.isValid) {
          logger.error('Pre-submission validation failed', {
            collectionId: collection.id,
            errors: validationResult.errors,
            warnings: validationResult.warnings
          })
          throw new Error(`Article validation failed: ${validationResult.errors.join('; ')}`)
        }
        
        if (validationResult.warnings.length > 0) {
          logger.warn('Pre-submission validation warnings', {
            collectionId: collection.id,
            warnings: validationResult.warnings
          })
        }
      }

      const result = await this.createArticleViaPeiq(articleParams)

      let articleUrl: string | null = null
      try {
        articleUrl = await this.fetchArticleUrl(result.articleId?.toString() || '')
      } catch (urlError) {
        logger.warn('Failed to fetch article URL - article created but URL unknown', {
          articleId: result.articleId,
          error: urlError,
        })
      }

      try {
        await this.addHeroImageFromTopArticle(result.articleId?.toString() || '', topArticles)
      } catch (imageError) {
        logger.warn('Failed to add hero image - article created but without hero image', {
          articleId: result.articleId,
          error: imageError,
        })
      }

      TopArticlesLogger.logCreationSuccess(
        collection,
        result.articleId?.toString() || '',
        articleUrl,
        topArticles,
      )
      return {
        articleId: result.articleId?.toString() || '',
        url: articleUrl,
      }
    } catch (error) {
      logger.error('Failed to create top articles collection', {
        error,
        collectionId: collection.id,
        articleCount: topArticles.length,
      })
      throw new Error('Failed to create article collection')
    }
  }

  /**
   * Builds data structure for article creation.
   *
   * @param collection - Source collection configuration
   * @param topArticles - Articles to include
   * @param publishTime - Calculated publish time
   * @returns Structured data for PEIQ article creation
   */
  static async buildCollectionData(
    collection: AutoContentCollection,
    topArticles: PeiqApiWrapperType.ArticleData[],
    publishTime: Date,
  ): Promise<TopArticlesCollectionData> {
    const { regionName, regionId, categoryName } =
      await CollectionDataExtractor.extractCollectionData(collection)
    const userId = collection.user?.id
      ? parseInt(collection.user.id)
      : await DirectusService.getCurrentUserId()

    const title = ContentBuilder.generateTitle(collection, regionName, categoryName)
    const content =
      topArticles.length === 0
        ? ContentBuilder.generateFallbackContent()
        : ContentBuilder.buildContent(
            topArticles,
            Array.isArray(collection.grouping)
              ? collection.grouping[0]
              : collection.grouping || undefined,
          )

    const tags =
      ContentBuilder.parseKeywordsToPublish(
        collection.keywords_to_publish,
        collection,
        publishTime,
      ) || []

    // Map the first category name to PEIQ ID, fallback to Regionauten (1)
    const categoryId =
      collection.category && collection.category.length > 0
        ? CategoryMapper.mapCategoryNameWithFallback(collection.category[0])
        : CategoryMapper.getDefaultCategoryId()

    return {
      title,
      content,
      categoryId,
      locationId: regionId,
      publishedDate: publishTime.toISOString(),
      heroImageId: topArticles[0]?.hero_image?.id,
      tags,
      userId,
    }
  }

  /**
   * Creates article via PEIQ API with circuit breaker protection.
   *
   * @param articleParams - PEIQ article parameters
   * @returns Article creation result
   * @throws Error if creation fails or circuit breaker is open
   */
  private static async createArticleViaPeiq(articleParams: PeiqApiWrapperType.ArticleCreateParams) {
    let result: PeiqApiWrapperType.ArticleCreateResult
    try {
      result = await peiqApiCircuitBreaker.execute(async () =>
        PeiqApiWrapper.ArticleApi.createArticle(articleParams),
      )
    } catch (error) {
      if (error instanceof Error && error.message.includes('Circuit breaker')) {
        logger.error('PEIQ API Circuit breaker is OPEN - cannot create article', {
          circuitBreakerStatus: peiqApiCircuitBreaker.getStatus(),
        })
      }
      throw error
    }

    if (!result.success) {
      throw new Error(`PEIQ API Error: ${JSON.stringify(result.info)}`)
    }

    if (!result.articleId) {
      throw new Error('Article creation succeeded but no article ID returned')
    }

    return result
  }

  /**
   * Fetches the public URL of a created article.
   *
   * @param articleId - PEIQ article ID
   * @returns Article URL or null if not available
   */
  private static async fetchArticleUrl(articleId: string): Promise<string | null> {
    try {
      const articleDetails = await peiqApiCircuitBreaker.execute(async () =>
        PeiqApiWrapper.ArticleApi.fetchArticle(parseInt(articleId)),
      )

      if (articleDetails && articleDetails.url) {
        TopArticlesLogger.logArticleUrlRetrieved(articleId, articleDetails.url)
        return articleDetails.url
      } else {
        TopArticlesLogger.logArticleUrlError(articleId, articleDetails)
        return null
      }
    } catch (urlError) {
      TopArticlesLogger.logUrlFetchError(articleId, urlError)
      return null
    }
  }

  /**
   * Adds hero image from the top-viewed article.
   *
   * Per acceptance criteria: "Das Bild des Overall Top Beitrages wird das Heroimage"
   *
   * @param articleId - Target article ID
   * @param topArticles - Articles sorted by view count (first = highest)
   */
  private static async addHeroImageFromTopArticle(
    articleId: string,
    topArticles: PeiqApiWrapperType.ArticleData[],
  ): Promise<void> {
    if (topArticles[0]?.hero_image?.url) {
      try {
        await peiqApiCircuitBreaker.execute(async () =>
          PeiqApiWrapper.ArticleApi.addArticleImage(articleId, {
            url: topArticles[0].hero_image.url,
            caption: topArticles[0].hero_image.caption,
            copyright: topArticles[0].hero_image.copyright,
          }),
        )
        TopArticlesLogger.logHeroImageAdded(
          articleId,
          topArticles[0].id,
          topArticles[0].hero_image.url,
          topArticles[0].visit_count,
        )
      } catch (imageError) {
        TopArticlesLogger.logHeroImageError(articleId, imageError)
      }
    }
  }
}
