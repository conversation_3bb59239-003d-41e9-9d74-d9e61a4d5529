import { constants, logger, RmaError } from '@regionalmedienaustria/microservice-utils'
import { format } from 'date-fns'

import { CollectionExecutor } from '../helpers/collectionExecutor.js'
import { CollectionResultBuilder } from '../helpers/collectionResultBuilder.js'
import { CollectionValidator } from '../helpers/collectionValidator.js'
import { RecyclingErrorHandler } from '../helpers/recyclingErrorHandler.js'
import { PublishTimeCalculator } from '../utils/publishTimeCalculator.js'
import { RecyclingConfig } from '../utils/recyclingConfig.js'
import { RegionExtractor } from '../utils/regionExtractor.js'

import { DirectusService, type AutoContentCollection } from './directusService.js'
import { ScheduleService } from './scheduleService.js'
import { TopArticlesService } from './topArticlesService.js'

import type { PeiqApiWrapper as PeiqApiWrapperType } from '@regionalmedienaustria/peiq-api-wrapper'

export type CollectionResult = {
  collectionId: string
  sourceArticlesFound: number
  collectionsCreated: number
  collectionsSkipped: number
  collectionsFailed: number
  errors: string[]
  createdCollectionIds: string[]
  executionTime: number
}

export class RecyclingService {
  /**
   * Executes all scheduled content recycling collections.
   * 
   * This method orchestrates the entire recycling process:
   * 1. Fetches active collections from Directus
   * 2. Determines which collections are due for execution
   * 3. Processes collections in parallel for better performance
   * 4. Handles errors gracefully without stopping other collections
   * 
   * @returns Array of collection results with execution status and metrics
   * @throws Never - all errors are caught and returned as error results
   */
  static async executeScheduledCollections(): Promise<CollectionResult[]> {
    const startTime = Date.now()
    logger.info('Starting scheduled top articles collection execution')

    try {
      // Fetch active collections with error handling
      const fetchResult = await this.fetchActiveCollections(startTime)
      
      // Check if we got an error result instead of collections
      if (this.isCollectionResultArray(fetchResult)) {
        return fetchResult
      }
      
      const activeCollections = fetchResult
      if (activeCollections.length === 0) {
        return []
      }

      // Get collections due for execution
      const dueCollections = await this.getDueCollections(activeCollections)
      if (dueCollections.length === 0) {
        logger.info('No collections due for execution at this time')
        return []
      }

      // Process collections in parallel
      return await CollectionExecutor.processCollections(
        dueCollections, 
        (collection) => this.processCollection(collection)
      )

    } catch (error) {
      // Handle catastrophic failures
      return [RecyclingErrorHandler.createCatastrophicResult(error, Date.now() - startTime)]
    }
  }

  /**
   * Type guard to distinguish between collection data and error results.
   * 
   * @param result - Either collection data or error results
   * @returns True if result is an array of CollectionResult errors
   */
  private static isCollectionResultArray(result: AutoContentCollection[] | CollectionResult[]): result is CollectionResult[] {
    return result.length > 0 && 'collectionId' in result[0]
  }


  /**
   * Fetches all active collections from Directus CMS.
   * 
   * @param startTime - Process start timestamp for execution time tracking
   * @returns Active collections or error result if Directus is unavailable
   */
  private static async fetchActiveCollections(startTime: number): Promise<AutoContentCollection[] | CollectionResult[]> {
    try {
      const activeCollections = await DirectusService.getActiveCollections()
      
      if (activeCollections.length === 0) {
        logger.info('No active collections found')
        return []
      }

      logger.info(`Found ${activeCollections.length} active collections`)
      return activeCollections

    } catch (error) {
      logger.error('Failed to fetch active collections from Directus', { error })
      return [RecyclingErrorHandler.createDirectusFailureResult(error, Date.now() - startTime)]
    }
  }

  /**
   * Filters collections that are due for execution based on their schedules.
   * 
   * @param activeCollections - All active collections from Directus
   * @returns Collections that should be executed now
   */
  private static async getDueCollections(activeCollections: AutoContentCollection[]): Promise<AutoContentCollection[]> {
    try {
      const dueCollections = ScheduleService.getCollectionsDueForExecution(activeCollections)
      
      logger.info(`Found ${dueCollections.length} collections due for execution`, {
        collectionIds: dueCollections.map(c => c.id)
      })

      return dueCollections

    } catch (error) {
      logger.error('Failed to validate collection schedules - processing all active collections', { error })
      // Fallback: process all if validation fails
      return activeCollections
    }
  }


  /**
   * Processes a single collection through the entire recycling pipeline.
   * 
   * Pipeline steps:
   * 1. Validate collection configuration
   * 2. Check if recently executed
   * 3. Validate content type support
   * 4. Find top articles from PEIQ
   * 5. Validate article count (proceeds even with zero)
   * 6. Check publishing time window
   * 7. Create the collection article in PEIQ
   * 
   * @param collection - Collection configuration from Directus
   * @returns Result object with execution metrics and status
   */
  static async processCollection(collection: AutoContentCollection): Promise<CollectionResult> {
    const startTime = Date.now()
    logger.info('Processing collection for top articles', { collectionId: collection.id })

    try {
      const validationResult = this.validateCollectionBeforeProcessing(collection, startTime)
      if (validationResult) {
        return validationResult
      }

      const recentCheckResult = await this.checkRecentExecution(collection, startTime)
      if (recentCheckResult) {
        return recentCheckResult
      }

      const contentTypeResult = this.validateContentTypeSupport(collection)

      const topArticles = await this.findTopArticlesForCollection(collection)
      if (topArticles === null) {
        // null means error occurred, not just empty results
        return CollectionResultBuilder.createSkippedResult(collection.id, 'Error finding articles', startTime)
      }

      const articleValidationResult = this.validateArticleCount(collection, topArticles, contentTypeResult.warning)
      
      const publishWindowResult = await this.checkPublishingWindow(collection, startTime)
      if (publishWindowResult) {
        return publishWindowResult
      }

      return await this.createCollectionArticle(collection, topArticles, articleValidationResult.warnings, startTime)

    } catch (error) {
      return RecyclingErrorHandler.handleCollectionError(collection, error, 'collection-processing', Date.now() - startTime)
    }
  }


  /**
   * Validates collection configuration before processing.
   * 
   * @param collection - Collection to validate
   * @param startTime - Process start time for metrics
   * @returns Error result if invalid, null if valid
   */
  private static validateCollectionBeforeProcessing(collection: AutoContentCollection, startTime: number): CollectionResult | null {
    const validationError = CollectionValidator.validateCollection(collection)
    if (validationError) {
      return RecyclingErrorHandler.handleValidationError(collection, validationError, Date.now() - startTime)
    }
    return null
  }

  /**
   * Checks if collection was already executed within the configured time window.
   * 
   * @param collection - Collection to check
   * @param startTime - Process start time for metrics
   * @returns Skip result if recently executed, null otherwise
   */
  private static async checkRecentExecution(collection: AutoContentCollection, startTime: number): Promise<CollectionResult | null> {
    try {
      const recentWindow = RecyclingConfig.getRecentGenerationWindow()
      const recentlyGenerated = await DirectusService.getRecentlyGeneratedArticles(
        collection.id.toString(),
        { timeInPast: recentWindow }
      )
      
      if (recentlyGenerated.length > 0) {
        logger.info('Collection was already executed recently', { 
          collectionId: collection.id,
          lastExecution: recentlyGenerated[0].date_created 
        })
        return CollectionResultBuilder.createSkippedResult(
          collection.id, 
          'Recently executed', 
          startTime
        )
      }
      
      return null
    } catch (error) {
      logger.error('Failed to check recently generated articles - proceeding anyway', { 
        error, 
        collectionId: collection.id 
      })
      return null
    }
  }

  /**
   * Validates if the collection's content type is supported.
   * 
   * @param collection - Collection to validate
   * @returns Object with optional warning message
   */
  private static validateContentTypeSupport(collection: AutoContentCollection): { warning?: string } {
    const validation = CollectionValidator.validateContentType(collection)
    return { warning: validation.warning }
  }

  /**
   * Searches for top articles based on collection criteria.
   * 
   * @param collection - Collection configuration with search criteria
   * @param startTime - Process start time for metrics
   * @returns Array of articles (can be empty) or null on error
   */
  private static async findTopArticlesForCollection(collection: AutoContentCollection): Promise<PeiqApiWrapperType.ArticleData[] | null> {
    try {
      const topArticles = await TopArticlesService.findTopArticles(collection)
      
      logger.info('Found top articles for collection', {
        collectionId: collection.id,
        contentType: collection.content_type || 'article',
        articlesFound: topArticles.length,
      })
      
      // Return the array regardless of count - let downstream handle empty arrays
      return topArticles
    } catch (error) {
      logger.error('Failed to find top articles - skipping collection', { 
        error, 
        collectionId: collection.id 
      })
      return null
    }
  }

  /**
   * Validates article count against minimum threshold.
   * 
   * Note: Per acceptance criteria, collections proceed even with zero articles.
   * 
   * @param collection - Collection configuration
   * @param topArticles - Found articles
   * @param existingWarning - Any previous warning to include
   * @returns Warnings array (empty if no issues)
   */
  private static validateArticleCount(collection: AutoContentCollection, topArticles: PeiqApiWrapperType.ArticleData[], existingWarning?: string): { warnings: string[] } {
    const warnings: string[] = []
    
    if (existingWarning) {
      warnings.push(existingWarning)
    }

    const minThreshold = RecyclingConfig.getMinimumArticlesThreshold()
    const validation = CollectionValidator.validateMinimumArticles(topArticles.length, collection, minThreshold)
    
    if (validation.warning) {
      warnings.push(validation.warning)
    }

    return { warnings }
  }

  /**
   * Checks if current time is within the collection's publishing window.
   * 
   * @param collection - Collection with publish_time configuration
   * @param startTime - Process start time for metrics
   * @returns Skip result if outside window, null otherwise
   */
  private static async checkPublishingWindow(collection: AutoContentCollection, startTime: number): Promise<CollectionResult | null> {
    try {
      const publishTime = PublishTimeCalculator.calculatePublishTime(collection)
      const { ScheduleRules } = await import('../helpers/scheduleRules.js')
      const inPublishingWindow = ScheduleRules.validatePublishingWindow(collection, publishTime)
      
      if (!inPublishingWindow) {
        logger.debug('Collection publish time outside window', { 
          collectionId: collection.id,
          publishTime: format(publishTime, 'HH:mm')
        })
        return CollectionResultBuilder.createSkippedResult(
          collection.id, 
          'Outside publishing window', 
          startTime
        )
      }
      
      return null
    } catch (error) {
      logger.error('Failed to check publishing window - proceeding anyway', { 
        error, 
        collectionId: collection.id 
      })
      return null
    }
  }

  /**
   * Creates the final collection article in PEIQ.
   * 
   * @param collection - Collection configuration
   * @param topArticles - Articles to include in the collection
   * @param warnings - Any warnings from previous steps
   * @param startTime - Process start time for metrics
   * @returns Success or failure result with article details
   */
  private static async createCollectionArticle(
    collection: AutoContentCollection, 
    topArticles: PeiqApiWrapperType.ArticleData[], 
    warnings: string[], 
    startTime: number
  ): Promise<CollectionResult> {
    try {
      const creationResult = await TopArticlesService.createTopArticlesCollection(
        collection,
        topArticles
      )

      // Log success to Directus
      await this.logSuccessfulCreation(collection, creationResult, topArticles.length)

      const result = CollectionResultBuilder.createSuccessResult(
        collection.id,
        creationResult.articleId,
        topArticles.length,
        startTime
      )

      // Add any warnings
      if (warnings.length > 0) {
        result.errors.push(...warnings)
      }

      logger.info('Successfully created top articles collection', {
        collectionId: collection.id,
        peiqArticleId: creationResult.articleId,
        articleUrl: creationResult.url,
        sourceArticleCount: topArticles.length,
      })

      return result
    } catch (error) {
      return RecyclingErrorHandler.handleCollectionError(collection, error, 'article-creation', Date.now() - startTime)
    }
  }

  /**
   * Records successful article creation in Directus for tracking.
   * 
   * @param collection - Source collection configuration
   * @param creationResult - PEIQ article creation result
   * @param sourceArticleCount - Number of articles included
   */
  private static async logSuccessfulCreation(
    collection: AutoContentCollection, 
    creationResult: { articleId: string; url: string | null }, 
    sourceArticleCount: number
  ): Promise<void> {
    try {
      // Get location with proper validation
      const regionId = RegionExtractor.getValidRegionId(collection)
      
      if (regionId === null) {
        logger.error('Cannot log generated article - no valid location found', {
          collectionId: collection.id,
          articleId: creationResult.articleId
        })
        // Don't throw - article was created successfully, just can't log it
        return
      }
      
      await DirectusService.createGeneratedArticle({
        content_collection_id: collection.id,
        peiq_article_id: creationResult.articleId,
        date_created: new Date().toISOString(),
        region: regionId,
        url: creationResult.url,
        config_snapshot: {
          sourceArticleCount,
          grouping: collection.grouping,
          executionTimestamp: new Date().toISOString(),
          hasUrl: !!creationResult.url
        }
      })
    } catch (loggingError) {
      logger.error('Failed to log successful collection creation - article created but not tracked', { 
        loggingError, 
        collectionId: collection.id,
        createdArticleId: creationResult.articleId 
      })
    }
  }


  /**
   * Manually executes a specific collection by ID.
   * 
   * @param collectionId - Directus collection ID to execute
   * @returns Collection execution result
   * @throws RmaError if collection not found or execution fails
   */
  static async executeManualCollection(collectionId: string): Promise<CollectionResult> {
    logger.info('Executing manual top articles collection', { collectionId })

    try {
      const collection = await DirectusService.getCollectionById(collectionId)
      
      if (!collection) {
        throw new RmaError({
          error: new Error('Collection not found'),
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND,
        })
      }

      const result = await this.processCollection(collection)
      
      logger.info('Completed manual collection execution', { 
        collectionId,
        result: {
          created: result.collectionsCreated,
          failed: result.collectionsFailed,
          executionTime: result.executionTime,
        }
      })

      return result
    } catch (error) {
      logger.error('Failed to execute manual collection', { error, collectionId })
      
      if (error instanceof RmaError) {
        throw error
      }
      
      throw new RmaError({
        error: error instanceof Error ? error : new Error('Failed to execute manual collection'),
        statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
      })
    }
  }



  /**
   * @deprecated Use CollectionValidator.validateCollection instead
   */
  static validateCollectionData(collection: AutoContentCollection): string | null {
    return CollectionValidator.validateCollection(collection)
  }

  /**
   * @deprecated Use RecyclingConfig.getMinimumArticlesThreshold instead
   */
  static getMinimumArticlesThreshold(): number {
    return RecyclingConfig.getMinimumArticlesThreshold()
  }


  /**
   * Tests article search functionality with custom parameters.
   * 
   * @param params - Search parameters
   * @param params.keywords - Keywords to search for
   * @param params.grouping - Grouping strategy
   * @param params.categoryIds - Category IDs to filter by
   * @param params.locationIds - Location IDs to filter by
   * @param params.dateFrom - Start date filter (ISO format)
   * @param params.dateTo - End date filter (ISO format)
   * @param params.sortBy - Sort field
   * @param params.sortDir - Sort direction
   * @param params.maxPerGroup - Max articles per group
   * @param params.maxTotal - Max total articles
   * @returns Found articles
   * @throws RmaError on search failure
   */
  static async testTopArticlesSearch(params: {
    keywords: string[]
    grouping: 'district' | 'category' | 'category_per_district'
    categoryIds: string[]
    locationIds: string[]
    dateFrom?: string
    dateTo?: string
    sortBy?: string
    sortDir?: string
    maxPerGroup?: number
    maxTotal?: number
  }): Promise<PeiqApiWrapperType.ArticleData[]> {
    try {
      // Build collection criteria with all optional parameters
      const collectionCriteria: Record<string, string | number> = {}
      
      if (params.dateFrom) {collectionCriteria.dateStart = params.dateFrom}
      if (params.dateTo) {collectionCriteria.dateEnd = params.dateTo}
      if (params.sortBy) {collectionCriteria.sortBy = params.sortBy}
      if (params.sortDir) {collectionCriteria.sortDir = params.sortDir}
      if (params.maxPerGroup !== undefined) {collectionCriteria.maxPerGroup = params.maxPerGroup}
      if (params.maxTotal !== undefined) {collectionCriteria.maxTotal = params.maxTotal}

      const testCollection: AutoContentCollection = {
        id: 1,
        status: 'published',
        title: 'Test Collection',
        // Map locationIds to Junction Collection format
        region: params.locationIds.map(locationId => ({
          auto_content_collection_id: 1,
          id: Date.now() + Math.random(), // Temporary ID for junction
          trees_id: parseInt(locationId)
        })),
        category: params.categoryIds,
        grouping: [params.grouping],
        keywords: params.keywords.join(','),
        content: 'Test content',
        group: 'test',
        time_in_past: 24,
        // Pass all search parameters through collection_criteria
        collection_criteria: Object.keys(collectionCriteria).length > 0 
          ? JSON.stringify(collectionCriteria)
          : null
      }

      return await TopArticlesService.findTopArticles(testCollection)
    } catch (error) {
      logger.error('Failed to test top articles search', { error, params })
      throw new RmaError({
        error: error instanceof Error ? error : new Error('Failed to test article search'),
        statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
      })
    }
  }

  /**
   * Tests article creation functionality in DRAFT mode.
   * 
   * @param params - Creation parameters
   * @param params.title - Article title
   * @param params.content - Article content
   * @param params.categoryId - Target category ID
   * @param params.locationId - Target location ID
   * @param params.userId - Author user ID
   * @param params.tags - Optional tags
   * @returns Created article ID
   * @throws RmaError on creation failure
   */
  static async testTopArticlesCreation(params: {
    title: string
    content: string
    categoryId: number
    locationId: number
    userId: number
    tags?: string[]
  }): Promise<string> {
    try {
      logger.info('Creating test article in DRAFT mode using TopArticlesService', { params })

      const testCollection: AutoContentCollection = {
        id: Date.now(),
        status: 'published',
        title: 'Test Collection',
        region: [],
        category: [params.categoryId.toString()],
        grouping: ['district'],
        keywords: 'test',
        content: 'Test content',
        group: 'test',
        time_in_past: 24
      }

      const testArticles = [{
        id: `test-source-${Date.now()}`,
        title: 'Test Source Article',
        visit_count: '100',
        like_count: '10',
        comment_count: '5',
        category_id: params.categoryId,
        location_id: params.locationId,
        published: new Date().toISOString(),
        created: new Date().toISOString(),
        text_elements: {
          subline: 'Test Subline',
          kicker: 'Test Kicker', 
          text: '<p>Test article content for recycling service testing.</p>',
          teaser: 'Test article content'
        }
      }] as PeiqApiWrapperType.ArticleData[]

      const creationResult = await TopArticlesService.createTopArticlesCollection(
        testCollection, 
        testArticles,
        {
          title: params.title,
          content: params.content,
          status: 'draft',
          userId: params.userId
        }
      )
      
      const articleId = creationResult.articleId
      
      logger.info('Successfully created test article in DRAFT mode via TopArticlesService', {
        articleId,
        testParams: params,
        status: 'draft'
      })

      return articleId
    } catch (error) {
      logger.error('Failed to test article creation in DRAFT mode', { error, params })
      throw new RmaError({
        error: error instanceof Error ? error : new Error('Failed to test article creation'),
        statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
      })
    }
  }
}