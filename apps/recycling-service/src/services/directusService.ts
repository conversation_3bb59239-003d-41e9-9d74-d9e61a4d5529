import { Cache } from '@regionalmedienaustria/cache-util'
import { logger } from '@regionalmedienaustria/microservice-utils'
import {
  readMe,
  readItems,
  createItem,
  readField,
  type AutoContentCollection,
  type GeneratedArticles,
} from '@rma-mono/directus-client'

import { recyclingClient } from '@/utils/directus/client.js'

import { getRecyclingCache } from '../cache/recyclingCache.js'
import { directusCircuitBreaker } from '../utils/circuitBreaker.js'

import { CacheService } from './cacheService.js'

import type { DirectusUser } from '../types/directusTypes.js'

export type { AutoContentCollection, GeneratedArticles }

/**
 * Service for interacting with Directus CMS.
 * Handles all content recycling related data operations.
 */
export class DirectusService {
  /**
   * Fetches all active (published) content collections.
   *
   * @param props - Options
   * @param props.bypassCache - Force fresh fetch from Directus
   * @returns Array of active collections
   * @throws Error if Directus is unavailable
   */
  static async getActiveCollections(
    props: { bypassCache?: boolean } = {},
  ): Promise<AutoContentCollection[]> {
    try {
      const cache = await getRecyclingCache()

      const collections = await cache.getWithFunction({
        key: 'recycling-active-collections',
        storeFunction: async () => {
          logger.debug('Cache miss: Fetching active collections from Directus')
          try {
            const response = await directusCircuitBreaker.execute(async () =>
              recyclingClient.request(
                readItems('auto_content_collection', {
                  filter: {
                    status: { _eq: 'published' },
                  },
                }),
              ),
            )
            return response || []
          } catch (error) {
            if (error instanceof Error && error.message.includes('Circuit breaker')) {
              logger.error('Directus Circuit breaker is OPEN - cannot fetch collections', {
                circuitBreakerStatus: directusCircuitBreaker.getStatus(),
              })
            }
            throw error
          }
        },
        ttl: Cache.CONSTANTS.ONE_HOUR,
        staleWhileRevalidate: true,
        force: props.bypassCache,
      })

      return collections
    } catch (error) {
      logger.error('Failed to fetch active collections from Directus', { error })
      throw new Error('Failed to fetch active collections from Directus')
    }
  }

  /**
   * Fetches a specific collection by ID.
   *
   * @param id - Collection ID to fetch
   * @param props - Options
   * @param props.bypassCache - Force fresh fetch from Directus
   * @returns Collection data or null if not found
   * @throws Error if Directus is unavailable
   */
  static async getCollectionById(
    id: string,
    props: { bypassCache?: boolean } = {},
  ): Promise<AutoContentCollection | null> {
    try {
      const cache = await getRecyclingCache()

      const collection = await cache.getWithFunction({
        key: `recycling-collection-${id}`,
        storeFunction: async () => {
          logger.debug('Cache miss: Fetching collection by ID from Directus', { id })
          const response = await recyclingClient.request(
            readItems('auto_content_collection', {
              filter: { id: { _eq: parseInt(id) } },
            }),
          )
          return response?.[0] || null
        },
        ttl: Cache.CONSTANTS.ONE_HOUR,
        staleWhileRevalidate: true,
        force: props.bypassCache,
      })

      return collection
    } catch (error) {
      logger.error('Failed to fetch collection by ID from Directus', { error, id })
      throw new Error('Failed to fetch collection by ID from Directus')
    }
  }

  /**
   * Creates a record of a generated article in Directus.
   *
   * This tracks which articles were created by the recycling service.
   *
   * @param data - Article data to store
   * @param data.content_collection_id - Source collection ID
   * @param data.peiq_article_id - Created PEIQ article ID
   * @param data.date_created - Creation timestamp
   * @param data.region - Target location ID
   * @param data.url - Public article URL
   * @param data.config_snapshot - Collection configuration at creation time
   * @returns Created record
   * @throws Error if creation fails
   */
  static async createGeneratedArticle(
    data: Omit<GeneratedArticles, 'id'>,
  ): Promise<GeneratedArticles> {
    try {
      const articleData: Omit<GeneratedArticles, 'id'> = {
        config_snapshot: data.config_snapshot,
        content_collection_id:
          typeof data.content_collection_id === 'number'
            ? data.content_collection_id
            : typeof data.content_collection_id === 'object' &&
              data.content_collection_id !== null &&
              'id' in data.content_collection_id
            ? Number(data.content_collection_id.id)
            : Number(data.content_collection_id),
        date_created: data.date_created,
        region: data.region,
        peiq_article_id: data.peiq_article_id,
        url: data.url,
      }

      const response = await directusCircuitBreaker.execute(async () =>
        recyclingClient.request(createItem('generated_articles', articleData)),
      )

      const collectionId =
        typeof data.content_collection_id === 'number'
          ? data.content_collection_id
          : typeof data.content_collection_id === 'object' && data.content_collection_id !== null
          ? typeof data.content_collection_id.id === 'number'
            ? data.content_collection_id.id
            : parseInt(String(data.content_collection_id.id))
          : parseInt(String(data.content_collection_id))
      await CacheService.invalidateArticleCreation(collectionId)

      logger.info('Created generated article record', { data })
      return response
    } catch (error) {
      logger.error('Failed to create generated article in Directus', { error, data })
      throw new Error('Failed to create generated article in Directus')
    }
  }

  /**
   * Fetches articles generated by a specific collection.
   *
   * @param collectionId - Collection ID to query
   * @param props - Options
   * @param props.limit - Maximum articles to return (default: 100)
   * @param props.bypassCache - Force fresh fetch
   * @returns Array of generated articles sorted by date (newest first)
   * @throws Error if query fails
   */
  static async getGeneratedArticlesByCollection(
    collectionId: string,
    props: { limit?: number; bypassCache?: boolean } = {},
  ): Promise<GeneratedArticles[]> {
    try {
      const cache = await getRecyclingCache()

      const limit = props.limit ?? 100

      const articles = await cache.getWithFunction({
        key: `recycling-articles-${collectionId}-${limit}`,
        storeFunction: async () => {
          logger.debug('Cache miss: Fetching generated articles from Directus', {
            collectionId,
            limit,
          })
          const response = await recyclingClient.request(
            readItems('generated_articles', {
              filter: { content_collection_id: { _eq: collectionId } },
              sort: ['-date_created'],
              limit,
            }),
          )
          return response || []
        },
        ttl: Cache.CONSTANTS.ONE_HOUR * 2,
        staleWhileRevalidate: true,
        force: props.bypassCache,
      })

      return articles
    } catch (error) {
      logger.error('Failed to fetch generated articles from Directus', {
        error,
        collectionId,
        limit: props.limit ?? 100,
      })
      throw new Error('Failed to fetch generated articles from Directus')
    }
  }

  /**
   * Fetches recently generated articles within a time window.
   *
   * Used to check if a collection was already executed recently.
   *
   * @param collectionId - Collection ID to query
   * @param props - Options
   * @param props.timeInPast - Hours to look back (default: 24)
   * @param props.bypassCache - Force fresh fetch
   * @returns Array of recent articles
   * @throws Error if query fails
   */
  static async getRecentlyGeneratedArticles(
    collectionId: string,
    props: { timeInPast?: number; bypassCache?: boolean } = {},
  ): Promise<GeneratedArticles[]> {
    try {
      const cache = await getRecyclingCache()

      const timeInPast = props.timeInPast ?? 24

      const articles = await cache.getWithFunction({
        key: `recycling-recent-${collectionId}-${timeInPast}h`,
        storeFunction: async () => {
          logger.debug('Cache miss: Fetching recent generated articles from Directus', {
            collectionId,
            timeInPast,
          })
          const timeThreshold = new Date(Date.now() - timeInPast * 60 * 60 * 1000).toISOString()

          const response = await recyclingClient.request(
            readItems('generated_articles', {
              filter: {
                _and: [
                  { content_collection_id: { _eq: collectionId } },
                  { date_created: { _gte: timeThreshold } as any }, // Directus SDK type issue - _gte should be valid for date fields
                ],
              },
              sort: ['-date_created'],
              limit: 100,
            }),
          )
          return response || []
        },
        ttl: Cache.CONSTANTS.ONE_HOUR / 2,
        staleWhileRevalidate: true,
        force: props.bypassCache,
      })

      return articles
    } catch (error) {
      logger.error('Failed to fetch recently generated articles from Directus', {
        error,
        collectionId,
        timeInPast: props.timeInPast ?? 24,
      })
      throw new Error('Failed to fetch recently generated articles from Directus')
    }
  }

  /**
   * Fetches a collection by ID with guaranteed result.
   *
   * @param id - Collection ID
   * @param props - Options
   * @param props.bypassCache - Force fresh fetch
   * @returns Collection data
   * @throws Error if collection not found or query fails
   */
  static async getAutoContentCollection(
    id: string,
    props: { bypassCache?: boolean } = {},
  ): Promise<AutoContentCollection> {
    try {
      const collection = await this.getCollectionById(id, props)

      if (!collection) {
        throw new Error(`Auto content collection with ID ${id} not found`)
      }

      return collection
    } catch (error) {
      logger.error('Failed to get auto content collection from Directus', { error, id })
      throw error instanceof Error ? error : new Error('Failed to get auto content collection')
    }
  }

  /**
   * Gets the current authenticated Directus user.
   *
   * This is the "ContentHub defined user" used for automated content creation.
   * Authenticated via DIRECTUS_RECYCLING_TOKEN.
   *
   * @param props - Options
   * @param props.bypassCache - Force fresh fetch
   * @returns User object with ID and metadata
   * @throws Error if authentication fails
   */
  static async getCurrentUser(props: { bypassCache?: boolean } = {}): Promise<DirectusUser> {
    try {
      const cache = await getRecyclingCache()

      const user = (await cache.getWithFunction({
        key: 'recycling-current-user',
        storeFunction: async () => {
          logger.debug('Cache miss: Fetching current user from Directus')
          const response = await recyclingClient.request(readMe())
          return response as DirectusUser
        },
        ttl: Cache.CONSTANTS.ONE_HOUR * 4,
        staleWhileRevalidate: true,
        force: props.bypassCache,
      })) as DirectusUser

      return user
    } catch (error) {
      logger.error('Failed to fetch current user from Directus', { error })
      throw new Error('Failed to fetch current user from Directus')
    }
  }

  /**
   * Gets the current user's ID as a number.
   *
   * Convenience method for PEIQ API which requires numeric user IDs.
   *
   * @param props - Options
   * @param props.bypassCache - Force fresh fetch
   * @returns Numeric user ID
   * @throws Error if user ID is invalid or fetch fails
   */
  static async getCurrentUserId(props: { bypassCache?: boolean } = {}): Promise<number> {
    try {
      const user = await this.getCurrentUser(props)
      const userId = parseInt(user.id, 10)

      if (isNaN(userId)) {
        throw new Error(`Invalid user ID from Directus: ${user.id}`)
      }

      logger.debug('Retrieved ContentHub user ID for content creation', { userId })
      return userId
    } catch (error) {
      logger.error('Failed to get current user ID from Directus', { error })
      throw error instanceof Error ? error : new Error('Failed to get current user ID')
    }
  }

  /**
   * Fetches field metadata for a specific field in a collection.
   *
   * Used to get field options, choices, and other metadata dynamically.
   *
   * @param collection - Collection name
   * @param field - Field name
   * @returns Field metadata including options and choices
   * @throws Error if field metadata cannot be fetched
   */
  static async getFieldMetadata(collection: string, field: string): Promise<any> {
    try {
      const cache = await getRecyclingCache()

      const fieldData = await cache.getWithFunction({
        key: `field-metadata-${collection}-${field}`,
        storeFunction: async () => {
          logger.debug('Fetching field metadata from Directus', {
            collection,
            field,
          })
          try {
            const response = await directusCircuitBreaker.execute(async () =>
              recyclingClient.request(readField(collection, field)),
            )
            return response
          } catch (error) {
            logger.error('Failed to fetch field metadata from Directus', {
              error,
              collection,
              field,
            })
            throw error
          }
        },
        ttl: Cache.CONSTANTS.ONE_DAY, // Field metadata rarely changes
        staleWhileRevalidate: true,
      })

      return fieldData
    } catch (error) {
      logger.error('Failed to get field metadata', {
        error,
        collection,
        field,
      })
      throw new Error(`Failed to get field metadata for ${collection}.${field}`)
    }
  }
}
