import { logger } from '@regionalmedienaustria/microservice-utils'

import type { AutoContentCollection } from '../services/directusService.js'
import type { PeiqApiWrapper as PeiqApiWrapperType } from '@regionalmedienaustria/peiq-api-wrapper'

/**
 * Helper for logging TopArticles operations
 * Centralized logging logic for search and creation operations
 */
export class TopArticlesLogger {
  /**
   * Logs the initiation of a PEIQ API search for a given collection.
   */
  static logSearchStart(collection: AutoContentCollection): void {
    logger.info('Searching for top articles using PEIQ API', {
      collectionId: collection.id,
      regions: collection.region?.map((r) => r.trees_id) || [],
      categories: collection.category || [],
      grouping: collection.grouping,
    })
  }

  /**
   * Log search results with performance monitoring
   */
  static logSearchResults({
    collection,
    allArticles,
    validArticles,
    invalidCount,
    startTime,
    groupedResults,
  }: {
    collection: AutoContentCollection
    allArticles: PeiqApiWrapperType.ArticleData[]
    validArticles: PeiqApiWrapperType.ArticleData[]
    invalidCount: number
    startTime: number
    groupedResults: Record<string, unknown>
  }): void {
    const executionTime = Date.now() - startTime
    logger.info('Successfully found top articles', {
      collectionId: collection.id,
      totalFound: allArticles.length,
      validArticles: validArticles.length,
      invalidFiltered: invalidCount,
      groupCount: Object.keys(groupedResults).length,
      executionTimeMs: executionTime,
    })

    // PERFORMANCE: Warn if API call is slow
    if (executionTime > 5000) {
      logger.warn('Slow PEIQ API response detected', {
        collectionId: collection.id,
        executionTimeMs: executionTime,
      })
    }
  }

  /**
   * Logs the start of the article creation process.
   * Includes a specific notice if no source articles were found and fallback content will be used.
   */
  static logCreationStart(
    collection: AutoContentCollection,
    topArticles: PeiqApiWrapperType.ArticleData[],
  ): void {
    logger.info('Creating top articles collection', {
      collectionId: collection.id,
      articleCount: topArticles.length,
    })

    if (topArticles.length === 0) {
      logger.info(
        'No articles found for collection, creating with fallback content as per acceptance criteria',
        {
          collectionId: collection.id,
        },
      )
    }
  }

  /**
   * Logs the successful creation of a new collection article in PEIQ.
   */
  static logCreationSuccess(
    collection: AutoContentCollection,
    articleId: string,
    articleUrl: string | null,
    topArticles: PeiqApiWrapperType.ArticleData[],
  ): void {
    logger.info('Successfully created top articles collection', {
      collectionId: collection.id,
      articleId,
      articleUrl,
      articleCount: topArticles.length,
    })
  }

  /**
   * Logs the successful retrieval of a created article's public URL.
   */
  static logArticleUrlRetrieved(articleId: string, url: string): void {
    logger.info('Retrieved article URL from PEIQ API', {
      articleId,
      url,
    })
  }

  /**
   * Logs an error where the PEIQ API returns details but no URL.
   */
  static logArticleUrlError(articleId: string, articleDetails?: unknown): void {
    logger.error('PEIQ API returned article details but no URL', {
      articleId,
      articleDetails,
    })
  }

  /**
   * Logs a general failure when fetching an article URL.
   */
  static logUrlFetchError(articleId: string, urlError: unknown): void {
    logger.error('Failed to retrieve article URL from PEIQ API', {
      articleId,
      urlError,
    })
  }

  /**
   * Logs the successful addition of a hero image, noting the specific acceptance criterion.
   */
  static logHeroImageAdded(
    articleId: string,
    sourceArticleId: string,
    sourceImage: string,
    topArticleViewCount?: string,
  ): void {
    logger.info('Added hero image from overall top article (acceptance criteria)', {
      articleId,
      sourceArticleId,
      sourceImage,
      topArticleViewCount,
    })
  }

  /**
   * Logs a non-critical failure where the article was created but adding the hero image failed.
   */
  static logHeroImageError(articleId: string, imageError: unknown): void {
    logger.warn('Failed to add hero image but article created successfully', {
      articleId,
      imageError,
    })
  }
}
