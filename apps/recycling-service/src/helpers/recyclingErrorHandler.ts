import { logger } from '@regionalmedienaustria/microservice-utils'

import { DirectusService } from '../services/directusService.js'
import { ErrorFormatter } from '../utils/errorFormatter.js'
import { RegionExtractor } from '../utils/regionExtractor.js'

import type { AutoContentCollection } from '../services/directusService.js'
import type { CollectionResult } from '../services/recyclingService.js'

/**
 * Centralized error handling for recycling service operations
 * Provides consistent error logging and result creation
 */
export class RecyclingErrorHandler {
  /**
   * Create a failed collection result with enhanced error context
   */
  static createFailedCollectionResult(
    collectionId: string,
    error: unknown,
    executionTime: number,
  ): CollectionResult {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'

    const detailedError = this.formatDetailedError(error, errorMessage)

    return {
      collectionId,
      sourceArticlesFound: 0,
      collectionsCreated: 0,
      collectionsSkipped: 0,
      collectionsFailed: 1,
      errors: [detailedError],
      createdCollectionIds: [],
      executionTime,
    }
  }

  /**
   * Format error with additional context for better debugging
   */
  private static formatDetailedError(error: unknown, errorMessage: string): string {
    const formatted = ErrorFormatter.formatError(error)

    const statusCode =
      error instanceof Error && 'statusCode' in error
        ? (error as Error & { statusCode?: number }).statusCode
        : undefined

    return `[${formatted.timestamp}] ${formatted.type}: ${errorMessage}${
      statusCode ? ` (HTTP ${statusCode})` : ''
    }${formatted.retryable ? ' [RETRYABLE]' : ''}`
  }

  /**
   * Handle and log collection processing errors
   */
  static async handleCollectionError(
    collection: AutoContentCollection,
    error: unknown,
    phase: string,
    executionTime: number,
  ): Promise<CollectionResult> {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'

    logger.error(`Failed during ${phase}`, {
      error,
      collectionId: collection.id,
      phase,
    })

    // Try to log the failed execution to Directus
    await this.tryLogFailure(collection, errorMessage, phase)

    return this.createFailedCollectionResult(collection.id.toString(), error, executionTime)
  }

  /**
   * Handle catastrophic service failures
   */
  static createCatastrophicResult(error: unknown, executionTime: number): CollectionResult {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'

    logger.error('Catastrophic failure in recycling service', { error })

    return {
      collectionId: 'catastrophic-error',
      sourceArticlesFound: 0,
      collectionsCreated: 0,
      collectionsSkipped: 0,
      collectionsFailed: 1,
      errors: [`Catastrophic failure: ${errorMessage}`],
      createdCollectionIds: [],
      executionTime,
    }
  }

  /**
   * Handle Directus service failures
   */
  static createDirectusFailureResult(error: unknown, executionTime: number): CollectionResult {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'

    logger.error('Failed to fetch active collections from Directus', { error })

    return {
      collectionId: 'directus-error',
      sourceArticlesFound: 0,
      collectionsCreated: 0,
      collectionsSkipped: 0,
      collectionsFailed: 1,
      errors: [`Failed to fetch collections: ${errorMessage}`],
      createdCollectionIds: [],
      executionTime,
    }
  }

  /**
   * Try to log failure to Directus (best effort)
   * Uses a special error ID format to distinguish from successful articles
   */
  private static async tryLogFailure(
    collection: AutoContentCollection,
    errorMessage: string,
    phase: string,
  ): Promise<void> {
    try {
      // Use special error ID format: ERROR_[timestamp]_[collectionId]
      const errorId = `ERROR_${Date.now()}_${collection.id}`

      // Get location with same logic as successful articles
      const locationId = RegionExtractor.getValidRegionId(collection)

      if (locationId === null) {
        logger.warn('Cannot log error to Directus - no valid location found', {
          collectionId: collection.id,
          errorMessage,
        })
        return
      }

      await DirectusService.createGeneratedArticle({
        content_collection_id: collection.id,
        peiq_article_id: errorId, // Special format for errors
        date_created: new Date().toISOString(),
        region: locationId,
        url: undefined,
        config_snapshot: {
          status: 'error',
          error: errorMessage,
          errorCode: this.categorizeError(errorMessage),
          timestamp: new Date().toISOString(),
          phase,
          retryable: this.isRetryableError(errorMessage),
        },
      })
    } catch (loggingError) {
      logger.error('Failed to log failure to Directus', {
        loggingError,
        originalError: errorMessage,
        collectionId: collection.id,
        phase,
      })
      // Even logging failures don't break the process
    }
  }

  /**
   * Categorize errors for better tracking and analysis
   */
  private static categorizeError(errorMessage: string): string {
    if (errorMessage.includes('PEIQ API') || errorMessage.includes('Rate limit')) {
      return 'EXTERNAL_API_ERROR'
    }
    if (errorMessage.includes('validation') || errorMessage.includes('Invalid')) {
      return 'VALIDATION_ERROR'
    }
    if (errorMessage.includes('timeout') || errorMessage.includes('ETIMEDOUT')) {
      return 'TIMEOUT_ERROR'
    }
    if (errorMessage.includes('Network') || errorMessage.includes('ECONNREFUSED')) {
      return 'NETWORK_ERROR'
    }
    return 'UNKNOWN_ERROR'
  }

  /**
   * Determine if an error is retryable
   */
  private static isRetryableError(errorMessage: string): boolean {
    const retryablePatterns = [
      'timeout',
      'ETIMEDOUT',
      'ECONNREFUSED',
      'Rate limit',
      'Network error',
      '503',
      '502',
      '504',
    ]

    return retryablePatterns.some((pattern) =>
      errorMessage.toLowerCase().includes(pattern.toLowerCase()),
    )
  }

  /**
   * Handle validation errors with detailed logging
   */
  static handleValidationError(
    collection: AutoContentCollection,
    validationError: string,
    executionTime: number,
  ): CollectionResult {
    logger.error('Collection validation failed', {
      collectionId: collection.id,
      validationError,
    })

    return {
      collectionId: collection.id.toString(),
      sourceArticlesFound: 0,
      collectionsCreated: 0,
      collectionsSkipped: 1,
      collectionsFailed: 0,
      errors: [`Invalid collection data: ${validationError}`],
      createdCollectionIds: [],
      executionTime,
    }
  }

  /**
   * Handle errors with optional error context
   */
  static withErrorContext<T>(
    operation: string,
    collectionId?: string | number,
  ): (fn: () => Promise<T>) => Promise<T> {
    return async (fn: () => Promise<T>): Promise<T> => {
      try {
        return await fn()
      } catch (error) {
        logger.error(`Failed during ${operation}`, {
          error,
          collectionId,
          operation,
        })
        throw error
      }
    }
  }
}
