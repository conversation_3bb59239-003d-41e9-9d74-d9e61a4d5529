import { CategoryMapper } from '../utils/categoryMapper.js'
import { CollectionDataExtractor } from '../utils/collectionDataExtractor.js'

import type { AutoContentCollection } from '../services/directusService.js'
import type { TopArticlesCollectionData } from '../services/topArticlesService.js'
import type { PeiqApiWrapper as PeiqApiWrapperType } from '@regionalmedienaustria/peiq-api-wrapper'

/**
 * Helper for building article collection content
 * Handles HTML generation, article grouping logic, and PEIQ API parameter building
 */
export class ContentBuilder {
  /**
   * Generate title for collection based on grouping strategy
   */
  static generateTitle(
    collection: AutoContentCollection,
    regionName: string,
    categoryName: string,
  ): string {
    const timeFrameText =
      collection.time_in_past && collection.time_in_past <= 24
        ? 'heute'
        : `der letzten ${Math.ceil((collection.time_in_past || 24) / 24)} Tage`

    const groupingValue = Array.isArray(collection.grouping)
      ? collection.grouping[0]
      : collection.grouping
    switch (groupingValue?.toLowerCase()) {
      case 'category':
        return `Die Top ${categoryName}-Beiträge von ${timeFrameText}`
      case 'district':
        return `Die Top ${categoryName}-Beiträge aus ${regionName} von ${timeFrameText}`
      default:
        return `Die Top Beiträge aus ${regionName} von ${timeFrameText}`
    }
  }

  /**
   * Generate teaser text for collection
   * Note: This method exists for backward compatibility but is not used in production code
   */
  static generateTeaser(collection: AutoContentCollection, articleCount: number): string {
    // Since this is only used in tests, we'll use hardcoded values for now
    const regionName = 'Region'
    const categoryName = 'Kategorie'

    const groupingValue = Array.isArray(collection.grouping)
      ? collection.grouping[0]
      : collection.grouping
    switch (groupingValue?.toLowerCase()) {
      case 'category':
        return `Die ${articleCount} meistgelesenen Artikel zum Thema ${categoryName}`
      case 'district':
        return `Die ${articleCount} beliebtesten ${categoryName}-Artikel aus ${regionName}`
      default:
        return `Die ${articleCount} beliebtesten Artikel aus ${regionName} zu lokalen Nachrichten`
    }
  }

  /**
   * Generate fallback content when no articles are found
   */
  static generateFallbackContent(): string {
    return '<p>Derzeit sind keine passenden Artikel verfügbar.</p>'
  }

  /**
   * Build content based on grouping strategy
   */
  static buildContent(articles: PeiqApiWrapperType.ArticleData[], grouping?: string): string {
    if (articles.length === 0) {
      return '<p>Keine Artikel gefunden.</p>'
    }

    switch (grouping) {
      case 'district':
        return this.buildDistrictGroupedContent(articles)
      case 'category':
        return this.buildCategoryGroupedContent(articles)
      default:
        return this.buildDistrictGroupedContent(articles)
    }
  }

  /**
   * Build content grouped by district/region
   */
  private static buildDistrictGroupedContent(articles: PeiqApiWrapperType.ArticleData[]): string {
    const groupedByRegion = this.groupByRegion(articles)
    let content = ''

    for (const [regionName, regionArticles] of Object.entries(groupedByRegion)) {
      content += `<h2>Die Top Beiträge aus ${regionName}</h2>\n`
      content += this.buildArticleList(regionArticles)
    }

    return content
  }

  /**
   * Build content grouped by category
   */
  private static buildCategoryGroupedContent(articles: PeiqApiWrapperType.ArticleData[]): string {
    const groupedByCategory = this.groupByCategory(articles)
    let content = ''

    for (const [categoryName, categoryArticles] of Object.entries(groupedByCategory)) {
      content += `<h2>Die Top Beiträge: ${categoryName}</h2>\n`
      content += this.buildArticleList(categoryArticles)
    }

    return content
  }

  /**
   * Group articles by region
   */
  private static groupByRegion(
    articles: PeiqApiWrapperType.ArticleData[],
  ): Record<string, PeiqApiWrapperType.ArticleData[]> {
    return articles.reduce((groups, article) => {
      const regionKey = `Region ${article.location_id}`
      if (!groups[regionKey]) {
        groups[regionKey] = []
      }
      groups[regionKey].push(article)
      return groups
    }, {} as Record<string, PeiqApiWrapperType.ArticleData[]>)
  }

  /**
   * Group articles by category
   */
  private static groupByCategory(
    articles: PeiqApiWrapperType.ArticleData[],
  ): Record<string, PeiqApiWrapperType.ArticleData[]> {
    return articles.reduce((groups, article) => {
      const categoryKey = `Kategorie ${article.category_id}`
      if (!groups[categoryKey]) {
        groups[categoryKey] = []
      }
      groups[categoryKey].push(article)
      return groups
    }, {} as Record<string, PeiqApiWrapperType.ArticleData[]>)
  }

  /**
   * Build BBCode list for articles (based on meinbezirk.at requirements)
   * Format: [earticle=ID layout=article-list-no-text]Title[/earticle]
   */
  static buildArticleList(articles: PeiqApiWrapperType.ArticleData[]): string {
    let bbcodeContent = ''

    for (const article of articles) {
      bbcodeContent += `[earticle=${article.id} layout=article-list-no-text]${article.title}[/earticle]\n`
    }

    return bbcodeContent
  }

  /**
   * Build PEIQ API parameters for test mode articles
   */
  static buildTestModeParams(
    testMode: { title: string; content: string; status: 'draft' | 'published'; userId: number },
    collectionData: TopArticlesCollectionData,
  ) {
    return {
      status: testMode.status,
      title: `[TEST] ${testMode.title}`,
      text_elements: {
        kicker: 'Test Content-Recycling',
        text: `<p>Dies ist ein Test-Artikel für das Content-Recycling System.</p><p>Inhalt: ${testMode.content}</p><p><strong>Hinweis:</strong> Dies ist ein Test-Artikel und sollte nicht veröffentlicht werden.</p>`,
        teaser: `Test-Artikel für Content-Recycling: ${testMode.title}`,
        subline: 'Automatisierter Test',
      },
      user_id: testMode.userId,
      category_id: collectionData.categoryId,
      location_id: collectionData.locationId,
      tags: [...collectionData.tags, 'test', 'content-recycling', 'draft-test'],
      static_tags: ['test-mode', 'auto-generated'],
      comments_enabled: false,
      news_sitemap: false,
      remote_id: `content-recycling-test-${Date.now()}`,
    }
  }

  /**
   * Build PEIQ API parameters for production articles
   */
  static buildProductionParams(
    collectionData: TopArticlesCollectionData,
    topArticles: PeiqApiWrapperType.ArticleData[],
    collection: AutoContentCollection,
  ) {
    const publishTime = new Date()

    // Use custom fields from collection or fallback to generated content
    const customTitle = this.replacePlaceholders(
      collection.title || collectionData.title,
      collection,
      publishTime,
    )
    const customKicker = this.replacePlaceholders(
      collection.kicker || 'Top Beiträge',
      collection,
      publishTime,
    )
    const customTeaser = this.replacePlaceholders(
      collection.lead_text ||
        collectionData.subtitle ||
        (topArticles.length === 0
          ? 'Derzeit sind keine passenden Artikel verfügbar'
          : `Die ${topArticles.length} beliebtesten Artikel`),
      collection,
      publishTime,
    )
    const customContent = this.replacePlaceholders(
      collection.content || collectionData.content,
      collection,
      publishTime,
    )

    // Parse keywords_to_publish for article tags with placeholder support
    const customTags =
      this.parseKeywordsToPublish(collection.keywords_to_publish, collection, publishTime) ||
      collectionData.tags

    // Map category_to_publish string to PEIQ category ID
    const publishCategoryId = collection.category_to_publish
      ? CategoryMapper.mapCategoryNameWithFallback(collection.category_to_publish)
      : collectionData.categoryId

    return {
      status: 'published' as const,
      title: customTitle,
      text_elements: {
        kicker: customKicker,
        text: customContent,
        teaser: customTeaser,
        subline:
          this.replacePlaceholders(collection.push_header || '', collection, publishTime) ||
          undefined,
      },
      user_id: collectionData.userId,
      category_id: publishCategoryId,
      location_id: collectionData.locationId,
      tags: customTags,
      static_tags: this.getStaticTags(collection),
      comments_enabled: true,
      news_sitemap: true,
      remote_id: `recycling-${collection.id}-${Date.now()}`,
    }
  }

  /**
   * Parse keywords_to_publish CSV string into array of tags with placeholder support
   * Supports placeholders: {{region}}, {{date}}, {{weekday}}, {{month}}
   */
  static parseKeywordsToPublish(
    keywordsToPublish?: string | null,
    collection?: AutoContentCollection,
    publishTime?: Date,
  ): string[] | null {
    if (!keywordsToPublish || keywordsToPublish.trim() === '') {
      return null
    }

    let processedKeywords = keywordsToPublish

    // Replace placeholders if collection and publishTime are provided
    if (collection && publishTime) {
      processedKeywords = this.replacePlaceholders(processedKeywords, collection, publishTime)
    }

    // Split by comma, trim whitespace, filter empty strings
    return processedKeywords
      .split(',')
      .map((tag) => tag.trim())
      .filter((tag) => tag.length > 0)
  }

  /**
   * Get static tags for the collection
   * These are system tags that should always be present
   */
  private static getStaticTags(collection: AutoContentCollection): string[] {
    const staticTags: string[] = ['auto-generated']

    // Add content type tag if specified
    if (collection.content_type) {
      staticTags.push(`content-type-${collection.content_type}`)
    }

    // Add group tag
    if (collection.group) {
      staticTags.push(`group-${collection.group}`)
    }

    // Add interval tag if specified
    if (collection.interval) {
      staticTags.push(`interval-${collection.interval}`)
    }

    return staticTags
  }

  /**
   * Replace placeholders in keywords string
   * Supports: {{region}}, {{date}}, {{weekday}}, {{month}}
   * Also supports random options: {option1|option2|option3}
   */
  private static replacePlaceholders(
    text: string,
    collection: AutoContentCollection,
    publishTime: Date,
  ): string {
    let result = text

    // Extract region name from collection
    const { regionName } = CollectionDataExtractor.extractRegionData(collection)

    // German weekday names
    const weekdays = [
      'Sonntag',
      'Montag',
      'Dienstag',
      'Mittwoch',
      'Donnerstag',
      'Freitag',
      'Samstag',
    ]
    const weekday = weekdays[publishTime.getDay()]

    // German month names
    const months = [
      'Januar',
      'Februar',
      'März',
      'April',
      'Mai',
      'Juni',
      'Juli',
      'August',
      'September',
      'Oktober',
      'November',
      'Dezember',
    ]
    const month = months[publishTime.getMonth()]

    // Format date as dd.mm.yyyy
    const day = publishTime.getDate().toString().padStart(2, '0')
    const monthNum = (publishTime.getMonth() + 1).toString().padStart(2, '0')
    const year = publishTime.getFullYear()
    const formattedDate = `${day}.${monthNum}.${year}`

    // Replace fixed placeholders first
    result = result.replace(/\{\{region\}\}/g, regionName)
    result = result.replace(/\{\{date\}\}/g, formattedDate)
    result = result.replace(/\{\{weekday\}\}/g, weekday)
    result = result.replace(/\{\{month\}\}/g, month)

    // Replace random option placeholders {option1|option2|option3}
    result = result.replace(/\{([^}]+)\}/g, (match, content) => {
      const options = content.split('|').map((opt: string) => opt.trim())
      if (options.length > 1) {
        const randomIndex = Math.floor(Math.random() * options.length)
        return options[randomIndex]
      }
      return match
    })

    return result
  }
}
