import { logger } from '@regionalmedienaustria/microservice-utils'
import { getDay, getDate } from 'date-fns'

import { TimeHelpers } from '../utils/timeHelpers.js'

import type { AutoContentCollection } from '../services/directusService.js'
import type { ScheduleValidationResult } from '../services/scheduleService.js'

/**
 * Individual schedule validation rules
 * Each method focuses on a single validation concern
 */
export class ScheduleRules {
  /**
   * Validate interval-based scheduling requirements
   */
  static validateInterval(collection: AutoContentCollection, currentTime: Date): boolean {
    if (!collection.interval) {
      return true
    }

    const interval = collection.interval.toLowerCase().trim()

    switch (interval) {
      case 'daily':
        return this.validatePublishingWindow(collection, currentTime)

      case 'weekly':
        return this.validateDayOfWeek(collection.days || [], getDay(currentTime))

      case 'monthly':
        return this.validateMonthlyOccurrence(collection, currentTime)

      default:
        logger.warn('Unknown interval type, allowing execution', {
          interval,
          collectionId: collection.id,
        })
        return true
    }
  }

  /**
   * Validates if the current day of the week is in the list of allowed days.
   * An empty list means this check always passes.
   * @param allowedDays An array of lowercase day names (e.g., ['monday', 'tuesday']).
   * @param currentDay The current day as a number (0 for Sunday, 6 for Saturday).
   */
  static validateDayOfWeek(allowedDays: string[], currentDay: number): boolean {
    if (allowedDays.length === 0) {
      return true
    }

    const currentDayName = TimeHelpers.getDayName(currentDay)
    return allowedDays.includes(currentDayName)
  }

  /**
   * Validate day of month requirements.
   * Checks if the current date is in the list of allowed days.
   * Supports positive numbers (e.g., 1, 15, 31) and negative numbers
   * to count from the end of the month (e.g., -1 for the last day).
   */
  static validateDayOfMonth({
    allowedDays,
    currentDate,
    currentTime,
  }: {
    allowedDays: string[]
    currentDate: number
    currentTime: Date
  }): boolean {
    if (allowedDays.length === 0) {
      return true
    }

    const actualAllowedDays = allowedDays.map((day) => {
      const dayNum = parseInt(day)
      return TimeHelpers.normalizeDayOfMonth(
        dayNum,
        currentTime.getFullYear(),
        currentTime.getMonth(),
      )
    })

    logger.debug('Day of month validation', {
      allowedDays,
      actualAllowedDays,
      currentDate,
      month: currentTime.getMonth() + 1,
      year: currentTime.getFullYear(),
    })

    return actualAllowedDays.includes(currentDate)
  }

  /**
   * Validate publishing time window
   */
  static validatePublishingWindow(collection: AutoContentCollection, publishTime: Date): boolean {
    if (!collection.publish_time || !collection.publish_time.includes(':')) {
      logger.warn('Daily interval requires a valid publish_time (HH:MM). Skipping collection.', {
        collectionId: collection.id,
        publish_time: collection.publish_time,
      })
      return false
    }

    try {
      const timeString = collection.publish_time.trim()
      const [targetHour, targetMinute] = timeString.split(':').map(Number)
      const currentHour = publishTime.getHours()
      const currentMinute = publishTime.getMinutes()

      return TimeHelpers.isTimeWithinTolerance(
        targetHour,
        targetMinute,
        currentHour,
        currentMinute,
        5,
      )
    } catch (error) {
      logger.warn('Invalid publish_time format, skipping execution for this collection', {
        publish_time: collection.publish_time,
        error,
        collectionId: collection.id,
      })
      return false
    }
  }

  /**
   * Validate monthly occurrence patterns
   */
  static validateMonthlyOccurrence(collection: AutoContentCollection, currentTime: Date): boolean {
    if (!collection.monthly_occurrence) {
      return true
    }

    const occurrence = collection.monthly_occurrence.toLowerCase().trim()
    const currentDate = getDate(currentTime)

    switch (occurrence) {
      case 'first':
        return TimeHelpers.isFirstWeekOfMonth(currentDate)

      case 'last':
        return TimeHelpers.isLastDayOfMonth(
          currentDate,
          currentTime.getFullYear(),
          currentTime.getMonth(),
        )

      default:
        logger.warn('Unknown monthly_occurrence value, allowing execution', {
          monthly_occurrence: occurrence,
          collectionId: collection.id,
        })
        return true
    }
  }

  /**
   * Build validation result with next scheduled time
   */
  static buildValidationResult(
    shouldRun: boolean,
    reason: string,
    collection?: AutoContentCollection,
    currentTime?: Date,
  ): ScheduleValidationResult {
    const result: ScheduleValidationResult = {
      shouldRun,
      reason,
    }

    if (!shouldRun && collection && currentTime) {
      result.nextScheduledTime = this.calculateNextRun(collection, currentTime)
    }

    return result
  }

  /**
   * Calculate next run time based on interval
   */
  private static calculateNextRun(collection: AutoContentCollection, fromTime: Date): Date {
    if (!collection.interval) {
      const nextScheduled = new Date(fromTime)
      nextScheduled.setHours(nextScheduled.getHours() + 1, 0, 0, 0)
      return nextScheduled
    }

    const interval = collection.interval.toLowerCase().trim()

    switch (interval) {
      case 'daily':
        return TimeHelpers.calculateNextDaily(fromTime, collection.publish_time)

      case 'weekly':
        return TimeHelpers.calculateNextWeekly(fromTime, collection.publish_time)

      case 'monthly':
        return TimeHelpers.calculateNextMonthly(
          fromTime,
          collection.monthly_occurrence,
          collection.publish_time,
        )

      default:
        const nextScheduled = new Date(fromTime)
        nextScheduled.setHours(nextScheduled.getHours() + 1, 0, 0, 0)
        return nextScheduled
    }
  }
}
