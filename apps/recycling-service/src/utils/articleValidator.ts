import { logger } from '@regionalmedienaustria/microservice-utils'

import type { PeiqApiWrapper as PeiqApiWrapperType } from '@regionalmedienaustria/peiq-api-wrapper'

/**
 * Utility for validating article data from PEIQ API
 */
export class ArticleValidator {
  /**
   * Validate article data from PEIQ API for content recycling eligibility.
   *
   * Validates:
   * - Article existence and basic structure
   * - Required fields: id, title, status, text_elements.teaser
   * - Publication status (must be 'published')
   * - Numeric field validity: location_id, category_id
   *
   * @param article - Article data from PEIQ API
   * @returns Validation result with success flag and error message
   */
  static validateArticleData(article: PeiqApiWrapperType.ArticleData): { isValid: boolean; error?: string } {
    if (!article) {
      return { isValid: false, error: 'Article is null or undefined' }
    }

    if (!article.id) {
      return { isValid: false, error: 'Article has no ID' }
    }

    if (!article.title || article.title.trim().length === 0) {
      return { isValid: false, error: 'Article has no title' }
    }

    if (!article.status) {
      return { isValid: false, error: 'Article has no status' }
    }

    // Only include published articles
    if (article.status !== 'published') {
      return { isValid: false, error: `Article is not published: ${article.status}` }
    }

    // Check for basic content structure
    if (!article.text_elements || !article.text_elements.teaser) {
      return { isValid: false, error: 'Article has no teaser content' }
    }

    // Check that required numeric values are valid
    if (article.location_id && isNaN(parseInt(article.location_id.toString()))) {
      return { isValid: false, error: `Invalid location_id: ${article.location_id}` }
    }

    if (article.category_id && isNaN(parseInt(article.category_id.toString()))) {
      return { isValid: false, error: `Invalid category_id: ${article.category_id}` }
    }

    return { isValid: true } // Valid article
  }

  /**
   * Filter array of articles, removing invalid ones and logging warnings
   */
  static filterValidArticles(
    articles: PeiqApiWrapperType.ArticleData[], 
    collectionId: string
  ): { valid: PeiqApiWrapperType.ArticleData[]; invalidCount: number } {
    const valid: PeiqApiWrapperType.ArticleData[] = []
    let invalidCount = 0

    for (const article of articles) {
      const validation = this.validateArticleData(article)
      if (!validation.isValid) {
        invalidCount++
        logger.warn('Skipping invalid article from search results', {
          articleId: article.id,
          validationError: validation.error,
          collectionId
        })
      } else {
        valid.push(article)
      }
    }

    return { valid, invalidCount }
  }
}