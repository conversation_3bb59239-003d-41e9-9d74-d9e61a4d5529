import type { AutoContentCollection } from '../services/directusService.js'

/**
 * Configuration helper for recycling service settings and thresholds
 * Centralizes configuration logic and environment variable handling
 */
export class RecyclingConfig {
  /**
   * Get minimum articles threshold for collection processing
   */
  static getMinimumArticlesThreshold(): number {
    return parseInt(process.env.MIN_ARTICLES_THRESHOLD || '3')
  }

  /**
   * Get recent generation check window (hours)
   */
  static getRecentGenerationWindow(): number {
    return parseInt(process.env.RECENT_GENERATION_WINDOW || '24')
  }

  /**
   * Get default publish time fallback hour
   */
  static getDefaultPublishHour(): number {
    return parseInt(process.env.DEFAULT_PUBLISH_HOUR || '12')
  }

  /**
   * Get supported content types
   */
  static getSupportedContentTypes(): string[] {
    const envTypes = process.env.SUPPORTED_CONTENT_TYPES
    if (envTypes) {
      return envTypes.split(',').map(type => type.trim().toLowerCase())
    }
    
    return ['gallery_collection', 'article_collection']
  }

  /**
   * Get maximum processing time (ms) before timeout
   */
  static getMaxProcessingTime(): number {
    return parseInt(process.env.MAX_PROCESSING_TIME || '300000') // 5 minutes default
  }

  /**
   * Get parallel processing batch size
   */
  static getParallelBatchSize(): number {
    return parseInt(process.env.PARALLEL_BATCH_SIZE || '10')
  }

  /**
   * Check if debug logging is enabled
   */
  static isDebugMode(): boolean {
    return process.env.RECYCLING_DEBUG === 'true' || process.env.NODE_ENV === 'development'
  }

  /**
   * Get retry configuration for failed operations
   */
  static getRetryConfig(): { maxRetries: number; delayMs: number } {
    return {
      maxRetries: parseInt(process.env.MAX_RETRIES || '3'),
      delayMs: parseInt(process.env.RETRY_DELAY_MS || '1000')
    }
  }

  /**
   * Get collection processing settings
   */
  static getProcessingSettings(): {
    enableParallelProcessing: boolean
    enableTimestampUpdates: boolean
    enableFailureLogging: boolean
    enableValidationLogging: boolean
  } {
    return {
      enableParallelProcessing: process.env.ENABLE_PARALLEL_PROCESSING !== 'false',
      enableTimestampUpdates: process.env.ENABLE_TIMESTAMP_UPDATES !== 'false',
      enableFailureLogging: process.env.ENABLE_FAILURE_LOGGING !== 'false',
      enableValidationLogging: process.env.ENABLE_VALIDATION_LOGGING !== 'false'
    }
  }

  /**
   * Get default test collection configuration
   */
  static getTestCollectionDefaults(): Partial<AutoContentCollection> {
    return {
      status: 'published',
      time_in_past: 24,
      grouping: ['district'],
      keywords: 'test',
      content: 'Test content',
      group: 'test'
    }
  }
}