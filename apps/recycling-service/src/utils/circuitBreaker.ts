import { logger } from '@regionalmedienaustria/microservice-utils'

/**
 * Circuit Breaker pattern implementation for external API calls.
 *
 * Prevents cascading failures by monitoring API call failures and temporarily
 * blocking requests when a service becomes unavailable. Acts like an electrical
 * circuit breaker - opens when too many failures occur, preventing system overload.
 *
 * **Three States:**
 * - CLOSED: Normal operation, requests pass through
 * - OPEN: Service unavailable, requests fail immediately (fail-fast)
 * - HALF_OPEN: Testing if service recovered, limited requests allowed
 *
 * **Benefits:**
 * - Prevents resource exhaustion from hanging requests
 * - Provides automatic recovery testing
 * - Enables graceful degradation
 * - Improves system resilience
 *
 * @example
 * ```typescript
 * const breaker = new CircuitBreaker('API_NAME', 5, 60000)
 * const result = await breaker.execute(() => apiCall())
 * ```
 */
export class CircuitBreaker {
  private failureCount = 0

  private lastFailureTime = 0

  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED'
  
  constructor(
    private readonly name: string,
    private readonly failureThreshold = 5,
    private readonly resetTimeout = 60000, // 1 minute
    private readonly halfOpenAttempts = 3
  ) {}

  /**
   * Execute a function with circuit breaker protection.
   *
   * @param fn - The async function to protect
   * @returns Promise with the function result
   * @throws Error if circuit is OPEN or function fails
   */
  async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN'
        logger.info(`Circuit breaker ${this.name} entering HALF_OPEN state`)
      } else {
        throw new Error(`Circuit breaker ${this.name} is OPEN - service unavailable`)
      }
    }

    try {
      const result = await fn()
      this.onSuccess()
      return result
    } catch (error) {
      this.onFailure()
      throw error
    }
  }

  private onSuccess(): void {
    if (this.state === 'HALF_OPEN') {
      logger.info(`Circuit breaker ${this.name} recovered - closing circuit`)
    }
    this.failureCount = 0
    this.state = 'CLOSED'
  }

  private onFailure(): void {
    this.failureCount++
    this.lastFailureTime = Date.now()

    if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN'
      logger.error(`Circuit breaker ${this.name} opened after ${this.failureCount} failures`)
    }
  }

  /**
   * Get current circuit breaker status for monitoring and debugging.
   *
   * @returns Object with state, failureCount and isHealthy flag
   */
  getStatus(): { state: string; failureCount: number; isHealthy: boolean } {
    return {
      state: this.state,
      failureCount: this.failureCount,
      isHealthy: this.state === 'CLOSED'
    }
  }

  /**
   * Manually reset the circuit breaker to CLOSED state.
   * Use with caution - only when you're sure the service is healthy again.
   */
  reset(): void {
    this.failureCount = 0
    this.state = 'CLOSED'
    logger.info(`Circuit breaker ${this.name} manually reset`)
  }
}

// Pre-configured circuit breaker instances for different services

/** Circuit breaker for PEIQ API calls (external service, higher tolerance) */
export const peiqApiCircuitBreaker = new CircuitBreaker('PEIQ_API', 5, 60000)

/** Circuit breaker for Directus CMS calls (internal service, lower tolerance) */
export const directusCircuitBreaker = new CircuitBreaker('Directus', 3, 30000)