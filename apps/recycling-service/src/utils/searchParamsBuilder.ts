import { logger } from '@regionalmedienaustria/microservice-utils'

import { CategoryMapper } from './categoryMapper.js'

import type { AutoContentCollection } from '../services/directusService.js'
import type { PeiqApiWrapper } from '@regionalmedienaustria/peiq-api-wrapper'

/**
 * Utility for building PEIQ API search parameters from collection configuration
 */
export class SearchParamsBuilder {
  /**
   * Build negative tags from collection config and system defaults
   * 
   * System default negative tags:
   * - 'commercial': Exclude sponsored/commercial content from collections
   * - 'recycled': Exclude already recycled articles to prevent re-recycling
   * - 'top-collection': Exclude other top collections to prevent nesting
   * 
   * These tags correspond to static_tags added by ContentBuilder when creating articles
   */
  static buildNegativeTags(collection: AutoContentCollection): string[] {
    // Parse CSV string from Directus into array of keywords
    const negativeKeywords = collection.negative_keywords
      ? collection.negative_keywords.split(',').map(k => k.trim()).filter(k => k.length > 0)
      : []
    
    // System negative tags that prevent recursive recycling and commercial content
    const systemNegativeTags = [
      'commercial',      // Exclude commercial/sponsored content
      'recycled',        // Prevent re-recycling of already recycled content
      'top-collection'   // Prevent nesting of top collections
    ]
    
    return [
      ...systemNegativeTags,
      ...negativeKeywords
    ]
  }

  /**
   * Extract grouping value from Directus Multiple Dropdown format
   * Returns the first value if array, or the value itself if string
   * Defaults to 'district' if not specified
   */
  static getGroupingValue(grouping?: string[] | string): 'district' | 'category' {
    const groupingValue = Array.isArray(grouping) ? grouping[0] : grouping
    
    if (groupingValue === 'category' || groupingValue === 'district') {
      return groupingValue
    }
    
    return 'district'
  }

  /**
   * Map Directus collection_criteria values to PEIQ API sortBy values
   * PEIQ API expects: 'tracking_visit_count', 'like_count', 'comment_count'
   * Directus provides: 'most_likes', 'most_viewed', 'keywords'
   *
   * NOTE: 'keywords' is a search criteria, not a sort criteria. When collection_criteria
   * is 'keywords', we use the keywords field for filtering, not sorting.
   */
  static mapCollectionCriteriaToSortBy(criteria?: string | null): string | undefined {
    switch (criteria) {
      case 'most_likes':
        return 'like_count'
      case 'most_viewed':
        return 'tracking_visit_count'
      case 'keywords':
        // Keywords is a filter criteria, not a sort criteria
        // Return undefined to use PEIQ API default sorting
        return undefined
      default:
        return 'tracking_visit_count'
    }
  }

  /**
   * Extract location IDs from collection region data
   * Handles both number and Trees object types for trees_id
   * Filters out invalid IDs (0 or negative values)
   *
   * @param region - Array of AutoContentCollectionTrees1 objects from Directus
   * @returns Array of valid location IDs
   */
  static extractLocationIds(region?: AutoContentCollection['region']): number[] | undefined {
    return region?.map(r => {
      const id = typeof r.trees_id === 'number' ? r.trees_id : parseInt(r.trees_id?.toString() || '0')
      return id
    }).filter(id => id > 0)
  }

  /**
   * Build complete search parameters for ArticleSearchService
   */
  static buildSearchParams(collection: AutoContentCollection): PeiqApiWrapper.ArticleSearchParams {
    const sortBy = this.mapCollectionCriteriaToSortBy(collection.collection_criteria)
    
    const baseParams: PeiqApiWrapper.ArticleSearchParams = {
      groupBy: this.getGroupingValue(collection.grouping || undefined),
      sortDir: 'desc',
      locationIds: this.extractLocationIds(collection.region),
      categoryIds: CategoryMapper.mapCategoryNamesToIds(collection.category),
      negativeTags: this.buildNegativeTags(collection),
      hoursBack: collection.time_in_past || 24,
      status: 'published'
    }

    // Only add sortBy if it's defined (not for 'keywords' criteria)
    if (sortBy !== undefined) {
      baseParams.sortBy = sortBy
    }

    // Add keywords for tag-based search if provided
    // For 'keywords' collection_criteria, use the keywords field for tag filtering
    if (collection.collection_criteria === 'keywords' && collection.keywords && collection.keywords.trim() !== '') {
      const keywordArray = collection.keywords.split(',').map(k => k.trim()).filter(k => k.length > 0)
      if (keywordArray.length > 0) {
        // Use keywords as tags for filtering (PEIQ API uses 'tags' for positive filtering)
        baseParams.tags = keywordArray
        logger.info('Using keywords-based article search with tag filtering', { 
          keywords: keywordArray,
          collectionId: collection.id,
          criteria: 'keywords',
          sortBy: baseParams.sortBy || 'PEIQ API default'
        })
      }
    } else if (collection.collection_criteria) {
      logger.info('Using collection criteria for article search', {
        collectionId: collection.id,
        criteria: collection.collection_criteria,
        sortBy: baseParams.sortBy
      })
    }

    return baseParams
  }

}