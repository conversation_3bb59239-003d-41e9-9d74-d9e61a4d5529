import { logger } from '@regionalmedienaustria/microservice-utils'

import type { AutoContentCollection } from '@rma-mono/directus-client'

/**
 * Utility class for extracting and validating region IDs from collections.
 */
export class RegionExtractor {
  /**
   * Extracts region ID from a single region object.
   *
   * @param region - Single region object from collection
   * @returns Valid region ID or null if invalid
   */
  private static extractRegionId(region: any): number | null {
    if (!region || region.trees_id === null || region.trees_id === undefined) {
      return null
    }

    let regionId: number

    // Handle different types of trees_id values
    if (typeof region.trees_id === 'number') {
      regionId = region.trees_id
    } else if (typeof region.trees_id === 'string') {
      // Parse string IDs like '42' to numbers
      regionId = parseInt(region.trees_id, 10)
    } else if (typeof region.trees_id === 'object' && region.trees_id.id !== null && region.trees_id.id !== undefined) {
      // Handle nested objects like { id: 42 } or { id: '42' }
      if (typeof region.trees_id.id === 'number') {
        regionId = region.trees_id.id
      } else if (typeof region.trees_id.id === 'string') {
        regionId = parseInt(region.trees_id.id, 10)
      } else {
        return null
      }
    } else {
      return null
    }

    return (!isNaN(regionId) && regionId > 0) ? regionId : null
  }

  /**
   * Extracts a valid location ID from collection regions.
   * 
   * @param collection - Collection with region junction data
   * @param options - Options for logging behavior
   * @returns First valid location ID or null if none found
   */
  static getValidRegionId(
    collection: AutoContentCollection,
    options: { enableLogging?: boolean } = { enableLogging: true }
  ): number | null {
    if (!collection.region || collection.region.length === 0) {
      if (options.enableLogging) {
        logger.warn('No regions defined for collection', {
          collectionId: collection.id,
          title: collection.title
        })
      }
      return null
    }
    
    // Try to find the first valid region
    for (const region of collection.region) {
      const regionId = this.extractRegionId(region)
      if (regionId !== null) {
        return regionId
      }
    }
    
    if (options.enableLogging) {
      logger.error('No valid location ID found in any region', {
        collectionId: collection.id,
        regions: collection.region
      })
    }
    
    return null
  }

  /**
   * Extracts all valid region IDs from collection regions.
   * 
   * @param collection - Collection with region junction data
   * @returns Array of valid location IDs
   */
  static getAllValidRegionIds(collection: AutoContentCollection): number[] {
    if (!collection.region || collection.region.length === 0) {
      return []
    }
    
    const validRegionIds: number[] = []

    for (const region of collection.region) {
      const regionId = this.extractRegionId(region)
      if (regionId !== null) {
        validRegionIds.push(regionId)
      }
    }

    return validRegionIds
  }

  /**
   * Validates if a region ID is valid.
   *
   * @param regionId - The region ID to validate
   * @returns True if the region ID is valid
   */
  static isValidRegionId(regionId: number | string | undefined | null): boolean {
    if (regionId === null || regionId === undefined) {
      return false
    }

    // Handle empty strings
    if (typeof regionId === 'string' && regionId.trim() === '') {
      return false
    }

    let id: number
    if (typeof regionId === 'number') {
      id = regionId
    } else if (typeof regionId === 'string') {
      id = parseInt(regionId, 10)
    } else {
      return false
    }

    return !isNaN(id) && id > 0
  }
}