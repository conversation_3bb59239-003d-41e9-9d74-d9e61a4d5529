import { TimeValidator } from './timeValidator.js'

/**
 * Helper utilities for time calculations and date manipulation
 */
export class TimeHelpers {
  /**
   * Check if two times are within a tolerance window.
   *
   * A tolerance window defines the acceptable time difference (in minutes) between
   * a target time and the current time. For example, with a 5-minute tolerance:
   * - Target: 10:30, Current: 10:32 → within tolerance (2 min difference)
   * - Target: 10:30, Current: 10:36 → outside tolerance (6 min difference)
   *
   * This is commonly used for scheduling validation where exact time matching
   * isn't practical due to processing delays or system timing variations.
   */
  static isTimeWithinTolerance({
    targetHour,
    targetMinute,
    currentHour,
    currentMinute,
    toleranceMinutes = 5
  }: {
    targetHour: number
    targetMinute: number
    currentHour: number
    currentMinute: number
    toleranceMinutes?: number
  }): boolean {
    const targetTotalMinutes = targetHour * 60 + targetMinute
    const currentTotalMinutes = currentHour * 60 + currentMinute
    return Math.abs(currentTotalMinutes - targetTotalMinutes) <= toleranceMinutes
  }

  static getLastDayOfMonth(year: number, month: number): number {
    return new Date(year, month + 1, 0).getDate()
  }

  static getDayName(dayNumber: number): string {
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    return dayNames[dayNumber] || 'Unknown'
  }

  /**
   * Normalize day numbers to valid day of month
   * Positive numbers are returned as-is if valid
   * Negative numbers: -1 = last day of month, -2 = second-to-last day, etc.
   */
  static normalizeDayOfMonth(dayNumber: number, year: number, month: number): number {
    if (dayNumber >= 0) {
      return dayNumber
    }
    
    const lastDayOfMonth = this.getLastDayOfMonth(year, month)
    return lastDayOfMonth + dayNumber + 1
  }

  static isFirstWeekOfMonth(currentDate: number): boolean {
    return currentDate >= 1 && currentDate <= 7
  }

  static isLastDayOfMonth(currentDate: number, year: number, month: number): boolean {
    const lastDay = this.getLastDayOfMonth(year, month)
    return currentDate === lastDay
  }

  static calculateNextDaily(fromTime: Date, publishTime?: string | null): Date {
    const nextScheduled = new Date(fromTime)
    nextScheduled.setDate(nextScheduled.getDate() + 1)
    const dateWithTime = TimeValidator.setTimeOnDate(nextScheduled, publishTime)
    return dateWithTime || nextScheduled
  }

  static calculateNextWeekly(fromTime: Date, publishTime?: string | null): Date {
    const nextScheduled = new Date(fromTime)
    nextScheduled.setDate(nextScheduled.getDate() + 7)
    const dateWithTime = TimeValidator.setTimeOnDate(nextScheduled, publishTime)
    return dateWithTime || nextScheduled
  }

  static calculateNextMonthly(fromTime: Date, monthlyOccurrence?: string | null, publishTime?: string | null): Date {
    const nextScheduled = new Date(fromTime)
    nextScheduled.setMonth(nextScheduled.getMonth() + 1)
    
    if (monthlyOccurrence) {
      const occurrence = monthlyOccurrence.toLowerCase().trim()
      
      switch (occurrence) {
        case 'first':
          nextScheduled.setDate(1)
          break
        case 'last':
          const lastDay = this.getLastDayOfMonth(nextScheduled.getFullYear(), nextScheduled.getMonth())
          nextScheduled.setDate(lastDay)
          break
        default:
          nextScheduled.setDate(1) // Fallback to first of month
      }
    } else {
      nextScheduled.setDate(1) // Default to first of month
    }
    
    const dateWithTime = TimeValidator.setTimeOnDate(nextScheduled, publishTime)
    return dateWithTime || nextScheduled
  }
}