import { logger } from '@regionalmedienaustria/microservice-utils'

/**
 * Constants for time validation
 */
export const TIME_VALIDATION_CONSTANTS = {
  MIN_HOURS_IN_PAST: 1,
  MAX_HOURS_IN_PAST: 8760, // 365 days
  TIME_WINDOW_TOLERANCE_MINUTES: 5,
  DEFAULT_PUBLISH_HOUR: 12,
  MINUTES_IN_HOUR: 60,
  HOURS_IN_DAY: 24,
  DAYS_IN_WEEK: 7,
  MONTHS_IN_YEAR: 12,
  MAX_DAYS_IN_MONTH: 31,
  // Validates time format HH:mm (24-hour format). Examples: 09:30, 14:45, 23:59
  TIME_REGEX: /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/,
} as const

/**
 * Utility class for time validation and parsing.
 * Consolidates all time-related validation logic in one place.
 */
export class TimeValidator {
  /**
   * Parses a time string (HH:mm) to total minutes.
   *
   * @param timeString - Time in HH:mm format (e.g., "14:30")
   * @returns Total minutes or null if invalid
   */
  static parseTimeToMinutes(timeString: string | null | undefined): number | null {
    if (!timeString) {
      return null
    }

    const match = timeString.match(TIME_VALIDATION_CONSTANTS.TIME_REGEX)
    if (!match) {
      logger.warn('Invalid time format', { timeString, expected: 'HH:mm' })
      return null
    }

    const [, hours, minutes] = match
    const totalMinutes =
      parseInt(hours) * TIME_VALIDATION_CONSTANTS.MINUTES_IN_HOUR + parseInt(minutes)

    return totalMinutes
  }

  /**
   * Validates if a time string is in valid HH:mm format.
   *
   * @param timeString - Time string to validate
   * @returns True if valid HH:mm format
   */
  static isValidTimeFormat(timeString: string | null | undefined): boolean {
    if (!timeString) {
      return false
    }
    return TIME_VALIDATION_CONSTANTS.TIME_REGEX.test(timeString)
  }

  /**
   * Validates the time_in_past field value.
   *
   * @param hours - Number of hours in the past
   * @returns Object with isValid flag and error message if invalid
   */
  static validateTimeInPast(hours: number | null | undefined): {
    isValid: boolean
    error?: string
  } {
    if (hours === null || hours === undefined) {
      return { isValid: false, error: 'time_in_past is required' }
    }

    if (typeof hours !== 'number' || isNaN(hours)) {
      return { isValid: false, error: 'time_in_past must be a number' }
    }

    if (hours < TIME_VALIDATION_CONSTANTS.MIN_HOURS_IN_PAST) {
      return {
        isValid: false,
        error: `time_in_past must be at least ${TIME_VALIDATION_CONSTANTS.MIN_HOURS_IN_PAST} hour`,
      }
    }

    if (hours > TIME_VALIDATION_CONSTANTS.MAX_HOURS_IN_PAST) {
      return {
        isValid: false,
        error: `time_in_past must not exceed ${TIME_VALIDATION_CONSTANTS.MAX_HOURS_IN_PAST} hours (365 days)`,
      }
    }

    return { isValid: true }
  }

  /**
   * Validates if current time is within publishing window.
   *
   * @param publishTime - Target publish time in HH:mm format
   * @param toleranceMinutes - Tolerance window in minutes (default: 5)
   * @returns True if current time is within window
   */
  static isWithinPublishingWindow(
    publishTime: string | null | undefined,
    toleranceMinutes: number = TIME_VALIDATION_CONSTANTS.TIME_WINDOW_TOLERANCE_MINUTES,
  ): boolean {
    const publishMinutes = this.parseTimeToMinutes(publishTime)
    if (publishMinutes === null) {
      return false
    }

    const now = new Date()
    const currentMinutes =
      now.getHours() * TIME_VALIDATION_CONSTANTS.MINUTES_IN_HOUR + now.getMinutes()

    const difference = Math.abs(currentMinutes - publishMinutes)
    return difference <= toleranceMinutes
  }

  /**
   * Validates day of week (0-6, where 0 is Sunday).
   *
   * @param day - Day of week to validate
   * @returns True if valid day of week
   */
  static isValidDayOfWeek(day: number | string | null | undefined): boolean {
    if (day === null || day === undefined) {
      return false
    }

    const dayNum = typeof day === 'string' ? parseInt(day) : day
    return !isNaN(dayNum) && dayNum >= 0 && dayNum < TIME_VALIDATION_CONSTANTS.DAYS_IN_WEEK
  }

  /**
   * Validates day of month (1-31, or negative for days from end).
   *
   * @param day - Day of month to validate
   * @returns True if valid day of month
   */
  static isValidDayOfMonth(day: number | string | null | undefined): boolean {
    if (day === null || day === undefined) {
      return false
    }

    const dayNum = typeof day === 'string' ? parseInt(day) : day
    if (isNaN(dayNum) || dayNum === 0) {
      return false
    }

    // Allow negative days (counting from end of month)
    if (dayNum < 0) {
      return dayNum >= -TIME_VALIDATION_CONSTANTS.MAX_DAYS_IN_MONTH
    }

    return dayNum <= TIME_VALIDATION_CONSTANTS.MAX_DAYS_IN_MONTH
  }

  /**
   * Validates array of days.
   *
   * @param days - Array of days to validate
   * @param validator - Validation function for individual days
   * @returns True if all days are valid
   */
  static validateDaysArray(
    days: string[] | null | undefined,
    validator: (day: string) => boolean,
  ): boolean {
    if (!days || !Array.isArray(days) || days.length === 0) {
      return false
    }
    return days.every(validator)
  }

  /**
   * Converts hours to milliseconds.
   *
   * @param hours - Number of hours
   * @returns Milliseconds
   */
  static hoursToMilliseconds(hours: number): number {
    return hours * TIME_VALIDATION_CONSTANTS.MINUTES_IN_HOUR * 60 * 1000
  }

  /**
   * Checks if a date is in the past by specified hours.
   *
   * @param date - Date to check
   * @param hoursInPast - How many hours in the past to check
   * @returns True if date is within the time window
   */
  static isWithinPastHours(date: Date | string, hoursInPast: number): boolean {
    const checkDate = typeof date === 'string' ? new Date(date) : date
    const now = new Date()
    const thresholdTime = now.getTime() - this.hoursToMilliseconds(hoursInPast)

    return checkDate.getTime() >= thresholdTime
  }

  /**
   * Sets time on a date from a time string.
   *
   * @param date - Date to modify
   * @param timeString - Time in HH:mm format
   * @returns Modified date or null if invalid time
   */
  static setTimeOnDate(date: Date, timeString: string | null | undefined): Date | null {
    const minutes = this.parseTimeToMinutes(timeString)
    if (minutes === null) {
      return null
    }

    const newDate = new Date(date)
    const hours = Math.floor(minutes / TIME_VALIDATION_CONSTANTS.MINUTES_IN_HOUR)
    const mins = minutes % TIME_VALIDATION_CONSTANTS.MINUTES_IN_HOUR

    newDate.setHours(hours, mins, 0, 0)
    return newDate
  }

  /**
   * Validates monthly occurrence string.
   *
   * @param occurrence - Monthly occurrence value
   * @returns True if valid occurrence
   */
  static isValidMonthlyOccurrence(occurrence: string | null | undefined): boolean {
    if (!occurrence) {
      return false
    }

    const validOccurrences = [
      'first_week',
      'second_week',
      'third_week',
      'fourth_week',
      'last_week',
      'first_day',
      'last_day',
    ]

    return validOccurrences.includes(occurrence)
  }

  /**
   * Gets a human-readable error message for time validation failures.
   *
   * @param field - Field name that failed validation
   * @param value - The invalid value
   * @param constraint - The constraint that was violated
   * @returns Human-readable error message
   */
  static getTimeValidationError(field: string, value: unknown, constraint: string): string {
    return `Invalid ${field}: ${value}. ${constraint}`
  }
}
