import { logger } from '@regionalmedienaustria/microservice-utils'

import { DirectusService } from '../services/directusService.js'

import type { AutoContentCollection } from '../services/directusService.js'
import type { Trees } from '@rma-mono/directus-client'

/**
 * Utility for extracting data from AutoContentCollection fields
 * Handles Junction Collections and Multiple Dropdowns consistently with robust error handling
 */
export class CollectionDataExtractor {
  private static categoryDisplayMap: Record<string, string> | null = null

  private static categoryMapLastFetched: number = 0

  private static readonly CATEGORY_MAP_TTL = 24 * 60 * 60 * 1000 // 24 hours

  /**
   * Get category display map dynamically from Directus field metadata
   */
  private static async getCategoryDisplayMap(): Promise<Record<string, string>> {
    const now = Date.now()
    
    // Return cached map if still valid
    if (
      this.categoryDisplayMap && 
      (now - this.categoryMapLastFetched) < this.CATEGORY_MAP_TTL
    ) {
      return this.categoryDisplayMap
    }

    try {
      // Fetch field metadata from Directus
      const fieldMetadata = await DirectusService.getFieldMetadata('auto_content_collection', 'category')
      
      // Extract choices from field metadata
      const choices = fieldMetadata?.meta?.options?.choices
      
      if (choices && Array.isArray(choices)) {
        // Convert choices array to display map
        const displayMap: Record<string, string> = {}
        for (const choice of choices) {
          if (choice.value && choice.text) {
            displayMap[choice.value] = choice.text
          }
        }
        
        // Cache the result
        this.categoryDisplayMap = displayMap
        this.categoryMapLastFetched = now
        
        logger.debug('Successfully fetched category display map from Directus', {
          choices: Object.keys(displayMap).length
        })
        
        return displayMap
      }
      
      logger.warn('No choices found in category field metadata, using fallback')
    } catch (error) {
      logger.error('Failed to fetch category display map from Directus, using fallback', { error })
    }
    
    // Fallback to hardcoded map if fetch fails
    const fallbackMap = {
      'lokales': 'Lokales',
      'politik': 'Politik',
      'sport': 'Sport',
      'wirtschaft': 'Wirtschaft',
      'leute': 'Leute',
      'freizeit': 'Freizeit & Kultur',
      'bauen': 'Bauen & Wohnen',
      'mobilitaet': 'Mobilität',
      'reisen': 'Reisen',
      'gesundheit': 'Gesundheit',
      'community': 'Regionauten',
      'regionauten-community': 'Regionauten'
    }
    
    this.categoryDisplayMap = fallbackMap
    this.categoryMapLastFetched = now
    
    return fallbackMap
  }

  /**
   * Extract region data from Junction Collection (trees) with comprehensive error handling
   */
  static extractRegionData(collection: AutoContentCollection): {
    regionData: Trees | null
    regionName: string
    regionId: number
  } {
    try {
      // Handle Junction Collection region field
      // Note: Currently we handle multiple data structures because the Directus query 
      // doesn't always expand the trees_id relation. Ideally, we should ensure
      // the relation is always expanded in the query.
      if (!collection.region || !Array.isArray(collection.region) || collection.region.length === 0) {
        logger.warn('No regions found in collection, using default region', { 
          collectionId: collection.id,
          regionField: collection.region 
        })
        // Using Wien (1) as default - this should be configured properly in Directus
        return { 
          regionData: null, 
          regionName: 'Wien', 
          regionId: 1
        }
      }

      const firstRegion = collection.region[0]
      
      // Case 1: Direct object with trees_id as object (expanded)
      if (firstRegion?.trees_id && typeof firstRegion.trees_id === 'object' && firstRegion.trees_id !== null) {
        const treeData = firstRegion.trees_id 
        const regionName = treeData.title || treeData.name || 'Region'
        const regionId = treeData.id || treeData.peiq_id || 1
        
        logger.debug('Extracted region data from expanded Junction Collection', {
          collectionId: collection.id,
          regionName,
          regionId,
          treeData: { id: treeData.id, title: treeData.title }
        })
        
        return { 
          regionData: treeData, 
          regionName: String(regionName), 
          regionId: Number(regionId) || 1
        }
      }
      
      // Case 2: trees_id is numeric ID (not expanded)
      if (firstRegion?.trees_id && typeof firstRegion.trees_id === 'number') {
        const regionId = firstRegion.trees_id
        
        logger.debug('Extracted region ID from non-expanded Junction Collection', {
          collectionId: collection.id,
          regionId
        })
        
        return { 
          regionData: firstRegion.trees_id, 
          regionName: `Region ${regionId}`, 
          regionId
        }
      }
      
      // Case 3: trees_id is string ID (not expanded)
      if (firstRegion?.trees_id && typeof firstRegion.trees_id === 'string') {
        const regionId = parseInt(firstRegion.trees_id, 10)
        
        if (!isNaN(regionId)) {
          logger.debug('Extracted region ID from string Junction Collection', {
            collectionId: collection.id,
            regionId
          })
          
          return { 
            regionData: firstRegion.trees_id, 
            regionName: `Region ${regionId}`, 
            regionId
          }
        }
      }
      
      // Case 4: Unexpected structure - log and use defaults
      logger.warn('Unexpected region structure in Junction Collection, using default region', {
        collectionId: collection.id,
        firstRegion,
        regionStructure: {
          hasTreesId: !!firstRegion?.trees_id,
          treesIdType: typeof firstRegion?.trees_id,
          isArray: Array.isArray(firstRegion?.trees_id)
        }
      })
      
      return { 
        regionData: firstRegion, 
        regionName: 'Wien', 
        regionId: 1
      }
      
    } catch (error) {
      logger.error('Failed to extract region data from Junction Collection', {
        error,
        collectionId: collection.id,
        regionField: collection.region
      })
      
      return { 
        regionData: null, 
        regionName: 'Wien', 
        regionId: 1
      }
    }
  }

  /**
   * Extract category data from Multiple Dropdown with error handling
   */
  static async extractCategoryData(collection: AutoContentCollection): Promise<{
    categoryNames: string[]
    categoryName: string
  }> {
    try {
      // Handle Multiple Dropdown category field
      if (!collection.category || !Array.isArray(collection.category) || collection.category.length === 0) {
        logger.warn('No categories found in collection, using default', { 
          collectionId: collection.id,
          categoryField: collection.category 
        })
        return { 
          categoryNames: ['regionauten-community'], 
          categoryName: 'Regionauten' 
        }
      }

      const categoryNames = collection.category.filter(cat => typeof cat === 'string' && cat.trim().length > 0)
      
      if (categoryNames.length === 0) {
        logger.warn('No valid categories found in collection, using default', { 
          collectionId: collection.id,
          originalCategories: collection.category 
        })
        return { 
          categoryNames: ['regionauten-community'], 
          categoryName: 'Regionauten' 
        }
      }

      const categoryDisplayMap = await this.getCategoryDisplayMap()
      const categoryName = categoryDisplayMap[categoryNames[0]] || categoryNames[0]

      logger.debug('Extracted category data from Multiple Dropdown', {
        collectionId: collection.id,
        categoryNames,
        categoryName
      })

      return { categoryNames, categoryName }
      
    } catch (error) {
      logger.error('Failed to extract category data from Multiple Dropdown', {
        error,
        collectionId: collection.id,
        categoryField: collection.category
      })
      
      return { 
        categoryNames: ['regionauten-community'], 
        categoryName: 'Regionauten' 
      }
    }
  }

  /**
   * Extract both region and category data at once
   */
  static async extractCollectionData(collection: AutoContentCollection): Promise<{
    regionData: Trees | null
    regionName: string
    regionId: number
    categoryNames: string[]
    categoryName: string
  }> {
    const { regionData, regionName, regionId } = this.extractRegionData(collection)
    const { categoryNames, categoryName } = await this.extractCategoryData(collection)

    return {
      regionData,
      regionName,
      regionId,
      categoryNames,
      categoryName
    }
  }
}