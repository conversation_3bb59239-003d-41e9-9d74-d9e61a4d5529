/**
 * Helper utilities for content validation
 */
export class ValidationHelpers {
  /**
   * Check if a string contains unprocessed placeholder patterns
   */
  static hasUnprocessedPlaceholders(text: string): boolean {
    const placeholderPatterns = [
      /\{\{region\}\}/g,
      /\{\{date\}\}/g, 
      /\{\{weekday\}\}/g,
      /\{\{month\}\}/g,
      /\{[^}|]+\|[^}]+\}/g, // Random options like {option1|option2}
    ]
    
    return placeholderPatterns.some(pattern => pattern.test(text))
  }

  /**
   * Check if text contains development placeholders
   * These are common placeholder texts that indicate unfinished or test content
   * that should not be published to production. The validation ensures that
   * articles don't contain temporary content markers left by developers.
   */
  static hasDevelopmentPlaceholders(text: string): boolean {
    const devPlaceholders = [
      'placeholder',
      'TODO',
      'FIXME', 
      'XXX',
      'lorem ipsum'
    ]
    
    const textLower = text.toLowerCase()
    return devPlaceholders.some(placeholder => 
      textLower.includes(placeholder.toLowerCase())
    )
  }

  static isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      // Check for relative URLs
      return url.startsWith('/') && !url.includes('..') && url.length > 1
    }
  }

  static extractLinksFromHtml(htmlContent: string): string[] {
    const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*>/gi
    const links: string[] = []
    let match

    while ((match = linkRegex.exec(htmlContent)) !== null) {
      links.push(match[1])
    }

    return links
  }

  static countH2Headers(htmlContent: string): number {
    const h2Regex = /<h2[^>]*>.*?<\/h2>/gi
    return (htmlContent.match(h2Regex) || []).length
  }

  static hasForbiddenTags(htmlContent: string): string[] {
    const forbiddenTags = ['script', 'style', 'iframe', 'object', 'embed']
    const foundTags: string[] = []

    for (const tag of forbiddenTags) {
      const tagRegex = new RegExp(`<${tag}[^>]*>`, 'gi')
      if (tagRegex.test(htmlContent)) {
        foundTags.push(tag)
      }
    }

    return foundTags
  }

  static isValidNumericId(id: string | number | undefined | null): boolean {
    if (typeof id === 'number') {
      return id > 0
    }
    if (typeof id === 'string') {
      return /^\d+$/.test(id) && parseInt(id, 10) > 0
    }
    return false
  }

  static parseTimeToMinutes(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number)
    return hours * 60 + minutes
  }
}