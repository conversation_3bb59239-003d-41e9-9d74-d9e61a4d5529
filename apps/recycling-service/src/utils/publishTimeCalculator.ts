import type { AutoContentCollection } from '../services/directusService.js'

/**
 * Utility for calculating article publish times based on collection configuration
 *
 * IMPORTANT SAFETY NOTE (addressing <PERSON>'s concern):
 * Articles without publish_time configuration should NOT be automatically published!
 * This could lead to unintended immediate publication at inappropriate times.
 *
 * Use:
 * - calculatePublishTimeForArticleCreation() for actual article creation (strict)
 * - calculatePublishTimeForScheduling() for scheduling validation (lenient)
 * - calculatePublishTime() with explicit options for custom behavior
 */
export class PublishTimeCalculator {
  /**
   * Calculate when an article should be published based on collection config
   *
   * @param collection - Collection configuration
   * @param options - Options for fallback behavior
   * @throws Error if no publish_time is configured and strict mode is enabled
   */
  static calculatePublishTime(
    collection: AutoContentCollection,
    options: { allowFallback?: boolean; strictMode?: boolean } = {}
  ): Date {
    const { allowFallback = false, strictMode = true } = options
    const now = new Date()

    // If collection has specific publish time configured
    if (collection.publish_time) {
      return this.calculateSpecificTime(collection.publish_time, now)
    }

    // SECURITY: No automatic fallback in strict mode
    if (strictMode && !allowFallback) {
      throw new Error(
        `Collection ${collection.id} has no publish_time configured. ` +
        `This could lead to unintended immediate publication. ` +
        `Please configure publish_time or explicitly allow fallback.`
      )
    }

    // Fallback: Use default hour from config (safer than "next hour")
    const fallbackTime = `12:00` // Use noon as safe default
    return this.calculateSpecificTime(fallbackTime, now)
  }

  /**
   * Calculate publish time based on specific time configuration
   */
  private static calculateSpecificTime(publishTime: string, now: Date): Date {
    const result = new Date()
    const [hours, minutes] = publishTime.split(':').map(Number)
    
    // Validate time format
    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      throw new Error(`Invalid publish_time format: ${publishTime}. Expected HH:MM format.`)
    }
    
    result.setHours(hours, minutes, 0, 0)
    
    // If the time has already passed today, schedule for next day
    if (result <= now) {
      result.setDate(result.getDate() + 1)
    }
    
    return result
  }

  /**
   * Calculate next hour publish time (default behavior)
   * @deprecated Use calculatePublishTime with explicit options instead
   */
  private static calculateNextHour(now: Date): Date {
    const nextHour = new Date(now)
    nextHour.setHours(now.getHours() + 1, 0, 0, 0)
    return nextHour
  }

  /**
   * Safe wrapper for article creation that validates publish_time configuration
   * This addresses Christian's concern about articles without publish_time
   */
  static calculatePublishTimeForArticleCreation(collection: AutoContentCollection): Date {
    if (!collection.publish_time) {
      throw new Error(
        `❌ SAFETY CHECK: Collection ${collection.id} has no publish_time configured!\n` +
        `This could lead to unintended immediate publication.\n` +
        `Please configure publish_time in Directus before creating articles.\n` +
        `Collection: ${JSON.stringify({ id: collection.id, title: collection.title || 'Unknown' }, null, 2)}`
      )
    }

    return this.calculateSpecificTime(collection.publish_time, new Date())
  }

  /**
   * Lenient wrapper for scheduling and validation purposes
   * Allows fallback for non-critical operations
   */
  static calculatePublishTimeForScheduling(collection: AutoContentCollection): Date {
    return this.calculatePublishTime(collection, {
      strictMode: false,
      allowFallback: true
    })
  }
}