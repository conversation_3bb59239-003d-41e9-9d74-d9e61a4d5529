import { logger } from '@regionalmedienaustria/microservice-utils'

import type { FeedParams } from '@/services/feedService.js'
import FeedService, { FeedFormatConst } from '@/services/feedService.js'

import type { Request, Response } from 'express'

const checkFeedHealth = async () => {
  try {
    const params = {
      type: FeedFormatConst.json,
      theme: 'bruck-an-der-mur',
      userSegments: ['redakteur', 'oesterreich', 'freier-mitarbeiter'],
      locations: [],
      tags: [],
      staticTags: [],
      includeCommercial: false,
      categories: [],
      limit: 1,
      apiVersion: 1,
    } satisfies FeedParams

    const feed = await FeedService.getFeed(params)

    const isFeedValid = feed && feed.feed && feed.contentType

    if (isFeedValid && JSON.parse(feed.feed).items.length === 0) {
      logger.warn('Feed is valid but has no items')
    }

    return { feed: isFeedValid }
  } catch (error: any) {
    return {
      feed: false,
      error: error.message ?? 'Unknown error',
    }
  }
}

export const healthCheck = async (req: Request, res: Response) => {
  logger.info('Health check called')
  const feedHealth = await checkFeedHealth()

  if (feedHealth.feed) {
    res.status(200).json({ success: true })
  } else {
    logger.error('Feed service health check failed', feedHealth)
    res.status(500).json([feedHealth])
  }
}
