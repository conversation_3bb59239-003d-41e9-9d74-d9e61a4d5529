import { RmaError, app } from '@regionalmedienaustria/microservice-utils'

import parseParams from '@/feedHelper.js'
import FeedService from '@/services/feedService.js'

import type { Request, Response } from 'express'

function getRedirectUrl(req: Request, params: any, version: number): string {
  const pattern = /v(\d+)/
  const replacement = 'v' + version
  const newBaseUrl = req.baseUrl.replace(pattern, replacement)

  let apiBasePath = app.getEnvVar('STAGE_API_BASE_PATH')
  if (app.getEnvVar('NODE_ENV') === 'production') {
    apiBasePath = app.getEnvVar('API_BASE_PATH')
  }

  return `${req.protocol}://${apiBasePath}${newBaseUrl}${req.path}?${params.toString()}`
}

export const getEventFeed = async (req: Request, res: Response) => {
  const params = new URL(req.protocol + '://' + req.hostname + req.originalUrl).searchParams
  params.append('requestedApiVersion', '1')
  const redirectUrl = getRedirectUrl(req, params, 2)
  res.redirect(301, `${redirectUrl}`)
}

export const getArticleFeed = async (req: Request, res: Response) => {
  const params = await parseParams(req)
  if (params.theme === 'event') {
    throw new RmaError({
      message: 'Event feed is not supported. Please use the event feed endpoint.',
      statusCode: 400,
    })
  } else {
    const feed = await FeedService.getFeed(params)
    res.setHeader('content-type', feed.contentType)
    res.status(200).send(feed.feed)
  }
}
