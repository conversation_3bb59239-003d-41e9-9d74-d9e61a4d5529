import { controller } from '@regionalmedienaustria/microservice-utils'
import express from 'express'

import * as feedController from '@/controllers/feedController2.js'
import { feedCacheMiddleware } from '@/utils/cache.js'

const router = express.Router()

router.get(
  '/event/:location/:type',
  feedCacheMiddleware,
  controller.wrapController(feedController.getEventFeed),
)

export default router
