import { controller } from '@regionalmedienaustria/microservice-utils'
import express from 'express'

import * as feedController from '@/controllers/feedController.js'
import { healthCheck } from '@/controllers/healthController.js'
import { feedCacheMiddleware } from '@/utils/cache.js'

const router = express.Router()

router.get(
  '/event/:location/:type',
  feedCacheMiddleware,
  controller.wrapController(feedController.getEventFeed),
)
router.get(
  '/:theme/:type',
  feedCacheMiddleware,
  controller.wrapController(feedController.getArticleFeed),
)

router.get('/healthcheck', controller.wrapController(healthCheck))

export default router
