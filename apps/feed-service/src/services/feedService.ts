import { logger } from '@regionalmedienaustria/microservice-utils'

import { PeiqApiWrapper } from '@/peiqApiWrapper.js'

import JsonService from './jsonService.js'
import RssService, { Category } from './rssService.js'

import type {
  PeiqApiParams,
  PeiqApiWrapper as PeiqApiWrapperType,
} from '@regionalmedienaustria/peiq-api-wrapper'

export const FeedFormatConst = { rss: 'rss', json: 'json' } as const

export type FeedFormatType = keyof typeof FeedFormatConst

export interface FeedParams {
  type: FeedFormatType
  theme: string
  userSegments?: string[]
  locations?: string[]
  tags?: string[]
  staticTags?: string[]
  categories?: string[]
  includeCommercial: boolean
  limit?: number
  apiVersion: number
}

export default class FeedService {
  private static async createFeed(obj: FeedParams): Promise<{ feed: string; contentType: string }> {
    let articles: { data: any[] } = { data: [] }
    const requestLocations: PeiqApiWrapperType.Location[] = []
    if (obj.locations && obj.locations.length) {
      for (let i = 0; i < obj.locations.length; i = i + 1) {
        requestLocations.push(
          await PeiqApiWrapper.LocationApi.getLocationObjByName(obj.locations[i]),
        )
      }
    }
    const categoryIds = FeedService.getCatIdsFromName(obj.categories)

    if (obj.theme !== 'event') {
      const location = await PeiqApiWrapper.LocationApi.getLocationObjByName(obj.theme)
      requestLocations.unshift(location)
      for (let i = 0; i < requestLocations.length; i = i + 1) {
        try {
          const apiParams: PeiqApiParams = {
            locationId: requestLocations[i].id,
            userSegments: obj.userSegments,
            tags: obj.tags,
            staticTags: obj.staticTags,
            categoryIds: categoryIds,
            pageSize: obj.limit,
          }
          const locationArticles = await PeiqApiWrapper.ArticleApi.fetchArticles(
            apiParams,
            obj.includeCommercial,
            false,
            false,
            false,
          )
          articles.data.push(...locationArticles.data)
        } catch (error: any) {
          logger.error(`${error.code}: ${error.message}`)
        }
      }
      articles.data.sort((a, b) => {
        const datA = new Date(a.published)
        const datB = new Date(b.published)
        return datA < datB ? 1 : datA > datB ? -1 : 0
      })
    } else if (obj.theme === 'event') {
      for (let i = 0; i < requestLocations.length; i = i + 1) {
        try {
          const today = new Date().toJSON().slice(0, 10)
          const apiParams: PeiqApiParams = {
            locationId: requestLocations[i].id,
            userSegments: obj.userSegments,
            staticTags: obj.staticTags,
            pageSize: obj.limit,
            page: 1,
            eventItemDateStartFrom: today,
          }
          const locationEvents = await PeiqApiWrapper.EventApi.fetchEvents(
            apiParams,
            obj.includeCommercial,
            false,
          )
          articles.data.push(...locationEvents.data)
        } catch (error: any) {
          logger.error(`${error.code}: ${error.message}`)
        }
      }
      const today = new Date().toJSON().slice(0, 10)
      FeedService.sortEvents(articles.data, today)
    }

    articles.data = articles.data.slice(0, obj.limit)

    let feed = ''
    if (obj.type === FeedFormatConst.rss) {
      feed = await RssService.transformArticles(articles.data, obj.theme, obj.locations)
    } else {
      feed = await JsonService.transformArticles(
        articles.data,
        obj.theme,
        obj.apiVersion,
        obj.locations,
      )
    }
    const contentType = obj.type === FeedFormatConst.rss ? 'application/xml' : 'application/json'
    return { feed, contentType }
  }

  public static async getFeed(params: FeedParams): Promise<{ feed: string; contentType: string }> {
    const feed = await FeedService.createFeed(params)

    return feed
  }

  private static getCatIdsFromName(categories: string[] | undefined): number[] {
    const catIds: number[] = []

    if (!categories) {
      return []
    }
    categories.forEach((catName) => {
      const foundEntry = Object.entries(Category).find(
        (entry) => catName.toLowerCase() === entry[1].toString().toLowerCase(),
      )
      if (foundEntry) {
        catIds.push(parseInt(foundEntry[0]))
      }
    })
    return catIds
  }

  /**
   * Sort a list of event dates and sort by eventitem_date desc.
   *
   * @param data List of Peiq events
   * @param today datestring. Format: yyyy-mm-dd
   */
  private static sortEvents(events: any, today: string) {
    events.forEach((event: any) => {
      if (event.eventitem_dates.length > 1) {
        event.eventitem_dates = event.eventitem_dates.filter((eventDate: any) => eventDate >= today)
      }
    })

    events.sort(function (a: any, b: any) {
      return a.eventitem_dates[0] > b.eventitem_dates[0]
        ? 1
        : a.eventitem_dates[0] < b.eventitem_dates[0]
        ? -1
        : 0
    })
  }
}
