import RSS from 'rss'

import { PeiqApiWrapper } from '@/peiqApiWrapper.js'

export enum Category {
  '0ist leer',
  'Politik',
  'Lokales',
  'Sport',
  'Wirtschaft',
  'Leute',
  'ungelistet',
  '7ist leer',
  'Profis aus der Region',
  '9ist leer',
  'Gedanken',
  'Geschenketipps',
  'Gesundheit',
  'Freizeit',
  'Bauen & Wohnen',
  'Mobilität',
  'Reisen',
  'Mein Profi',
  'Regionauten-Community',
}

enum EventCategory {
  '0ist leer',
  'Ausstellung',
  'Ball/Fest',
  'Brauchtum/Kultur',
  'Gesundheit',
  '<PERSON><PERSON><PERSON>',
  'Kinder/Jugend',
  'Konzert/Bühne/Kino',
  'Literatur/Buch',
  'Markt/Basar',
  'Sport',
  'Workshop/Seminar/Infoveranstaltung',
  '12ist leer',
  'Sonstiges',
}

export default class RssService {
  public static async transformArticles(articles: any[], theme: string, locations?: string[]) {
    const feed = new RSS({
      title: `Neueste ${theme === 'event' ? 'Veranstaltungen' : 'Beiträge'} aus ${
        theme === 'event' ? '' : theme + ', '
      }${locations && locations?.length ? locations.join(', ') : ''} auf MeinBezirk.at`,
      feed_url: `https://www.meinbezirk.at/${theme}/rss`,
      site_url: 'https://www.meinbezirk.at',
      description: `Neueste ${theme === 'event' ? 'Veranstaltungen' : 'Beiträge'} aus ${
        theme === 'event' ? '' : theme + ', '
      }${
        locations && locations?.length ? locations.join(', ') : ''
      } auf MeinBezirk.at. Dieser Feed wird regelmäßig aktualisiert und enthält alle Beiträge, die kürzlich erstellt oder aktualisiert wurden.`,
      custom_namespaces: {
        atom: 'http://www.w3.org/2005/Atom',
        media: 'http://search.yahoo.com/mrss/',
      },
    })

    for (let i = 0; i < articles.length; i = i + 1) {
      const article = articles[i]
      const users = await PeiqApiWrapper.UserApi.fetchUsers([article.user_id])

      feed.item({
        title: article.title,
        url: article.url,
        custom_elements: [
          { 'dc:creator': `${users?.data[0].full_name}` },
          {
            'media:content': [
              {
                _attr: {
                  url: `${article.hero_image?.url || article.thumbnail?.url}`,
                  type: 'image/jpeg',
                  medium: 'image',
                },
              },
            ],
          },
        ],
        date: article.updated,
        guid: `https://www.meinbezirk.at/${theme}/rss${article.id}`,
        description: RssService.filterDescription(
          article.text_elements?.text || article.description,
        ),
        categories:
          theme === 'event'
            ? [EventCategory[article.category_id]]
            : [Category[article.category_id]],
        //enclosure: { url: 'url ro enclosure' },
      })
    }

    return feed.xml({ indent: true })
  }

  private static filterDescription(description: string): string {
    const startIndex = description.toLowerCase().indexOf('onclick="')
    if (startIndex !== -1) {
      const endIndex = description.indexOf('"', startIndex + 9)
      const retString =
        description.substring(0, startIndex) +
        description.substring(endIndex + 1, description.length)
      return retString
    }
    return description
  }
}
