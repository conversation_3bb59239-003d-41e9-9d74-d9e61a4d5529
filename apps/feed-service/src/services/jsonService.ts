import { logger } from '@regionalmedienaustria/microservice-utils'
import moment from 'moment-timezone'

import { PeiqApiWrapper } from '@/peiqApiWrapper.js'

interface FeedAuthor {
  name?: string
  url?: string
  avatar?: string
}

interface Hub {
  type: string
  url: string
}

interface FeedAttachement {
  url: string
  mime_type: string
  title?: string
  size_in_bytes?: number
  duration_in_seconds?: number
}

interface FeedItem {
  id: string
  url?: string
  external_url?: string
  title?: string
  content_html?: string
  content_text?: string
  summary?: string
  image?: string
  banner_image?: string
  date_published?: string //Example: 2010-02-07T14:04:00-05:00
  date_modified?: string
  authors?: FeedAuthor[]
  tags?: string[]
  language?: string
  attachements?: FeedAttachement[]
  _event_category?: string | { id: number; name: string }
  _event_dates?: string[]
  _event_locations?: EventLocation[]
}

interface JsonFeed {
  version: string
  title: string
  home_page_url?: string
  feed_url?: string
  description?: string
  user_comment?: string
  next_url?: string
  icon?: string
  favicon?: string
  authors?: FeedAuthor[]
  language?: string
  expired?: boolean
  hubs?: Hub[]
  items: FeedItem[]
}

interface EventLocation {
  type: string
  name: string
  street: string
  city: string
  zipcode: string
  lat: number
  lng: number
  url: string
}

const ApiVersions = {
  VERSION1: 1,
  VERSION2: 2,
}

export default class JsonService {
  private static topLevel(theme: string, locations?: string[]): JsonFeed {
    const feed: JsonFeed = {
      version: 'https://jsonfeed.org/version/1.1',
      title: `Neueste ${theme === 'event' ? 'Veranstaltungen' : 'Beiträge'} aus ${
        theme === 'event' ? '' : theme + ', '
      }${locations && locations?.length ? locations.join(', ') : ''} auf MeinBezirk.at`,
      feed_url: `https://www.meinbezirk.at/${theme}${
        locations && locations?.length ? '?locations=' + locations.join(',') : ''
      }`,
      home_page_url: 'https://www.meinbezirk.at',
      language: 'de-AT',
      description: `Neueste ${theme === 'event' ? 'Veranstaltungen' : 'Beiträge'} aus ${
        theme === 'event' ? '' : theme + ', '
      }${
        locations && locations?.length ? locations.join(', ') : ''
      } auf MeinBezirk.at. Dieser Feed wird regelmäßig aktualisiert und enthält alle Beiträge, die kürzlich erstellt oder aktualisiert wurden.`,
      icon: 'https://storage.googleapis.com/rma-static-content/rma-logo-512.jpg',
      favicon: 'https://storage.googleapis.com/rma-static-content/rma_favicon.png',
      items: [],
    }
    return feed
  }

  private static async addItem(feed: JsonFeed, article: any, theme: string, apiVersion: number) {
    feed.items.push({
      id: article.id,
      title: article.title,
      url: article.url,
      content_html: article.text_elements?.text || article.description,
      summary: article.text_elements?.kicker,
      image: article.hero_image?.url || article.thumbnail?.url,
      date_published: article.published,
      date_modified: article.updated,
      authors: await this.createAuthor(article.user_id), //needs to be added
      tags: article.tags,
      language: 'de-AT',
      ...(theme === 'event' && {
        _event_category: await this.getEventCategory(article.category_id, apiVersion),
      }),
      ...(article.eventitem_dates && {
        _event_details: await this.getEventdetails(
          article.eventitem_dates,
          article.event_locations,
        ),
      }),
    })
  }

  private static async getEventCategory(categoryId: number, apiVersion: number) {
    let eventCategory = await PeiqApiWrapper.EventApi.fetchEventCategory(categoryId)
    if (apiVersion === ApiVersions.VERSION1) {
      return eventCategory
    } else {
      return {
        id: categoryId,
        name: eventCategory,
      }
    }
  }

  private static async getEventdetails(peiqDates: string[], peiqLocations: any[]) {
    const dates = await this.transformEventDates(peiqDates)
    return {
      event_locations: peiqLocations,
      event_dates: dates,
    }
  }

  private static async transformEventDates(peiqDates: string[]) {
    const eventDates: string[] = []
    try {
      peiqDates.forEach((date) => {
        // check if date was set with timespan
        // 2023-04-23T15:00:00E2023-04-23T18:00:00
        if (date.indexOf('E') > -1) {
          const eventSpan = date.split('E')
          eventDates.push(moment(eventSpan[0]).tz('Europe/Vienna').format())
        } else {
          eventDates.push(moment(date).tz('Europe/Vienna').format())
        }
      })
    } catch (error) {
      logger.error(error)
    }
    return eventDates
  }

  private static async createAuthor(userId: number): Promise<FeedAuthor[]> {
    const users = await PeiqApiWrapper.UserApi.fetchUsers([userId])
    let author: FeedAuthor = {}
    if (users?.data?.length) {
      author.avatar = users.data[0].avatar?.url
      author.name = users.data[0].full_name
    }
    return [author]
  }

  public static async transformArticles(
    articles: any[],
    theme: string,
    apiVersion: number,
    locations?: string[],
  ): Promise<string> {
    const feed = this.topLevel(theme, locations)

    for (let i = 0; i < articles.length; i = i + 1) {
      await JsonService.addItem(feed, articles[i], theme, apiVersion)
    }

    return JSON.stringify(feed)
  }
}
