import { requestUtil } from '@regionalmedienaustria/microservice-utils'

import { FeedFormatConst } from './services/feedService.js'

import type { FeedFormatType } from './services/feedService.js'
import type { Request } from 'express'

const parseParams = async (req: Request, isEvent?: boolean) => {
  const type = (
    requestUtil.getParameter(req, 'type', FeedFormatConst.rss) !== FeedFormatConst.rss
      ? FeedFormatConst.json
      : FeedFormatConst.rss
  ) as FeedFormatType
  const theme = isEvent ? 'event' : requestUtil.getParameter(req, 'theme')
  const userSegments = requestUtil.getParameterAsArray<string>(req, 'userSegments', [
    'redakteur',
    'oesterreich',
    'freier-mitarbeiter',
  ])
  const locations: string[] = []
  let limit = requestUtil.getParameter(req, 'limit', 25)

  let requestedApiVersion = requestUtil.getParameter(req, 'requestedApiVersion', 1)
  if (isEvent) {
    requestedApiVersion = requestUtil.getParameter(req, 'requestedApiVersion', 2)
    locations.push(requestUtil.getParameter(req, 'location'))
    limit = requestUtil.getParameter(req, 'limit', 75)
  }
  locations.push(...requestUtil.getParameterAsArray<string>(req, 'locations', []))
  const tags = requestUtil.getParameterAsArray<string>(req, 'tags')
  const staticTags: string[] = requestUtil.getParameterAsArray<string>(req, 'staticTags')
  const categories: string[] = requestUtil.getParameterAsArray<string>(req, 'categories')
  const includeCommercial: boolean = requestUtil.getParameter(req, 'includeCommercial', false)

  const apiVersion = +requestedApiVersion

  return {
    type,
    theme,
    userSegments,
    locations,
    tags,
    staticTags,
    includeCommercial,
    categories,
    limit,
    apiVersion,
  }
}

export default parseParams
