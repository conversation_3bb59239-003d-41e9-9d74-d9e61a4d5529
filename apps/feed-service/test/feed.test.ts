import { describe, expect, it } from 'vitest'

import type { FeedParams } from '@/services/feedService.js'
import FeedService, { FeedFormatConst } from '@/services/feedService.js'

describe('feed', () => {
  describe('getFeed', () => {
    it('return feed', async () => {
      const params: FeedParams = {
        theme: 'event',
        type: FeedFormatConst.json,
        userSegments: ['redakteur'],
        includeCommercial: false,
        limit: 5,
        apiVersion: 2,
      }
      const result = await FeedService.getFeed(params)
      expect(result.feed, 'articles found')
    })
  })
})
