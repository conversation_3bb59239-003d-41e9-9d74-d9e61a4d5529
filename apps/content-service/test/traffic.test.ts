import { describe, expect, it } from 'vitest'

import TrafficService from '../src/services/trafficService.js'

describe('traffic-service', () => {
  describe('get webcam data', () => {
    it('should return webcam data', async () => {
      const webcamData = await TrafficService.getWebcamData('ooe')
      expect(webcamData.webcameras.highwayCams.length > 0, 'webcam data found')
    })
  })
})

describe('traffic-service', () => {
  describe('get webcam data', () => {
    it('should return webcam data', async () => {
      const webcamData = await TrafficService.getWebcamHtml('ooe')
      //expect(webcamData.webcameras.length > 0, 'webcam data found')
      expect(webcamData.html && webcamData.html.length > 0, 'html found')
    })
  })
})
