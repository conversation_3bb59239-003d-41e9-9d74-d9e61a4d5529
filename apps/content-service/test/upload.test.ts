// voteableUpload.test.ts
import { createItem } from '@rma-mono/directus-client'
import { describe, expect, it, vi, beforeEach } from 'vitest'

import { VoteableUploadService } from '../src/services/voteableUploadService.js'
import { VotingChallengeService } from '../src/services/votingChallengeService.js'
import { uploadClient } from '../src/utils/directus/client.js'

// Mocks
vi.mock('../src/services/votingChallengeService.js', () => ({
  VotingChallengeService: {
    getChallenge: vi.fn(),
  },
}))

vi.mock('../src/utils/directus/client.js', () => ({
  uploadClient: {
    request: vi.fn(),
  },
}))

vi.mock('../src/utils/directus/uploadFile.js', () => ({
  uploadFileToDirectus: vi.fn(),
}))

vi.mock('@rma-mono/directus-client', () => ({
  createItem: vi.fn(() => 'mocked-query'),
  readItem: vi.fn(),
  updateItem: vi.fn(),
  aggregate: vi.fn(),
}))

// Direkter Spy auf die Implementierung von voteableUploadService statt komplexer Mocks
// Dies gibt uns mehr Kontrolle für diesen spezifischen Test

describe('voteableUpload-service', () => {
  beforeEach(() => {
    vi.resetAllMocks()
    console.log('--------------------------------------------')
    console.log('Mocks zurückgesetzt, neuer Test startet')
  })

  describe('uploadItem with ordered JSON', () => {
    it('should maintain the field order according to fieldOrder array', async () => {
      // 1. Mock voting challenge und uploadItem-Methode
      const mockVotingChallenge = {
        id: 123,
        title: 'Test Challenge',
        upload_enabled: true,
        mediatype: 'image',
        further_information_fields: [],
        contact_fields: [],
        personal_data_fields: [],
        personal_data_necessary: false,
        checkboxes: [],
        show_user_description: false,
        title_input: true,
      }

      // Mock für VotingChallengeService.getChallenge
      vi.mocked(VotingChallengeService.getChallenge).mockResolvedValue(mockVotingChallenge as any)
      console.log('VotingChallengeService.getChallenge gemockt')

      // 2. Mock uploadClient.request
      vi.mocked(uploadClient.request).mockResolvedValue({ id: 'upload-id-123' })

      // 3. Mock createItem
      vi.mocked(createItem).mockReturnValue('mocked-query')

      // 4. Create test data with fieldOrder - NOTE: Using __fieldOrder to match the actual implementation
      const testOtherData: Record<string, any> = {
        ort: 'Graz',
        plz: 8020,
        email: '<EMAIL>',
        title: 'New Idea',
        anrede: 'Herr',
        vorname: 'Max',
        nachname: 'Mustermann',
        description: 'Test Test',
        telefonnummer_1: '912837927834',
        telefonnummer_2: '912837927834',
        upload_fileinfo: {
          name: 'CleanShot 2025-03-04 at 12.06.59.mp4',
          size: '5478335',
          type: 'video/mp4',
        },
        'name-der-ideedes-unternehmens': 'Max Mustermann',
        'unternehmenssitzadresse-der-kontaktperson-muss-in-n-liegen': 'RMA',
        'Ja,-ich-m&ouml;chte-bei-&bdquo;120-Sekunden-2024-der-Ideendschungel-ruft&ldquo;-zu-den-Teilnahmebedingungen-teilnehmen.':
          true,
      }

      // Add a specific __fieldOrder that matches our expected order
      testOtherData.__fieldOrder = JSON.stringify([
        'name-der-ideedes-unternehmens',
        'unternehmenssitzadresse-der-kontaktperson-muss-in-n-liegen',
        'plz',
        'ort',
        'description',
        'anrede',
        'vorname',
        'nachname',
        'telefonnummer_1',
        'email',
        'telefonnummer_2',
        'Ja,-ich-m&ouml;chte-bei-&bdquo;120-Sekunden-2024-der-Ideendschungel-ruft&ldquo;-zu-den-Teilnahmebedingungen-teilnehmen.',
      ])

      const requestData = {
        title: 'Test Upload',
        challengeSlug: 'test-challenge',
        challengeId: '123',
        state: 'Steiermark',
        otherData: JSON.stringify(testOtherData),
        email: '<EMAIL>',
        // Add the missing required fields:
        fileContentType: 'video/mp4',
        originalFileName: 'test-video.mp4',
      }

      // Call the real method:
      await VoteableUploadService.uploadItem({ requestData })

      // Verify that createItem was called once
      expect(createItem).toHaveBeenCalledTimes(1)

      // Check the createItem call parameters
      const createItemArgs = vi.mocked(createItem).mock.calls[0] as unknown as [
        string,
        Record<string, any>,
      ]
      const createItemData = createItemArgs[1] as { other_data: Record<string, any> }

      // Check that other_data exists
      expect(createItemData).toHaveProperty('other_data')
      console.log('other_data in createItem:', createItemData.other_data)

      // Expected order based on fieldOrder
      const expectedOrderedFields = [
        'title',
        'name-der-ideedes-unternehmens',
        'unternehmenssitzadresse-der-kontaktperson-muss-in-n-liegen',
        'plz',
        'ort',
        'description',
        'anrede',
        'vorname',
        'nachname',
        'telefonnummer_1',
        'email',
        'telefonnummer_2',
        'Ja,-ich-m&ouml;chte-bei-&bdquo;120-Sekunden-2024-der-Ideendschungel-ruft&ldquo;-zu-den-Teilnahmebedingungen-teilnehmen.',
        'upload_fileinfo',
      ]

      // Instead of checking the exact string order, check that the field order matches the array
      const keys = Object.keys(createItemData.other_data)

      // Remove __fieldOrder from the keys for this check
      const dataKeys = keys.filter((key) => key !== '__fieldOrder')

      // Log the actual order for debugging
      console.log('Actual field order:', dataKeys)
      console.log('Expected order:', expectedOrderedFields)

      // Check that all expected fields exist in the data
      for (const field of expectedOrderedFields) {
        if (field === 'title' || field === 'upload_fileinfo') {
          // These are special fields that might be handled differently
          continue
        }
        expect(createItemData.other_data).toHaveProperty(field)
      }

      // Check the relative order of fields
      // For each consecutive pair of fields in expectedOrderedFields, check that they appear
      // in the same relative order in dataKeys (if both exist)
      for (let i = 0; i < expectedOrderedFields.length - 1; i++) {
        const currentField = expectedOrderedFields[i]
        const nextField = expectedOrderedFields[i + 1]

        const currentIndex = dataKeys.indexOf(currentField)
        const nextIndex = dataKeys.indexOf(nextField)

        // Only compare if both fields exist in the data
        if (currentIndex !== -1 && nextIndex !== -1) {
          expect(
            currentIndex < nextIndex,
            `Expected '${currentField}' to appear before '${nextField}' in the field order`,
          ).toBe(true)
        }
      }
    })
  })
})
