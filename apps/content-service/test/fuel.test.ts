import { describe, expect, it } from 'vitest'

import { getStateNameByValue } from '../src/data/fuel/states.js'
import EControlService from '../src/services/eControlService.js'

describe('fuel-service', () => {
  describe('get stations by address', () => {
    it('should return e-control stations', async () => {
      const stations = await EControlService.getStationsByAddress(
        46.64122367,
        13.18936045,
        'address',
      )
      expect(stations, 'stations found')
    })
  })
})

describe('fuel-service', () => {
  describe('get stations by region type district', () => {
    it('should return e-control stations', async () => {
      const result = await EControlService.getStationsByRegion('310', 'PB', 'region')
      let length = 0
      if (result.dieStations && result.supStations) {
        length = result.dieStations.length + result.supStations.length
      }
      expect(length > 0, 'stations found')
    })
  })
})

describe('fuel-service', () => {
  describe('get stations by district name', () => {
    it('should return e-control stations', async () => {
      const result = await EControlService.getStationsByDistrictName('graz_umgebung')
      let length = 0
      if (result.dieStations && result.supStations) {
        length = result.dieStations.length + result.supStations.length
      }
      expect(length > 0, 'stations found')
    })
  })
})

describe('fuel-service', () => {
  describe('get stations by region type state', () => {
    it('should return e-control stations', async () => {
      const result = await EControlService.getStationsByRegion(
        '1',
        'BL',
        `state-${getStateNameByValue(1)}`,
      )
      let length = 0
      if (result.dieStations && result.supStations) {
        length = result.dieStations.length + result.supStations.length
      }
      expect(length > 0, 'stations found')
    })
  })
})

describe('fuel-service', () => {
  describe('get all stations', () => {
    it('should return 2x45 e-control stations', async () => {
      const stations = await EControlService.getAllStations()
      expect(stations.dieStations?.length === 45, 'stations found')
      expect(stations.supStations?.length === 45, 'stations found')
    })
  })
})
