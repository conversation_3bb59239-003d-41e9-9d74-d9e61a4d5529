import { describe, it, expect } from 'vitest'

import { AirQualityService } from '@/services/airQualityService.js'

describe('Air Quality Service', () => {
  // Tests are run without previously saved cache data

  it('should correctly fetch air quality data for Tirol', async () => {
    const result = await AirQualityService.getAirQualityInfo('tirol')

    expect(result).toBeDefined()
    expect(result.region).toBe('tirol')

    // Adjusted: We expect at least 3 stations - this is the minimum we should receive
    expect(result.data.length).toBeGreaterThan(2)
    console.log(`Found ${result.data.length} stations for Tirol`)

    if (result.data.length > 0) {
      expect(result.data[0]).toHaveProperty('city')
      expect(result.data[0]).toHaveProperty('aqi')

      // Log the found stations for debugging
      console.log('Stations found for Tirol:')
      result.data.forEach((station) => {
        console.log(
          `- ${station.city?.name || 'Unknown'} (${station.city?.geo?.join(', ') || 'No geo'})`,
        )
      })
    }
  })

  it('should correctly fetch air quality data for Vienna', async () => {
    const result = await AirQualityService.getAirQualityInfo('wien')
    expect(result.data.length).toBeGreaterThan(1)

    console.log(`Found ${result.data.length} stations for Vienna`)
  })
})
