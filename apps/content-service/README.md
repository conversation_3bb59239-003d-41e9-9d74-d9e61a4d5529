# Content Service

## Overview

The `content-service` is the backend microservice that handles integration of different content sources for the [rma-widgets](../rma-widgets/README.md)

It serves as a bridge between external APIs + the CMS (Directus) and various frontend applications (including the main PEIQ system).

## Service Architecture

### Directory Structure

- `/src` - Source code for the service
  - `/cache` - Cache adapters
  - `/controllers` - Controller layer for handling requests
  - `/services` - Service layer for handling business logic
  - `/utils` - Utility functions
- `/test` - Test files and fixtures
- `/errors` - Custom error classes

### Key Components

- **API Layer** - RESTful endpoints for content operations
- **Cache Management** - Integration with Redis via `cache-util` package
- **Content Transformation** - Utilities for processing and formatting content
- **Authentication & Authorization** - Security controls for content access

## Integration Points

- **Directus CMS** - Primary content source
- **Frontend Applications** - Content consumers
- **Google Cloud Storage** - Media asset storage
- **Redis Cache** - Performance optimization

## Subservices

The content-service provides several specialized subservices, each handling different types of content and integrations:

| Subservice         | Description                                                                    | Consumers              | Data Sources                     |
| ------------------ | ------------------------------------------------------------------------------ | ---------------------- | -------------------------------- |
| AdsConfig          | Manages advertisement configuration.                                           | PEIQ ads.txt           | Directus                         |
| AdventCalendar     | Manages the Advent Calendar content.                                           | Advent Calendar widget | Directus                         |
| Fuel               | Provides fuel price information for gas stations.                              | Fuel widget            | E-Control API                    |
| Gallery Sponsoring | Manages exclusions of automatic ads in defined galleries.                      | PEIQ galleries         | Directus                         |
| Pharmacy           | Provides information about pharmacies, including on-duty/emergency pharmacies. | Pharmacy widget        | Apothekenkammer API              |
| RSS2Ads            | Converts RSS feeds into advertisement-compatible formats.                      | Rss2Ads widget         | Directus w/ an external RSS feed |
| Traffic            | Delivers traffic information with live camera feeds.                           | Traffic widget(s)      | Asfinag API                      |
| Voting             | Manages voting and poll functionality.                                         | Voting widget(s)       | Directus                         |

### Voting

The voting services are used to manage voting and poll functionality, like:

- Showing an upload form
- Showing a single voteable item
- Showing a list of voteable items
- Showing the results of a voting challenge

#### IP Blocking

To ensure fair voting, we block IPs that have voted for a specific voting challenge within the last 24 hours.

This is done by using a Redis & Memory Cache to save the voting challenge + hash of the IP address and a timestamp of the last vote.

We use both types of caches to avoid concurrent (spam) votes from the same IP address within the same second.
