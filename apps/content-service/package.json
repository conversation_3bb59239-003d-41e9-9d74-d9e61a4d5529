{"name": "@rma-mono/content-service", "version": "1.0.0", "description": "This microservice serves all backend needs for our content integrations into PEIQ", "type": "module", "license": "ISC", "author": "", "main": "src/index.ts", "scripts": {"build": "tsc --p ./tsconfig.json && tsc-alias -p ./tsconfig.json", "debug": "source $HOME/.nvm/nvm.sh; nvm use && run-p lint && run-p build && main=lib/src/index.js NODE_ENV=local nodemon -e tsc lib/src/index.js && tsc-alias src/index.ts --inspect", "dev": "source $HOME/.nvm/nvm.sh; nvm use && NODE_ENV=local tsx watch src/index.ts", "lint": "eslint \"**/*.{ts,tsx,js,jsx}\"", "lint:fix": "npm run lint -- --fix", "start": "node lib/src/index.js", "pretest": "run-p typecheck lint", "test": "vitest", "typecheck": "tsc --noEmit"}, "dependencies": {"@google-cloud/storage": "^6.9.5", "@regionalmedienaustria/cache-util": "^2.3.1-alpha.0", "@regionalmedienaustria/microservice-utils": "^3.0.0-alpha.7", "@rma-mono/cache-clearing-util": "^1.0.0", "@rma-mono/directus-client": "^1.0.0", "@rma-mono/rma-regions": "^1.0.0", "@rma-mono/voting-challenge-uploader": "^1.0.0", "@turf/bbox": "^7.2.0", "@turf/bbox-polygon": "^7.2.0", "@turf/buffer": "^7.1.0", "@turf/helpers": "^7.1.0", "@turf/points-within-polygon": "^7.1.0", "axios": "^1.6.7", "body-parser": "^1.19.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "fast-glob": "^3.2.2", "form-data": "^4.0.0", "formdata-node": "^5.0.1", "geojson": "^0.5.0", "google-auth-library": "^9.15.0", "lodash": "^4.17.15", "multer": "^1.4.5-lts.1", "normalize-path": "^3.0.0", "path": "^0.12.7", "path-to-regexp": "^6.1.0", "promise": "^8.3.0", "reflect-metadata": "^0.1.13", "rss-parser": "^3.13.0", "zod": "^3.22.4"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.20.2", "@babel/node": "^7.20.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.20.2", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@babel/register": "^7.18.9", "@rma-mono/eslint-config-backend": "^1.13", "@types/cors": "^2.8.12", "@types/express": "^4.17.14", "@types/lodash": "^4.14.189", "@types/multer": "^1.4.7", "@types/node": "^14.18.33", "@types/sinon": "^10.0.0", "babel-plugin-transform-typescript-metadata": "^0.3.2", "copyfiles": "^2.3.0", "google-artifactregistry-auth": "^3.0.2", "helmet": "^6.0.1", "nodemon": "^2.0.20", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "ts-node": "^8.10.2", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.7", "tsconfig-paths": "^4.2.0", "tsx": "^3.12.10", "typescript": "5.1.6", "vitest": "^2.0.5"}, "engines": {"node": ">=20.0.0"}}