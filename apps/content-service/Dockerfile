FROM node:22.11.0-bullseye-slim

# Create and change to the app directory.
WORKDIR /usr/src/app

# Assuming the Docker build is executed from the monorepo root directory

# Copy application dependency manifests to the container image.
COPY package*.json .
COPY gcp-sa-key.json .
COPY .npmrc .

# Copy shared packages
COPY packages ./packages
COPY ci/build-packages.sh ./ci/build-packages.sh

COPY apps/content-service ./apps/content-service

# Install dependencies.
# If you add a package-lock.json speed your build by switching to 'npm ci'.
# RUN npm ci --only=production
RUN export GOOGLE_APPLICATION_CREDENTIALS=./gcp-sa-key.json
# RUN npm pkg delete scripts.prepare && npm run gcpInstall --omit=dev
RUN npm pkg delete scripts.prepare && npm run gcpInstall

RUN npm run build -w=apps/content-service

# Make port 8080 available
EXPOSE 8080

# Run the web service on container startup.
CMD ["npm","run", "start", "-w=apps/content-service"]
