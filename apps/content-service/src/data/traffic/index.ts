const HIGHWAYS = [
  { nameID: 'A07', title: 'A7 Mühlkreisautobahn' },
  { nameID: 'A01', title: 'A1 Westautobahn' },
  { nameID: 'A02', title: 'A2 Südautobahn' },
  { nameID: 'A22', title: 'A22 Donauuferautobahn' },
  { nameID: 'A04', title: 'A4 Ostautobahn' },
  { nameID: 'A05', title: 'A5 Weinviertelautobahn' },
  { nameID: 'A08', title: 'A8 Innkreisautobahn' },
  { nameID: 'A09', title: 'A9 Pyhrnautobahn' },
  { nameID: 'A11', title: 'A11 Karawanken Autobahn' },
  { nameID: 'A25', title: 'A25 Welser Autobahn' },
  { nameID: 'S01', title: 'S1 Wiener Außenring Schnellstraße' },
  { nameID: 'S03', title: 'S3 Weinviertler Schnellstraße' },
  { nameID: 'S33', title: 'S33 <PERSON>hn<PERSON>straße' },
  { nameID: 'S05', title: 'S5 Stockerauer Schnellstraße' },
]

const DATA_TRAFFIC_MAP = {
  freistadt_linz: [
    { id: '2037', value: 'bei Salzburger Straße 2' },
    { id: '17001', value: '' },
    { id: '55969', value: '' },
    { id: '55971', value: '' },
    { id: '16992', value: '' },
    { id: '16986', value: '' },
    { id: '16989', value: 'Uhrfahr' },
    { id: '16977', value: '' },
    { id: '7751', value: '' },
    { id: '2295', value: '' },
    { id: '2294', value: '' },
    { id: '16974', value: '' },
    { id: '2066', value: 'bei voestalpine in Linz' },
    { id: '2065', value: 'bei voestalpine in Linz' },
    { id: '2287', value: 'bei Wienerstraße in Linz 2' },
    { id: '2288', value: 'bei Wienerstraße in Linz 1' },
    { id: '2051', value: '' },
    { id: '2052', value: '' },
    { id: '2058', value: '' },
    { id: '2272', value: 'bei Salzburger Straße 5' },
    { id: '2276', value: 'bei Salzburger Straße 4' },
    { id: '2038', value: 'bei Salzburger Straße 3' },
    { id: '2044', value: 'bei Salzburger Straße 1' },
    {
      id: '16963',
      value: 'bei Auffahrt von A7 auf A1 Richtung Salzburg & Wien',
    },
    { id: '16965', value: 'bei Franzosenhausweg' },
    { id: '16968', value: 'bei Franzosenhausweg' },
    { id: '17819', value: 'Ab- & Auffahrt Franzosenhausweg' },
    { id: '17822', value: 'vor Franzosenhausweg 2' },
    { id: '16971', value: 'vor Franzosenhausweg 1' },
    { id: '88440', value: '' },
    { id: '88446', value: '' },
    { id: 'LOOE_a47KAM3', value: '' }, // Federal Camera
    { id: 'LOOE_a608KAM1', value: '' }, // Federal Camera
    { id: 'LOOE_a603KAM1', value: '' }, // Ferderal Camera
  ],
  ooe_a1: [
    { id: '79371', value: '' },
    { id: '943', value: '' },
    { id: '9996', value: '' },
    { id: '11871', value: '' },
    { id: '6761', value: '' },
    { id: '1862', value: '' },
    { id: '4295', value: '' },
    { id: '14751', value: '' },
    { id: '2119', value: '' },
    { id: '9083', value: '' },
    { id: '87009', value: '' },
    { id: '58980', value: '' },
    { id: '55891', value: '' },
    { id: '55921', value: '' },
    { id: '3152', value: '' },
    { id: '111287', value: '' },
    { id: '3146', value: '' },
    { id: '3143', value: '' },
  ],
  ooe_a21: [
    { id: '79954', value: '' },
    { id: '72695', value: '' },
    { id: '72623', value: '' },
    { id: '80006', value: '' },
  ],
  ooe_a2: [
    { id: '111426', value: '' },
    { id: '111577', value: '' },
    { id: '9970', value: '' },
    { id: '93103', value: '' },
    { id: '93115', value: '' },
    { id: '111555', value: '' },
    { id: '111601', value: '' },
    { id: '3635', value: '' },
    { id: '111543', value: '' },
    { id: '4191', value: '' },
    { id: '3671', value: '' },
    { id: '93139', value: '' },
    { id: '13295', value: '' },
    { id: '15148', value: '' },
    { id: '3342', value: '' },
    { id: '3345', value: '' },
    { id: '56093', value: '' },
    { id: '57472', value: '' },
  ],
  ooe_a4: [
    { id: '772', value: '' },
    { id: '776', value: '' },
    { id: '94381', value: '' },
    { id: '94369', value: '' },
    { id: '8336', value: '' },
  ],
  ooe_a9: [
    { id: '73188', value: '' },
    { id: '73213', value: '' },
    { id: '78825', value: '' },
    { id: '94241', value: '' },
    { id: '94238', value: '' },
    { id: '72230', value: '' },
    { id: '5806', value: '' },
    { id: '94505', value: '' },
    { id: '94508', value: '' },
    { id: '7172', value: '' },
    { id: '5823', value: '' },
    { id: '14999', value: '' },
    { id: '111660', value: '' },
    { id: '111689', value: '' },
    { id: '9947', value: '' },
  ],
  ooe_a10: [
    { id: '82099', value: '' },
    { id: '82107', value: '' },
    { id: '3469', value: '' },
    { id: '55874', value: '' },
    { id: '3128', value: '' },
    { id: '3127', value: '' },
    { id: '3136', value: '' },
    { id: '3120', value: '' },
    { id: '89062', value: '' },
    { id: '3116', value: '' },
    { id: '82111', value: '' },
    { id: '3109', value: '' },
    { id: '3110', value: '' },
    { id: '3102', value: '' },
    { id: '111291', value: '' },
    { id: '3103', value: '' },
    { id: '3095', value: '' },
    { id: '3096', value: '' },
    { id: '3088', value: '' },
    { id: '111295', value: '' },
    { id: '3089', value: '' },
    { id: '3081', value: '' },
    { id: '5578', value: '' },
    { id: '111271', value: '' },
    { id: '95073', value: '' },
    { id: '5585', value: '' },
    { id: '111275', value: '' },
    { id: '111299', value: '' },
    { id: '5586', value: '' },
    { id: '82183', value: '' },
    { id: '5445', value: '' },
    { id: '86962', value: '' },
    { id: '82119', value: '' },
    { id: '82179', value: '' },
    { id: '82123', value: '' },
    { id: '111279', value: '' },
    { id: '82316', value: '' },
    { id: '82319', value: '' },
    { id: '82310', value: '' },
    { id: '82313', value: '' },
    { id: '5452', value: '' },
    { id: '111283', value: '' },
    { id: '5453', value: '' },
    { id: '5460', value: '' },
    { id: '5459', value: '' },
    { id: '82127', value: '' },
    { id: '6602', value: '' },
    { id: '82131', value: '' },
    { id: '82135', value: '' },
    { id: '91179', value: '' },
    { id: '5262', value: '' },
    { id: '95706', value: '' },
    { id: '5503', value: '' },
    { id: '56032', value: '' },
    { id: '95710', value: '' },
  ],
  ooe_a11: [
    { id: '56010', value: '' },
    { id: '94530', value: '' },
    { id: '57931', value: '' },
  ],
  ooe_a12: [
    { id: '92265', value: '' },
    { id: '12808', value: '' },
    { id: '12813', value: '' },
    { id: '9518', value: '' },
    { id: '12828', value: '' },
    { id: '625', value: '' },
    { id: '12850', value: '' },
    { id: '92354', value: '' },
    { id: '3415', value: '' },
    { id: '94245', value: '' },
    { id: '9600', value: '' },
  ],
  ooe_a13: [
    { id: '91793', value: '' },
    { id: '2715', value: '' },
    { id: '91157', value: '' },
    { id: '94255', value: '' },
    { id: '59906', value: '' },
    { id: '104447', value: '' },
  ],
  ooe_a1_linz_wien: [
    { id: '16963', value: '' },
    { id: '16931', value: '' },
    { id: '1846', value: '' },
    { id: '1839', value: '' },
    { id: '16928', value: '' },
    { id: '16925', value: '' },
    { id: '16922', value: '' },
    { id: '1855', value: '' },
    { id: '1862', value: '' },
    { id: '16920', value: '' },
    { id: '1867', value: '' },
    { id: '1872', value: '' },
    { id: '10157', value: '' },
    { id: '10154', value: '' },
    { id: '10148', value: '' },
    { id: '10139', value: '' },
    { id: '6761', value: '' },
  ],
  ooe_a1_linz_salzburg: [
    { id: '1846', value: '' },
    { id: '16934', value: '' },
    { id: '7747', value: '' },
    { id: '16940', value: '' },
    { id: '9061', value: '' },
    { id: '9065', value: '' },
    { id: '9067', value: '' },
    { id: '2119', value: '' },
    { id: '58953', value: '' },
    { id: '58956', value: '' },
    { id: '92245', value: '' },
    { id: '58958', value: '' },
    { id: '58962', value: '' },
    { id: '87001', value: '' },
    { id: '9075', value: '' },
    { id: '110362', value: '' },
    { id: '9079', value: '' },
    { id: '9081', value: '' },
    { id: '9083', value: '' },
    { id: '9086', value: '' },
    { id: '14855', value: '' },
    { id: '11834', value: '' },
    { id: '58977', value: '' },
    { id: '87009', value: '' },
    { id: '58968', value: '' },
    { id: '58971', value: '' },
    { id: '58980', value: '' },
    { id: '58983', value: '' },
  ],
  ooe_a25: [
    { id: '87049', value: '' },
    { id: '87045', value: '' },
    { id: '87041', value: '' },
    { id: '87037', value: '' },
    { id: '87033', value: '' },
    { id: '87029', value: '' },
    { id: '87025', value: '' },
    { id: '9062', value: '' },
  ],
  ooe_a8: [
    { id: '58907', value: '' },
    { id: '58910', value: '' },
    { id: '14070', value: '' },
    { id: '3743', value: '' },
    { id: '14080', value: '' },
    { id: '78663', value: '' },
    { id: '78673', value: '' },
    { id: '78659', value: '' },
    { id: '78678', value: '' },
    { id: '6744', value: '' },
    { id: '79614', value: '' },
    { id: '79624', value: '' },
    { id: '79629', value: '' },
    { id: '92257', value: '' },
    { id: '92261', value: '' },
    { id: '17022', value: '' },
    { id: '2116', value: '' },
    { id: '2119', value: '' },
    { id: '73188', value: '' },
  ],
  ooe_b1_b139: [
    { id: 'LOOE_a611KAM1', value: '' },
    { id: 'LOOE_a611KAM4', value: '' },
    { id: 'LOOE_a613KAM1', value: '' },
    { id: 'LOOE_a613KAM2', value: '' },
    { id: 'LOOE_a22KAM3', value: '' },
    { id: 'LOOE_a22KAM4', value: '' },
    { id: 'LOOE_a614KAM2', value: '' },
    { id: 'LOOE_a614KAM3', value: '' },
    { id: 'LOOE_a614KAM1', value: '' },
  ],
  ooe_schaerding: [
    { id: '79629', value: '' },
    { id: '79624', value: '' },
    { id: '79619', value: '' },
    { id: '79614', value: '' },
    { id: '6744', value: '' },
    { id: '78678', value: '' },
    { id: '78659', value: '' },
    { id: '78655', value: '' },
    { id: '78651', value: '' },
    { id: '78663', value: '' },
    { id: '14080', value: '' },
    { id: '3743', value: '' },
    { id: '14070', value: '' },
    { id: '58910', value: '' },
    { id: '58907', value: '' },
    { id: '92261', value: '' },
    { id: '92257', value: '' },
    { id: '17022', value: '' },
    { id: '2119', value: '' },
    { id: '2116', value: '' },
    { id: '2113', value: '' },
    { id: '2110', value: '' },
    { id: '87049', value: '' },
    { id: '87045', value: '' },
    { id: '87033', value: '' },
    { id: '87026', value: '' },
    { id: '87029', value: '' },
    { id: '87025', value: '' },
    { id: '9061', value: '' },
    { id: '9062', value: '' },
    { id: 'LOOE_a604KAM1', value: '' },
    { id: 'LOOE_a604KAM2', value: '' },
  ],
  ooe_ried_braunau: [
    { id: '79629', value: '' },
    { id: '79624', value: '' },
    { id: '79619', value: '' },
    { id: '79614', value: '' },
    { id: '6744', value: '' },
    { id: '78678', value: '' },
    { id: '78659', value: '' },
    { id: '78674', value: '' },
    { id: '78655', value: '' },
    { id: '78668', value: '' },
    { id: '78651', value: '' },
    { id: '78663', value: '' },
    { id: '14080', value: '' },
    { id: '3743', value: '' },
    { id: '3744', value: '' },
    { id: '14070', value: '' },
    { id: '5891', value: '' },
    { id: '58907', value: '' },
    { id: '92261', value: '' },
    { id: '92257', value: '' },
    { id: '12745', value: '' },
    { id: '12744', value: '' },
    { id: '12746', value: '' },
    { id: '2113', value: '' },
    { id: '2119', value: '' },
    { id: '2116', value: '' },
    { id: '2110', value: '' },
    { id: '87049', value: '' },
    { id: '87045', value: '' },
    { id: '87041', value: '' },
    { id: '87037', value: '' },
    { id: '87033', value: '' },
    { id: '87029', value: '' },
    { id: '87025', value: '' },
    { id: '9061', value: '' },
    { id: '9062', value: '' },
  ],
  ooe_gmunden: [
    { id: '58953', value: '' },
    { id: '58956', value: '' },
    { id: '92245', value: '' },
    { id: '58959', value: '' },
    { id: '58962', value: '' },
    { id: '87001', value: '' },
    { id: '9075', value: '' },
    { id: '9076', value: '' },
    { id: '110362', value: '' },
    { id: '110365', value: '' },
    { id: '9079', value: '' },
    { id: '9080', value: '' },
    { id: '9081', value: '' },
    { id: '9082', value: '' },
    { id: '9083', value: '' },
    { id: '9084', value: '' },
    { id: '9086', value: '' },
    { id: '9085', value: '' },
    { id: '14855', value: '' },
    { id: '14846', value: '' },
    { id: '11834', value: '' },
    { id: '11815', value: '' },
    { id: '58977', value: '' },
    { id: '60216', value: '' },
    { id: '87009', value: '' },
    { id: '58968', value: '' },
    { id: '58971', value: '' },
    { id: '58980', value: '' },
    { id: '58983', value: '' },
  ],
  ooe_kirchdorf: [
    { id: '94241', value: '' },
    { id: '94238', value: '' },
    { id: '60155', value: '' },
    { id: '60152', value: '' },
    { id: '78825', value: '' },
    { id: '73213', value: '' },
    { id: '59985', value: '' },
    { id: '73293', value: '' },
    { id: '73198', value: '' },
    { id: '73188', value: '' },
    { id: '17022', value: '' },
    { id: '92257', value: '' },
    { id: '92261', value: '' },
  ],
  ooe_steyr_enns_linz: [
    { id: '10154', value: '' },
    { id: '10157', value: '' },
    { id: '1872', value: '' },
    { id: '13536', value: '' },
    { id: '13542', value: '' },
    { id: '13548', value: '' },
    { id: '13564', value: '' },
    { id: '1867', value: '' },
    { id: '16920', value: '' },
    { id: '1862', value: '' },
    { id: '1856', value: '' },
    { id: '1855', value: '' },
    { id: '16922', value: '' },
    { id: '16925', value: '' },
    { id: '16928', value: '' },
    { id: '1839', value: '' },
    { id: '1847', value: '' },
    { id: '1846', value: '' },
    { id: '4295', value: '' },
    { id: '16931', value: '' },
    { id: '16934', value: '' },
    { id: '16937', value: '' },
    { id: '7744', value: '' },
    { id: '7747', value: '' },
    { id: '12741', value: '' },
    { id: '14751', value: '' },
    { id: '12739', value: '' },
    { id: '16940', value: '' },
    { id: '9061', value: '' },
    { id: '9062', value: '' },
  ],
  ooe_perg_linz: [
    { id: 'LOOE_a32KAM3', value: '' },
    { id: 'LOOE_a32KAM4', value: '' },
    { id: 'LOOE_a609KAM1', value: '' },
    { id: 'LOOE_a609KAM2', value: '' },
    { id: 'LOOE_a610KAM1', value: '' },
    { id: 'LOOE_a610KAM2', value: '' },
    { id: '16974', value: '' },
    { id: '2294', value: '' },
    { id: '2295', value: '' },
    { id: '7751', value: '' },
    { id: '2065', value: '' },
    { id: '2066', value: '' },
    { id: '2288', value: '' },
    { id: '2287', value: '' },
    { id: '2058', value: '' },
    { id: '2052', value: '' },
    { id: '2051', value: '' },
    { id: '2044', value: '' },
    { id: '2037', value: '' },
    { id: '2038', value: '' },
    { id: '2276', value: '' },
    { id: '2272', value: '' },
    { id: '16971', value: '' },
    { id: '17822', value: '' },
    { id: '17819', value: '' },
    { id: '16969', value: '' },
    { id: '16965', value: '' },
    { id: '16963', value: '' },
  ],
  ooe_grieskirchen_linz: [
    { id: '17022', value: '' },
    { id: '58907', value: '' },
    { id: '58910', value: '' },
    { id: '14070', value: '' },
    { id: '3744', value: '' },
    { id: '3743', value: '' },
    { id: '14080', value: '' },
    { id: '78663', value: '' },
    { id: '78651', value: '' },
    { id: '78668', value: '' },
    { id: '78655', value: '' },
    { id: '78673', value: '' },
    { id: '78659', value: '' },
    { id: '78678', value: '' },
    { id: '6744', value: '' },
    { id: '79614', value: '' },
    { id: '79619', value: '' },
    { id: '79624', value: '' },
    { id: '79629', value: '' },
    { id: '72317', value: '' },
    { id: '72316', value: '' },
    { id: '79634', value: '' },
  ],
  ooe_a7: [
    { id: '16963', value: '' },
    { id: '16965', value: '' },
    { id: '16968', value: '' },
    { id: '17819', value: '' },
    { id: '17822', value: '' },
    { id: '16971', value: '' },
    { id: '2272', value: '' },
    { id: '2276', value: '' },
    { id: '2038', value: '' },
    { id: '2037', value: '' },
    { id: '2044', value: '' },
    { id: '2051', value: '' },
    { id: '2052', value: '' },
    { id: '2058', value: '' },
    { id: '2287', value: '' },
    { id: '2288', value: '' },
    { id: '2066', value: '' },
    { id: '2065', value: '' },
    { id: '7751', value: '' },
    { id: '2295', value: '' },
    { id: '2294', value: '' },
    { id: '16974', value: '' },
    { id: '16977', value: '' },
    { id: '16992', value: '' },
    { id: '17001', value: '' },
    { id: '55969', value: '' },
    { id: '55971', value: '' },
    { id: '88440', value: '' },
    { id: '88446', value: '' },
  ],

  burgenland: [
    { id: '58747', value: '' },
    { id: '58748', value: '' },
    { id: '58750', value: '' },
    { id: '58749', value: '' },
    { id: '8337', value: '' },
    { id: '8332', value: '' },
    { id: '92474', value: '' },
    { id: '92500', value: '' },
    { id: '8338', value: '' },
    { id: '89047', value: '' },
    { id: '8339', value: '' },
    { id: '8334', value: '' },
    { id: '79918', value: '' },
    { id: '79919', value: '' },
    { id: '83696', value: '' },
    { id: '82283', value: '' },
    { id: '82286', value: '' },
    { id: '82289', value: '' },
    { id: '82292', value: '' },
    { id: '92463', value: '' },
    { id: '86374', value: '' },
    { id: '86382', value: '' },
    { id: '86390', value: '' },
    { id: '82296', value: '' },
    { id: '82299', value: '' },
    { id: '82302', value: '' },
    { id: '82305', value: '' },
    { id: '86418', value: '' },
    { id: '92536', value: '' },
    { id: '72512', value: '' },
    { id: '72502', value: '' },
    { id: '72519', value: '' },
    { id: '72515', value: '' },
    { id: '72522', value: '' },
    { id: '72527', value: '' },
    { id: '72538', value: '' },
    { id: '72535', value: '' },
    { id: '72531', value: '' },
    { id: '11924', value: '' },
    { id: '17726', value: '' },
    { id: '82208', value: '' },
    { id: '17732', value: '' },
    { id: '17735', value: '' },
    { id: '92251', value: '' },
    { id: '92254', value: '' },
    { id: '2344', value: '' },
    { id: '2356', value: '' },
    { id: '2365', value: '' },
    { id: '2347', value: '' },
    { id: '2368', value: '' },
    { id: '2379', value: '' },
    { id: '17750', value: '' },
    { id: '17753', value: '' },
    { id: '17756', value: '' },
    { id: '17759', value: '' },
  ], // BGLD
  noe_a1: [
    { id: '10157', value: '' },
    { id: '10154', value: '' },
    { id: '10148', value: '' },
    { id: '10145', value: '' },
    { id: '10142', value: '' },
    { id: '6761', value: '' },
    { id: '11906', value: '' },
    { id: '11900', value: '' },
    { id: '11897', value: '' },
    { id: '11890', value: '' },
    { id: '58474', value: '' },
    { id: '11871', value: '' },
    { id: '5693', value: '' },
    { id: '96909', value: '' },
    { id: '914', value: '' },
    { id: '15085', value: '' },
    { id: '15083', value: '' },
    { id: '15088', value: '' },
    { id: '15091', value: '' },
    { id: '9996', value: '' },
    { id: '94327', value: '' },
    { id: '94305', value: '' },
    { id: '95761', value: '' },
    { id: '95736', value: '' },
    { id: '943', value: '' },
    { id: '917', value: '' },
    { id: '80955', value: '' },
    { id: '80941', value: '' },
    { id: '80952', value: '' },
    { id: '80945', value: '' },
    { id: '80949', value: '' },
    { id: '77388', value: '' },
    { id: '79380', value: '' },
    { id: '79371', value: '' },
  ],
  noe_a2: [
    { id: '2130', value: '' }, // A2 Vösendorf
    { id: '2139', value: '' }, // A2 Vösendorf
    { id: '88538', value: '' },
    { id: '79722', value: '' },
    { id: '58722', value: '' },
    { id: '88541', value: '' },
    { id: '88544', value: '' },
    { id: '86370', value: '' },
    { id: '88547', value: '' },
    { id: '91689', value: '' },
    { id: '107564', value: '' },
    { id: '91697', value: '' },
    { id: '97029', value: '' },
    { id: '94925', value: '' },
    { id: '91752', value: '' },
    { id: '91709', value: '' },
    { id: '8193', value: '' },
    { id: '12008', value: '' },
    { id: '12022', value: '' },
    { id: '12025', value: '' },
    { id: '12031', value: '' },
    { id: '12038', value: '' },
    { id: '12046', value: '' },
    { id: '12070', value: '' },
    { id: '12101', value: '' },
    { id: '12049', value: '' },
    { id: '12052', value: '' },
    { id: '12104', value: '' },
    { id: '12055', value: '' },
    { id: '12058', value: '' },
  ],
  noe_a22: [
    { id: '10358', value: '' },
    { id: '10361', value: '' },
    { id: '10367', value: '' },
    { id: '9768', value: '' },
    { id: '10370', value: '' },
    { id: '14880', value: '' },
    { id: '96970', value: '' },
    { id: '213014883', value: '' },
    { id: '2130', value: '' },
  ],
  noe_a4: [
    { id: '778', value: '' },
    { id: '100074', value: '' },
    { id: '94381', value: '' },
    { id: '58812', value: '' },
    { id: '58815', value: '' },
    { id: '58806', value: '' },
    { id: '94345', value: '' },
    { id: '9284', value: '' },
    { id: '9285', value: '' },
    { id: '94357', value: '' },
    { id: '7266', value: '' },
    { id: '94365', value: '' },
    { id: '94369', value: '' },
    { id: '57396', value: '' },
    { id: '12738', value: '' },
  ],
  noe_a5: [
    { id: '96912', value: '' },
    { id: '13401', value: '' },
    { id: '13423', value: '' },
    { id: '13422', value: '' },
    { id: '13427', value: '' },
    { id: '13426', value: '' },
    { id: '13428', value: '' },
    { id: '13430', value: '' },
    { id: '13429', value: '' },
    { id: '13432', value: '' },
    { id: '13431', value: '' },
    { id: '13434', value: '' },
    { id: '13435', value: '' },
    { id: '13437', value: '' },
    { id: '13436', value: '' },
    { id: '13440', value: '' },
    { id: '13439', value: '' },
    { id: '13442', value: '' },
    { id: '13441', value: '' },
    { id: '13443', value: '' },
    { id: '13445', value: '' },
    { id: '13444', value: '' },
    { id: '81975', value: '' },
    { id: '81969', value: '' },
    { id: '81972', value: '' },
    { id: '81981', value: '' },
    { id: '81978', value: '' },
    { id: '13447', value: '' },
    { id: '13449', value: '' },
    { id: '13448', value: '' },
    { id: '13451', value: '' },
    { id: '13450', value: '' },
    { id: '13453', value: '' },
    { id: '13452', value: '' },
    { id: '13455', value: '' },
    { id: '13454', value: '' },
    { id: '13457', value: '' },
    { id: '13456', value: '' },
    { id: '13459', value: '' },
    { id: '13458', value: '' },
    { id: '13461', value: '' },
    { id: '13460', value: '' },
    { id: '13464', value: '' },
    { id: '13463', value: '' },
    { id: '13466', value: '' },
    { id: '13465', value: '' },
    { id: '13468', value: '' },
    { id: '13470', value: '' },
    { id: '13469', value: '' },
    { id: '13472', value: '' },
    { id: '13471', value: '' },
    { id: '13474', value: '' },
    { id: '13473', value: '' },
    { id: '13477', value: '' },
    { id: '13476', value: '' },
    { id: '79759', value: '' },
    { id: '79771', value: '' },
    { id: '79775', value: '' },
    { id: '79806', value: '' },
    { id: '79779', value: '' },
    { id: '79783', value: '' },
    { id: '79812', value: '' },
    { id: '79787', value: '' },
    { id: '79815', value: '' },
    { id: '79791', value: '' },
    { id: '79818', value: '' },
    { id: '79795', value: '' },
    { id: '79821', value: '' },
    { id: '91528', value: '' },
    { id: '91531', value: '' },
    { id: '91534', value: '' },
  ],
  noe_s1: [
    { id: '2136', value: '' },
    { id: '2133', value: '' },
    { id: '7277', value: '' },
    { id: '7277', value: '' },
    { id: '72012', value: '' },
    { id: '13015', value: '' },
    { id: '13016', value: '' },
    { id: '13017', value: '' },
    { id: '13018', value: '' },
    { id: '13019', value: '' },
    { id: '13020', value: '' },
    { id: '13021', value: '' },
    { id: '82400', value: '' },
    { id: '82403', value: '' },
    { id: '82406', value: '' },
    { id: '82409', value: '' },
    { id: '92412', value: '' },
    { id: '82415', value: '' },
    { id: '13022', value: '' },
    { id: '13023', value: '' },
    { id: '13025', value: '' },
    { id: '13026', value: '' },
    { id: '13027', value: '' },
    { id: '13028', value: '' },
    { id: '13029', value: '' },
    { id: '13030', value: '' },
    { id: '13031', value: '' },
    { id: '13032', value: '' },
    { id: '13033', value: '' },
    { id: '13034', value: '' },
    { id: '13035', value: '' },
    { id: '13037', value: '' },
    { id: '13039', value: '' },
    { id: '13040', value: '' },
    { id: '13041', value: '' },
    { id: '13042', value: '' },
    { id: '13043', value: '' },
    { id: '13045', value: '' },
    { id: '13045', value: '' },
    { id: '13101', value: '' },
    { id: '13342', value: '' },
    { id: '13343', value: '' },
  ],
  noe_s3: [
    { id: '88385', value: '' },
    { id: '88393', value: '' },
    { id: '88401', value: '' },
    { id: '88413', value: '' },
    { id: '94647', value: '' },
    { id: '100443', value: '' },
    { id: '100302', value: '' },
  ],
  noe_s33: [
    { id: '97032', value: '' },
    { id: '96981', value: '' },
    { id: '7217', value: '' },
    { id: '7216', value: '' },
    { id: '7219', value: '' },
    { id: '91516', value: '' },
    { id: '7221', value: '' },
    { id: '7220', value: '' },
  ],
  noe_s5: [
    { id: '80802', value: '' },
    { id: '80794', value: '' },
    { id: '80782', value: '' },
    { id: '80774', value: '' },
    // TODO: { id: 'EVISNOE-B009_012,397_0_00079933', value: '' }, // Federal Camera
    // TODO: { id: 'EVISNOE-B010_027,747_0_00079937', value: '' }, // Federal Camera
  ],
  stmk_a2: [
    { id: '11943', value: '' },
    { id: '11909', value: '' },
    { id: '11946', value: '' },
    { id: '11912', value: '' },
    { id: '11949', value: '' },
    { id: '17803', value: '' },
    { id: '17800', value: '' },
    { id: '17806', value: '' },
    { id: '17812', value: '' },
    { id: '11952', value: '' },
    { id: '11955', value: '' },
    { id: '11958', value: '' },
    { id: '11961', value: '' },
    { id: '11927', value: '' },
    { id: '11964', value: '' },
    { id: '11940', value: '' },
    { id: '9962', value: '' },
    { id: '9964', value: '' },
    { id: '9967', value: '' },
    { id: '9970', value: '' },
    { id: '9979', value: '' },
    { id: '9982', value: '' },
    { id: '93103', value: '' },
    { id: '93111', value: '' },
    { id: '84641', value: '' },
    { id: '93115', value: '' },
    { id: '13608', value: '' },
    { id: '13614', value: '' },
    { id: '3615', value: '' },
    { id: '3614', value: '' },
    { id: '3608', value: '' },
    { id: '3607', value: '' },
    { id: '10835', value: '' },
    { id: '14986', value: '' },
    { id: '3604', value: '' },
    { id: '4181', value: '' },
    { id: '4180', value: '' },
    { id: '14989', value: '' },
    { id: '10838', value: '' },
    { id: '3629', value: '' },
    { id: '3628', value: '' },
    { id: '15050', value: '' },
    { id: '9757', value: '' },
    { id: '9754', value: '' },
    { id: '9755', value: '' },
    { id: '9758', value: '' },
    { id: '14993', value: '' },
    { id: '3636', value: '' },
    { id: '3635', value: '' },
    { id: '4188', value: '' },
    { id: '3660', value: '' },
    { id: '3665', value: '' },
    { id: '3667', value: '' },
    { id: '4191', value: '' },
    { id: '10896', value: '' },
    { id: '3671', value: '' },
    { id: '4205', value: '' },
    { id: '3676', value: '' },
    { id: '3675', value: '' },
    { id: '82233', value: '' },
    { id: '82236', value: '' },
    { id: '82223', value: '' },
    { id: '82230', value: '' },
    { id: '82239', value: '' },
    { id: '82242', value: '' },
    { id: '82227', value: '' },
    { id: '8149', value: '' },
    { id: '3682', value: '' },
    { id: '4210', value: '' },
    { id: '3687', value: '' },
    { id: '3686', value: '' },
    { id: '7309', value: '' },
    { id: '7313', value: '' },
    { id: '93119', value: '' },
    { id: '7319', value: '' },
    { id: '7320', value: '' },
    { id: '15002', value: '' },
    { id: '15005', value: '' },
    { id: '15008', value: '' },
    { id: '93127', value: '' },
    { id: '15129', value: '' },
    { id: '82245', value: '' },
    { id: '82248', value: '' },
    { id: '3134', value: '' },
    { id: '3198', value: '' },
    { id: '93131', value: '' },
    { id: '93135', value: '' },
    { id: '93139', value: '' },
  ],
  stmk_a9: [
    { id: '94241', value: '' },
    { id: '72185', value: '' },
    { id: '72190', value: '' },
    { id: '94238', value: '' },
    { id: '72230', value: '' },
    { id: '72235', value: '' },
    { id: '93147', value: '' },
    { id: '5806', value: '' },
    { id: '5810', value: '' },
    { id: '7155', value: '' },
    { id: '7154', value: '' },
    { id: '94505', value: '' },
    { id: '94508', value: '' },
    { id: '94266', value: '' },
    { id: '94290', value: '' },
    { id: '7172', value: '' },
    { id: '7171', value: '' },
    { id: '7178', value: '' },
    { id: '93151', value: '' },
    { id: '93159', value: '' },
    { id: '93163', value: '' },
    { id: '3722', value: '' },
    { id: '3729', value: '' },
    { id: '4176', value: '' },
    { id: '5823', value: '' },
    { id: '5822', value: '' },
    { id: '11203', value: '' },
    { id: '94635', value: '' },
    { id: '3282', value: '' },
    { id: '8161', value: '' },
    { id: '14999', value: '' },
    { id: '8921', value: '' },
    { id: '8957', value: '' },
    { id: '10929', value: '' },
    { id: '86592', value: '' },
    { id: '86596', value: '' },
    { id: '9945', value: '' },
    { id: '9947', value: '' },
    { id: '9950', value: '' },
  ],
  stmk_s35: [
    { id: '93221', value: '' },
    { id: '93229', value: '' },
    { id: '93233', value: '' },
    { id: '58617', value: '' },
    { id: '93237', value: '' },
    { id: '4509', value: '' },
  ],
  stmk_s6: [
    { id: '8445', value: '' },
    { id: '8452', value: '' },
    { id: '8451', value: '' },
    { id: '8565', value: '' },
    { id: '93175', value: '' },
    { id: '93187', value: '' },
    { id: '93191', value: '' },
    { id: '93195', value: '' },
    { id: '4513', value: '' },
    { id: '4519', value: '' },
    { id: '93199', value: '' },
    { id: '93203', value: '' },
    { id: '4523', value: '' },
  ],
  stmk_s36: [
    { id: '93980', value: '' },
    { id: '93245', value: '' },
    { id: '93249', value: '' },
    { id: '93253', value: '' },
    { id: '93257', value: '' },
    { id: '93256', value: '' },
    { id: '4530', value: '' },
  ],
  sbg_a1: [
    { id: '81984', value: '' },
    { id: '81992', value: '' },
    { id: '81996', value: '' },
    { id: '55879', value: '' },
    { id: '10716', value: '' },
    { id: '10720', value: '' },
    { id: '55882', value: '' },
    { id: '81964', value: '' },
    { id: '55885', value: '' },
    { id: '55888', value: '' },
    { id: '82000', value: '' },
    { id: '55891', value: '' },
    { id: '55894', value: '' },
    { id: '55897', value: '' },
    { id: '55900', value: '' },
    { id: '55903', value: '' },
    { id: '55909', value: '' },
    { id: '55912', value: '' },
    { id: '55918', value: '' },
    { id: '55915', value: '' },
    { id: '55921', value: '' },
    { id: '55924', value: '' },
    { id: '3152', value: '' },
    { id: '3153', value: '' },
    { id: '55933', value: '' },
    { id: '82103', value: '' },
    { id: '3146', value: '' },
    { id: '3145', value: '' },
    { id: '3143', value: '' },
    { id: '82359', value: '' },
    { id: '82362', value: '' },
    { id: '82365', value: '' },
    { id: '82368', value: '' },
    { id: '82371', value: '' },
    { id: '82374', value: '' },
  ],
  sbg_a10: [
    { id: '82099', value: '' },
    { id: '82107', value: '' },
    { id: '3469', value: '' },
    { id: '55874', value: '' },
    { id: '3128', value: '' },
    { id: '3127', value: '' },
    { id: '3136', value: '' },
    { id: '3120', value: '' },
    { id: '89062', value: '' },
    { id: '3116', value: '' },
    { id: '82111', value: '' },
    { id: '3109', value: '' },
    { id: '3110', value: '' },
    { id: '3102', value: '' },
    { id: '3103', value: '' },
    { id: '3095', value: '' },
    { id: '3096', value: '' },
    { id: '3088', value: '' },
    { id: '3089', value: '' },
    { id: '3081', value: '' },
    { id: '5578', value: '' },
    { id: '95073', value: '' },
    { id: '5585', value: '' },
    { id: '5586', value: '' },
    { id: '82183', value: '' },
    { id: '5445', value: '' },
    { id: '86962', value: '' },
    { id: '82119', value: '' },
    { id: '82179', value: '' },
    { id: '82123', value: '' },
    { id: '82316', value: '' },
    { id: '82319', value: '' },
    { id: '82310', value: '' },
    { id: '82313', value: '' },
    { id: '5452', value: '' },
    { id: '5453', value: '' },
    { id: '5460', value: '' },
    { id: '5459', value: '' },
    { id: '82127', value: '' },
    { id: '6602', value: '' },
    { id: '82131', value: '' },
    { id: '82135', value: '' },
    { id: '91179', value: '' },
    { id: '5262', value: '' },
    { id: '95706', value: '' },
    { id: '95710', value: '' },
  ],
  sbg_b159: [
    { id: '111299', value: '' },
    { id: '111295', value: '' },
    { id: '111271', value: '' },
    { id: '111275', value: '' },
  ],
  sbg_b311: [
    { id: '7295', value: '' },
    { id: '7296', value: '' },
  ],
  sbg_l107: [{ id: '111291', value: '' }],
  ooe_b177: [
    { id: 'LT_17703', value: '' },
    { id: 'LT_17704', value: '' },
    { id: 'LT_17701', value: '' },
    { id: 'LT_17702', value: '' },
    { id: 'LT_17705', value: '' },
    { id: 'LT_17706', value: '' },
    { id: 'LT_17707', value: '' },
  ],
  ooe_b179: [
    { id: 'LT_17919', value: '' },
    { id: 'LT_17918', value: '' },
    { id: 'LT_17914', value: '' },
    { id: 'LT_17913', value: '' },
    { id: 'LT_17921', value: '' },
    { id: 'LT_17920', value: '' },
    { id: 'LT_17925', value: '' },
    { id: 'LT_17924', value: '' },
    { id: 'LT_17906', value: '' },
    { id: 'LT_17905', value: '' },
    { id: 'LT_17902', value: '' },
    { id: 'LT_17901', value: '' },
  ],
  ooe_b197: [
    { id: 'LT_19701', value: '' },
    { id: 'LT_19702', value: '' },
    { id: 'LT_19703', value: '' },
    { id: 'LT_19704', value: '' },
  ],
  ktn_a2_arnoldstein: [
    { id: '57472', value: '' },
    { id: '57467', value: '' },
    { id: '57469', value: '' },
    { id: '82490', value: '' },
    { id: '82255', value: '' },
    { id: '82258', value: '' },
    { id: '82261', value: '' },
    { id: '82264', value: '' },
    { id: '82273', value: '' },
    { id: '82252', value: '' },
    { id: '82267', value: '' },
    { id: '82494', value: '' },
    { id: '82270', value: '' },
    { id: '82279', value: '' },
    { id: '82276', value: '' },
    { id: '81944', value: '' },
    { id: '81941', value: '' },
    { id: '81948', value: '' },
    { id: '56108', value: '' },
    { id: '56102', value: '' },
    { id: '56105', value: '' },
    { id: '56096', value: '' },
    { id: '56099', value: '' },
  ],
  ktn_a2_villach_klagenfurt: [
    { id: '56096', value: '' },
    { id: '56099', value: '' },
    { id: '56090', value: '' },
    { id: '56093', value: '' },
    { id: '6591', value: '' },
    { id: '6590', value: '' },
    { id: '56087', value: '' },
    { id: '6588', value: '' },
    { id: '56084', value: '' },
    { id: '56081', value: '' },
    { id: '56075', value: '' },
    { id: '56072', value: '' },
    { id: '56069', value: '' },
    { id: '56066', value: '' },
    { id: '56063', value: '' },
    { id: '3345', value: '' },
    { id: '3344', value: '' },
    { id: '3343', value: '' },
    { id: '3342', value: '' },
    { id: '56057', value: '' },
    { id: '3334', value: '' },
    { id: '3335', value: '' },
    { id: '56051', value: '' },
    { id: '56048', value: '' },
    { id: '16380', value: '' },
    { id: '16358', value: '' },
    { id: '9122', value: '' },
    { id: '9132', value: '' },
    { id: '16374', value: '' },
    { id: '56042', value: '' },
    { id: '56045', value: '' },
  ],
  ktn_a2_klagenfurt_graz: [
    { id: '56042', value: '' },
    { id: '56045', value: '' },
    { id: '15157', value: '' },
    { id: '106226', value: '' },
    { id: '106339', value: '' },
    { id: '106232', value: '' },
    { id: '106235', value: '' },
    { id: '15154', value: '' },
    { id: '15151', value: '' },
    { id: '15148', value: '' },
    { id: '13358', value: '' },
    { id: '13361', value: '' },
    { id: '15169', value: '' },
    { id: '13352', value: '' },
    { id: '15166', value: '' },
    { id: '13300', value: '' },
    { id: '13297', value: '' },
    { id: '13295', value: '' },
    { id: '92431', value: '' },
    { id: '92435', value: '' },
    { id: '95084', value: '' },
    { id: '95081', value: '' },
    { id: '86353', value: '' },
    { id: '86350', value: '' },
    { id: '86347', value: '' },
  ],
  ktn_a10: [
    { id: '5503', value: '' },
    { id: '94227', value: '' },
    { id: '5509', value: '' },
    { id: '5510', value: '' },
    { id: '86601', value: '' },
    { id: '86605', value: '' },
    { id: '83708', value: '' },
    { id: '83712', value: '' },
    { id: '82187', value: '' },
    { id: '106161', value: '' },
    { id: '106165', value: '' },
    { id: '106173', value: '' },
    { id: '106177', value: '' },
    { id: '106181', value: '' },
    { id: '106185', value: '' },
    { id: '81952', value: '' },
    { id: '81958', value: '' },
    { id: '81955', value: '' },
    { id: '81961', value: '' },
    { id: '56018', value: '' },
    { id: '80986', value: '' },
    { id: '56026', value: '' },
    { id: '56032', value: '' },
    { id: '56029', value: '' },
  ],
  ktn_a11_karawanken: [
    { id: '56010', value: '' },
    { id: '106189', value: '' },
    { id: '82324', value: '' },
    { id: '82327', value: '' },
    { id: '82336', value: '' },
    { id: '82330', value: '' },
    { id: '82333', value: '' },
    { id: '106193', value: '' },
    { id: '106201', value: '' },
    { id: '106205', value: '' },
    { id: '82340', value: '' },
    { id: '82343', value: '' },
    { id: '82346', value: '' },
    { id: '82349', value: '' },
    { id: '82352', value: '' },
    { id: '86609', value: '' },
    { id: '94530', value: '' },
    { id: '57931', value: '' },
  ],
}

enum STATES {
  o = 'Oberösterreich',
  n = 'Niederösterreich',
  s = 'Salzburg',
  w = 'Wien',
  t = 'Tirol',
  st = 'Steiermark',
  b = 'Burgenland',
  k = 'Kärnten',
  v = 'Vorarlberg',
}

export function getDataTrafficIds(key: string): string[] {
  if (key.indexOf('-') !== -1) {
    key = key.replaceAll('-', '_')
  }
  const route: keyof typeof DATA_TRAFFIC_MAP = key as keyof typeof DATA_TRAFFIC_MAP
  const ids: string[] = []
  DATA_TRAFFIC_MAP[route].forEach((e) => {
    ids.push(e.id)
  })
  return ids
}

export function getStateCode(state: string): string {
  const stateKey: keyof typeof STATES = state.toLocaleLowerCase() as keyof typeof STATES
  return STATES[stateKey] || ''
}

export function getHighway(nameID: string) {
  return HIGHWAYS.filter((h) => h.nameID === nameID)[0]?.title || ''
}

export function getCameraTitle(key: string, id: number | string) {
  if (key.indexOf('-') !== -1) {
    key = key.replaceAll('-', '_')
  }
  const route: keyof typeof DATA_TRAFFIC_MAP = key as keyof typeof DATA_TRAFFIC_MAP
  let title = ''
  for (let i = 0; i < DATA_TRAFFIC_MAP[route].length; i++) {
    const camera = DATA_TRAFFIC_MAP[route][i]
    // eslint-disable-next-line eqeqeq
    if (camera.id === id) {
      title = camera.value
      break
    }
  }
  return title
}

// N,S,2,P,D,L,G,O,W,T,St,F,E,B,K,V
// AUTOBRENNERO,HAK,DARS,EVISNOE,EVISOOE,ITS,LANDOOE,LANDTIROL,UNGARN
const STATE_DATA = [
  {
    name: 'Wien',
    code: 'W',
    meta: 'wien',
    source: ['ITS'],
  },
  {
    name: 'Burgenland',
    code: 'B',
    meta: 'bgld',
    source: ['UNGARN'],
  },
  {
    name: 'Niederösterreich',
    code: 'N',
    meta: 'noe',
    source: ['EVISNOE'],
  },
  {
    name: 'Oberösterreich',
    code: 'O',
    aditionalCodes: 'N,S,T,W,St,K,B,V',
    meta: 'ooe',
    source: ['EVISOOE', 'LANDOOE', 'LANDTIROL', 'EVISS'],
  },
  {
    name: 'Steiermark',
    code: 'St',
    meta: 'stmk',
    source: [],
  },
  {
    name: 'Salzburg',
    code: 'S',
    meta: 'sbg',
    source: ['AUTOBRENNERO', 'DARS', 'EVISS'],
  },
  {
    name: 'Tirol',
    code: 'T',
    meta: 'tirol',
    source: ['LANDTIROL'],
  },
  {
    name: 'Kärnten',
    code: 'K',
    meta: 'ktn',
    source: [],
  },
  {
    name: 'Vorarlberg',
    code: 'V',
    meta: 'vab',
    source: [],
  },
]

export function getStateData(state: string) {
  for (let i = 0; i < STATE_DATA.length; i++) {
    const st = STATE_DATA[i]
    if (st.meta === state) {
      return st
    }
  }
}
