const COLLECTIONS_MAPPING = {
  rss_2_ads: { redisPrefix: 'rss2ads', cdnRoute: '/v1/content/rss2ads' },
  voting_challenge: { redisPrefix: 'voting-challenge', cdnRoute: '/v1/content/coting-challenge' },
}

export interface CollectionCacheObject {
  redisPrefix: string
  cdnRoute: string
}

export function getCollectionCacheObject(collection: string): CollectionCacheObject {
  const collectionKey = collection as keyof typeof COLLECTIONS_MAPPING
  return COLLECTIONS_MAPPING[collectionKey]
}
