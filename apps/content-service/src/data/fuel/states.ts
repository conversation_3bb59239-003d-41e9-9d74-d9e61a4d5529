enum STATES {
  burgenland = 1,
  ka<PERSON><PERSON> = 2,
  niede<PERSON><PERSON><PERSON>ich = 3,
  oberoesterreich = 4,
  salzburg = 5,
  steiermark = 6,
  tirol = 7,
  vorarlberg = 8,
  wien = 9,
}

export function getStateCode(state: string): number {
  const stateKey: keyof typeof STATES = state.toLocaleLowerCase() as keyof typeof STATES
  return STATES[stateKey] || -1
}

export function getStateNameByValue(v: number) {
  const keys1 = Object.keys(STATES)
  let index = -1
  const values1 = Object.values(STATES)
  values1.forEach((value, i) => {
    if (value === v) {
      index = i
    }
  })
  let name = keys1[index]
  name = name.charAt(0).toUpperCase() + name.slice(1)
  name = name.replace('oe', 'ö')
  name = name.replace('ae', 'ä')
  return name
}
