import { controller, middlewares } from '@regionalmedienaustria/microservice-utils'
import { Router } from 'express'

import { AirQualityController } from '@/controllers/airQualityController.js'

const airQualityRouter = Router()

airQualityRouter.get(
  '/',
  middlewares.withCache({
    public: true,
    maxAge: '1hour',
    sMaxAge: '6hours',
    staleWhileRevlalidate: '1day',
  }),
  controller.wrapController(AirQualityController.getAirQuality),
)

export { airQualityRouter }
