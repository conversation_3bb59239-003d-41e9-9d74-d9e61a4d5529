import { Router } from 'express'

import { adsConfigRouter } from './adsConfig.js'
import adventCalendarRouter from './advent.js'
import { airQualityRouter } from './airQuality.js'
import { cacheRouter } from './cache.js'
import { captchaRouter } from './captcha.js'
import fuelRouter from './fuel.js'
import { gallerySponsoringRouter } from './gallerySponsoring.js'
import { pharmacyRouter } from './pharmacy.js'
import { rss2adsRouter } from './rss2ads.js'
import trafficRouter from './traffic.js'
import { voteableUploadRouter } from './voteableUpload.js'
import { votingChallengeRouter } from './votingChallenge.js'
import { votingResultRouter } from './votingResult.js'

const routes = Router()

routes.use('/v1/content/captcha/', captchaRouter)
routes.use('/v1/content/voteable-upload/', voteableUploadRouter)
routes.use('/v1/content/voting-challenge/', votingChallengeRouter)
routes.use('/v1/content/gallery-sponsoring/', gallerySponsoringRouter)
routes.use('/v1/content/ads-config/', adsConfigRouter)
routes.use('/v1/content/fuel/', fuelRouter)
routes.use('/v1/content/traffic/', trafficRouter)
routes.use('/v1/content/pharmacy/', pharmacyRouter)
routes.use('/v1/content/voting-result/', votingResultRouter)
routes.use('/v1/content/advent-calendar/', adventCalendarRouter)
routes.use('/v1/content/rss2ads/', rss2adsRouter)
routes.use('/v1/content/cache/', cacheRouter)
routes.use('/v1/content/air-quality/', airQualityRouter)

export { routes }
