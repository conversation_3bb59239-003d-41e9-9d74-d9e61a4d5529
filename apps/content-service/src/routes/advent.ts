import { controller, middlewares } from '@regionalmedienaustria/microservice-utils'
import express from 'express'

import { AdventCalendarController } from '@/controllers/adventCalendarController.js'

const adventCalendarRouter = express.Router()

adventCalendarRouter.get(
  '/:id',
  middlewares.withCache({ public: true, maxAge: '1hour', sMaxage: '24hours' }),
  controller.wrapController(AdventCalendarController.getById),
)

export default adventCalendarRouter
