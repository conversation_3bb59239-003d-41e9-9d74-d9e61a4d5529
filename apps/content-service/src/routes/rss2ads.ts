import { controller, middlewares } from '@regionalmedienaustria/microservice-utils'
import { Router } from 'express'

import { RSS2AdsController } from '@/controllers/rss2adsController.js'

const rss2adsRouter = Router()

rss2adsRouter.get(
  '/:id',
  middlewares.withCache({
    public: true,
    maxAge: '1hour',
    sMaxage: '24hours',
  }),
  controller.wrapController(RSS2AdsController.getbyId),
)

export { rss2adsRouter }
