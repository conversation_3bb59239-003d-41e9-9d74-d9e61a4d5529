import { controller } from '@regionalmedienaustria/microservice-utils'
import { Router } from 'express'

import { CacheController } from '@/controllers/cacheController.js'

const cacheRouter = Router()

// Clear a specific item in a collection
cacheRouter.get('/clear/:collection/:id', controller.wrapController(CacheController.clearCache))

// Clear all items in a specific collection (legacy pattern)
cacheRouter.get(
  '/clear/:collection',
  controller.wrapController(CacheController.clearCollectionCache),
)

// Clear all items in all collections
cacheRouter.get('/clear-all', controller.wrapController(CacheController.clearAllCache))

export { cacheRouter }
