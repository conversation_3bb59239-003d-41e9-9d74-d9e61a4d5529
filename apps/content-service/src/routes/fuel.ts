import { controller, middlewares } from '@regionalmedienaustria/microservice-utils'
import express from 'express'

import StationController from '@/controllers/stationController.js'

const fuelRouter = express.Router()

fuelRouter.get(
  '/gas-stations',
  middlewares.withCache({
    public: true,
    maxAge: '15min',
  }),
  controller.wrapController(StationController.getStations),
)

fuelRouter.get(
  '/gas-stations/national',
  middlewares.withCache({
    public: true,
    maxAge: '15min',
  }),
  controller.wrapController(StationController.getAllStations),
)

fuelRouter.get(
  '/gas-stations/district',
  middlewares.withCache({
    public: true,
    maxAge: '15min',
  }),
  controller.wrapController(StationController.getStationsByDistrictName),
)

fuelRouter.get(
  '/gas-stations/state',
  middlewares.withCache({
    public: true,
    maxAge: '15min',
  }),
  controller.wrapController(StationController.getStationsByStateName),
)

export default fuelRouter
