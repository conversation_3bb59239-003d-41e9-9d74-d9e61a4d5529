import { controller, middlewares } from '@regionalmedienaustria/microservice-utils'
import express from 'express'

import { VotingChallengeController } from '@/controllers/votingChallengeController.js'

const votingChallengeRouter = express.Router()

votingChallengeRouter.get(
  '/:id',
  middlewares.withCache({
    public: true,
    maxAge: '1min',
    sMaxAge: '15min',
    staleWhileRevalidate: '1day',
  }),
  controller.wrapController(VotingChallengeController.getByIdOrSlug),
)

votingChallengeRouter.get(
  '/:id/items',
  middlewares.withCache({
    public: true,
    maxAge: '1min',
    sMaxAge: '15min',
    staleWhileRevalidate: '1day',
  }),
  controller.wrapController(VotingChallengeController.getVoteableItemsForChallenge),
)

export { votingChallengeRouter }
