import { controller, middlewares } from '@regionalmedienaustria/microservice-utils'
import express from 'express'

import TrafficController from '@/controllers/trafficController.js'

const trafficRouter = express.Router()

trafficRouter.get(
  '/webcams',
  middlewares.withCache({
    public: true,
    maxAge: '15min',
  }),
  controller.wrapController(TrafficController.getWebcamData),
)

trafficRouter.get(
  '/webcams/html',
  middlewares.withCache({
    public: true,
    maxAge: '15min',
  }),
  controller.wrapController(TrafficController.getWebcamHtml),
)

trafficRouter.get(
  '/route',
  middlewares.withCache({
    public: true,
    maxAge: '15min',
  }),
  controller.wrapController(TrafficController.getTrafficList),
)

export default trafficRouter
