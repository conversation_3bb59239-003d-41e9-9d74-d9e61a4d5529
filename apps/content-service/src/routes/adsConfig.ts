import { controller, middlewares } from '@regionalmedienaustria/microservice-utils'
import { Router } from 'express'

import { AdsConfigController } from '@/controllers/adsConfigController.js'

export const adsConfigRouter = Router()

adsConfigRouter.get(
  '/ads.txt',
  middlewares.withCache({
    public: true,
    maxAge: '1day',
  }),
  controller.wrapController(AdsConfigController.getMergedAdsTxt),
)
