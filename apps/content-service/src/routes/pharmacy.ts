import { Cache } from '@regionalmedienaustria/cache-util'
import { middlewares } from '@regionalmedienaustria/microservice-utils'
import { Router } from 'express'

import { PharmacyController } from '@/controllers/pharmacyController.js'

const pharmacyRouter = Router()

pharmacyRouter.get(
  '/',
  middlewares.withCache({
    maxAge: Cache.CONSTANTS.ONE_DAY,
    sMaxAge: Cache.CONSTANTS.ONE_DAY,
    staleWhileRevalidate: Cache.CONSTANTS.ONE_DAY * 3,
  }),
  PharmacyController.getPharmacies,
)

export { pharmacyRouter }
