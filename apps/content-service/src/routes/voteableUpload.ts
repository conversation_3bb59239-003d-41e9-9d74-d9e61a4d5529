import { controller, middlewares } from '@regionalmedienaustria/microservice-utils'
import express from 'express'
import multer from 'multer'

import { VoteableUploadController } from '@/controllers/voteableUploadController.js'

const voteableUploadRouter = express.Router()

const upload = multer({
  limits: {
    fileSize: 32 * 1024 * 1024, // 32MB limit
  },
})

voteableUploadRouter.post(
  '/:id/vote',
  controller.wrapController(VoteableUploadController.voteForItem),
)
voteableUploadRouter.get(
  '/:id/minimal',
  middlewares.withCache({
    public: true,
    maxAge: '1min',
    staleWhileRevalidate: '1day',
  }),
  controller.wrapController(VoteableUploadController.getMinimalVoteableUpload),
)
voteableUploadRouter.post(
  '/',
  upload.single('file'),
  controller.wrapController(VoteableUploadController.uploadItem),
)

voteableUploadRouter.put(
  '/',
  upload.none(),
  controller.wrapController(VoteableUploadController.updateFile),
)

export { voteableUploadRouter }
