import { controller, middlewares } from '@regionalmedienaustria/microservice-utils'
import express from 'express'

import { GalleryController } from '@/controllers/galleryController.js'

const gallerySponsoringRouter = express.Router()

gallerySponsoringRouter.get(
  '/:state',
  middlewares.withCache({
    public: true,
    maxAge: '1hour',
  }),
  controller.wrapController(GalleryController.loadSponsorings),
)

export { gallerySponsoringRouter }
