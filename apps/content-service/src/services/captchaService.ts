import axios from 'axios'

export class CaptchaService {
  private static secret = process.env.FRIENDLY_CAPTCHA_API_KEY

  private static siteKey = process.env.FRIENDLY_CAPTCHA_SITE_KEY

  private static readonly client = axios.create({
    baseURL: 'https://api.friendlycaptcha.com/api/v1',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
  })

  public static async validateSolution({ solution }: { solution: string }): Promise<
    | {
        success: true
      }
    | {
        success: false
        details: string
        errors: string
      }
  > {
    // https://docs.friendlycaptcha.com/#/verification_api?id=the-verification-response
    const errorCodeMapping = {
      bad_request: 'Something else is wrong with your request, e.g. your request body is empty.',
      secret_missing: 'You forgot to add the secret (=API key) parameter.',
      secret_invalid: 'The API key you provided was invalid.',
      solution_missing: 'You forgot to add the solution parameter.',
      solution_invalid:
        'The solution you provided was invalid (perhaps the user tried to tamper with the puzzle).',
      solution_timeout_or_duplicate:
        'The puzzle that the solution was for has expired or has already been used.',
    }

    const response = await this.client.post<
      | { success: true; error: undefined }
      | {
          success: false
          details: string
          errors: (keyof typeof errorCodeMapping)[]
        }
    >('siteverify', {
      solution,
      secret: this.secret,
      sitekey: this.siteKey,
    })

    if (response.data.success) {
      return {
        success: true,
      }
    }

    const errors = response.data.errors.map((error) => errorCodeMapping[error]).join(', ')
    const details = response.data.details ? ` Details: ${response.data.details}` : ''

    return {
      success: false,
      details,
      errors,
    }
  }
}
