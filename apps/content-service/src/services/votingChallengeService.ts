import { Cache } from '@regionalmedienaustria/cache-util'
import { aggregate, readItems } from '@rma-mono/directus-client'

import { getVotingCache } from '@/cache/votingCache.js'
import { votingClient } from '@/utils/directus/client.js'

const VOTING_CHALLENGE_TTL = Cache.CONSTANTS.ONE_HOUR

type IdOrSlugProps = {
  id?: number | undefined
  slug?: string | undefined
}

export class VotingChallengeService {
  public static async getChallenge(props: { bypassCache?: boolean } & IdOrSlugProps) {
    const cache = await getVotingCache()

    const data = await cache.getWithFunction({
      key: `voting-challenge-${props.id ?? props.slug}`,
      storeFunction: async () =>
        votingClient.request(
          readItems('voting_challenge', {
            fields: [
              '*',
              {
                sponsor_logo: [
                  {
                    directus_files_id: ['id', 'width', 'height', 'title'],
                  },
                ],
                contact_fields: ['*', { rma_input_entity_id: ['*'] }],
                personal_data_fields: ['*', { rma_input_entity_id: ['*'] }],
                further_information_fields: ['*', { rma_input_entity_id: ['*'] }],
                title_input: ['*'],
                email_input: ['*'],
              },
            ],
            filter: this.getIdOrSlugFilter(props),
          }),
        ),
      ttl: VOTING_CHALLENGE_TTL,
      staleWhileRevalidate: true,
      force: props.bypassCache,
    })

    return data.length === 0 ? null : data[0]
  }

  public static async getChallengeSnippet(props: IdOrSlugProps) {
    const cache = await getVotingCache()

    const data = await cache.getWithFunction({
      key: `voting-challenge-snippet-${props.id ?? props.slug}`,
      storeFunction: async () =>
        votingClient.request(
          readItems('voting_challenge', {
            fields: [
              'id',
              'voteable_from',
              'voteable_till',
              'voting_text',
              'result_date',
              'title',
              'slug',
            ],
            filter: this.getIdOrSlugFilter(props),
          }),
        ),
      ttl: VOTING_CHALLENGE_TTL,
      staleWhileRevalidate: true,
    })

    return data.length === 0 ? null : data[0]
  }

  public static async getVoteableItemsForChallenge(
    props: { bypassCache?: boolean } & IdOrSlugProps,
  ) {
    const cache = await getVotingCache()

    const data = await cache.getWithFunction({
      key: `voting-challenge-voteable-items-${props.id ?? props.slug}`,
      storeFunction: async () => {
        const [votingChallenges, voteableUploads, voteCounts] = await Promise.all([
          votingClient.request(
            readItems('voting_challenge', {
              fields: [
                'id',
                'title',
                'voteable_from',
                'voteable_till',
                'sort',
                'voting_text',
                'result_date',
              ],
              filter: this.getIdOrSlugFilter(props),
            }),
          ),
          votingClient.request(
            readItems('voteable_upload', {
              fields: [
                'id',
                'title',
                'state',
                {
                  image: ['id', 'width', 'height', 'title'],
                },
              ],
              filter: {
                voting_challenge: this.getIdOrSlugFilter(props),
              },
              limit: 10,
            }),
          ),
          votingClient.request(
            aggregate('vote', {
              groupBy: ['voteable_upload'],
              aggregate: { count: '*' },
              filter: {
                voteable_upload: {
                  voting_challenge: this.getIdOrSlugFilter(props),
                },
              },
            }),
          ),
        ])

        if (votingChallenges.length === 0) {
          return null
        }

        // use an object for better serialization/deserialization in cache
        const voteCountMap: Record<string, number> = {}
        voteCounts.forEach((voteCount) => {
          // also make sure that the key is a string and the value is a number otherwise it won't be serialized and ts will yell at us
          voteCountMap[String(voteCount.voteable_upload)] = Number(voteCount.count) || 0
        })

        const challenge = votingChallenges[0]
        return {
          ...challenge,
          uploads: voteableUploads,
          voteCountMap,
        }
      },
      ttl: VOTING_CHALLENGE_TTL,
      staleWhileRevalidate: true,
      force: props.bypassCache,
    })

    if (!data) {
      return null
    }

    return {
      id: data.id,
      title: data.title,
      voteableFrom: data.voteable_from,
      voteableUntil: data.voteable_till,
      votingText: data.voting_text,
      resultDate: data.result_date,
      uploads: data.uploads.map((upload) => ({
        id: upload.id,
        title: upload.title,
        state: upload.state,
        votesCount: data.voteCountMap[upload.id] || 0,
        image: upload.image,
      })),
    }
  }

  private static getIdOrSlugFilter(props: IdOrSlugProps) {
    return props.id
      ? {
          id: {
            _eq: Number(props.id),
          },
        }
      : { slug: { _eq: props.slug } }
  }
}
