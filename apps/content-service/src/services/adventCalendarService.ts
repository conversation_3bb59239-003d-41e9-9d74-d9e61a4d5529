import { readItems } from '@rma-mono/directus-client'

import { getCache } from '@/cache/cache.js'
import { publicClient } from '@/utils/directus/client.js'

import type { AdventCalendar } from '@rma-mono/directus-client'

const ADVENT_CALENDAR_TTL = 60 * 60 * 24 // 24 hours

export class AdventCalendarService {
  public static async getCalendar(calendarId: number): Promise<AdventCalendar | null> {
    const cache = await getCache()

    const data = await cache.getWithFunction({
      key: `advent-calendar-${calendarId}`,
      storeFunction: async () =>
        publicClient.request(
          readItems('advent_calendar', {
            fields: [
              'id',
              'title',
              'wrong_day_message',
              'background_image',
              'advent_testing_date',
              {
                advent_calendar_doors: [
                  {
                    advent_calendar_door_id: [
                      'headline',
                      'content_description',
                      'follow_url',
                      { content_file: ['id', 'title', 'type', 'filesize', 'width', 'height'] },
                    ],
                  },
                ],
              },
            ],
            filter: { id: { _eq: calendarId } },
          }),
        ),
      ttl: ADVENT_CALENDAR_TTL,
    })

    const calendar = data.length === 0 ? null : (data[0] as AdventCalendar)

    if (calendar) {
      calendar.doors_order = this.randomizeDoorsOrder()
    }

    return calendar
  }

  private static randomizeDoorsOrder(): number[] {
    return Array.from({ length: 24 }, (_, i) => i + 1).sort(() => Math.random() - 0.5)
  }
}
