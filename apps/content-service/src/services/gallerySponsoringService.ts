import { readItems } from '@rma-mono/directus-client'

import { getCache } from '@/cache/cache.js'
import { publicClient } from '@/utils/directus/client.js'

export class GallerySponsoringService {
  public static async loadSponsoringIds(state: string): Promise<Array<Number>> {
    // use cache because of the possibility of different CDN locations
    const cache = await getCache()
    return cache.getWithFunction({
      key: `gallerySponsoring-${state}`,
      storeFunction: this.fetchSponsoringIdsFromContentHub,
      params: [state],
      ttl: 60 * 60,
    })
  }

  private static async fetchSponsoringIdsFromContentHub(state: string) {
    const result = await publicClient.request(
      readItems('locked_galleries', { fields: ['locked_ids'], filter: { state: { _eq: state } } }),
    )
    return result[0]?.locked_ids?.map((m) => m.article_id) || []
  }
}
