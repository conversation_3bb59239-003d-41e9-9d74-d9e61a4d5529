import qs from 'querystring'

import { logger } from '@regionalmedienaustria/microservice-utils'
import axios from 'axios'

import { getTrafficCache } from '@/cache/trafficCache.js'
import { getDistrictObject } from '@/data/fuel/districts.js'
import { getStateCode } from '@/data/fuel/states.js'
import StationHtmlService from '@/services/stationHtmlService.js'

import type { Cache } from '@regionalmedienaustria/cache-util'
import type { AxiosRequestConfig } from 'axios'

export interface EControlStationResult {
  name: 'string'
  location: {
    address: 'string'
    city: 'string'
    latitude: number
    longitude: number
    postalCode: 'string'
  }
  contact: {
    fax: 'string'
    mail: 'string'
    telephone: 'string'
    website: 'string'
  }
  prices: {
    amount: number
    fuelType: 'string'
    label: 'string'
  }[]
  cacheTs: number
}

export interface StationsResult {
  dieStations?: EControlStationResult[]
  supStations?: EControlStationResult[]
  html: string
  cacheTs?: number
}

interface StationParams {
  address?: {
    latitude: number
    longitude: number
    fuelType: string
  }
  region?: {
    code: string
    type: string
    fuelType: string
  }
  prefix: string
}

const CACHE_TTL = 60 * 60 // 1hr
const ECONTROLURL = 'https://api.e-control.at/sprit/1.0/search/gas-stations'
const REFRESH_CHACHE_AFTER = 60 * 12
const HEADERS = {
  Accept: '*/*',
  'Content-Type': 'application/json',
}

export default class EControlService {
  private static cache: Cache.RmaCache

  private static async getStationsFromCache(
    stationParams: StationParams,
  ): Promise<EControlStationResult[]> {
    this.cache = await getTrafficCache()
    const data = await this.cache.getWithFunction({
      key: EControlService.getCacheKey(stationParams),
      storeFunction: EControlService.getStationsFromEControl,
      params: [stationParams],
      ttl: CACHE_TTL,
    })

    if (
      // @ts-expect-error  - no idea what is happening
      data.length > 0 &&
      // @ts-expect-error  - no idea what is happening
      data[0].cacheTs &&
      // @ts-expect-error  - no idea what is happening
      (Date.now() - data[0].cacheTs) / 1000 > REFRESH_CHACHE_AFTER
    ) {
      this.cache.getWithFunction({
        key: EControlService.getCacheKey(stationParams),
        storeFunction: EControlService.getStationsFromEControl,
        params: [stationParams],
        ttl: CACHE_TTL,
      })
    }

    // @ts-expect-error  - no idea what is happening
    return data
  }

  private static async getStationsFromEControl(
    stationParams: StationParams,
  ): Promise<EControlStationResult | []> {
    let url = ECONTROLURL
    let qparams = {}
    if (stationParams.address !== undefined) {
      url += '/by-address'
      qparams = qs.stringify(stationParams.address)
    } else if (stationParams.region !== undefined) {
      url += '/by-region'
      qparams = qs.stringify(stationParams.region)
    }
    const reqOptions: AxiosRequestConfig = {
      url: `${url}?${qparams}`,
      method: 'GET',
      headers: HEADERS,
    }
    try {
      const response = await axios.request(reqOptions)
      const stations = await EControlService.filterStations(response.data)
      stations.cacheTs = Date.now()
      return stations
    } catch (error) {
      logger.error(error)
      return []
    }
  }

  public static async getStationsByAddress(
    latitude: number,
    longitude: number,
    prefix: string,
  ): Promise<StationsResult> {
    const stationParams: StationParams = {
      address: { latitude: latitude, longitude: longitude, fuelType: 'DIE' },
      prefix: prefix,
    }
    const stationsResult: StationsResult = {
      dieStations: [],
      supStations: [],
      html: '',
    }
    stationsResult.dieStations = await EControlService.getStationsFromCache(stationParams)
    if (stationParams.address) {
      stationParams.address.fuelType = 'SUP'
    }
    stationsResult.supStations = await EControlService.getStationsFromCache(stationParams)
    stationsResult.html = await StationHtmlService.getStationsHtml(stationsResult, 'address')
    return stationsResult
  }

  public static async getStationsByRegion(
    code: string,
    type: string,
    prefix: string,
  ): Promise<StationsResult> {
    const stationParams: StationParams = {
      region: {
        code: code,
        type: type,
        fuelType: 'DIE',
      },
      prefix: prefix,
    }
    let stationsResult: StationsResult = {
      dieStations: [],
      supStations: [],
      html: '',
    }
    stationsResult.dieStations = await EControlService.getStationsFromCache(stationParams)
    if (stationParams.region) {
      stationParams.region.fuelType = 'SUP'
    }
    stationsResult.supStations = await EControlService.getStationsFromCache(stationParams)
    stationsResult = await EControlService.prepareStations(stationsResult)
    stationsResult.html = await StationHtmlService.getStationsHtml(stationsResult, 'region')
    return stationsResult
  }

  private static async getAllStationsFromEControl(): Promise<StationsResult> {
    const dieselStations: EControlStationResult[] = []
    const superStations: EControlStationResult[] = []
    const stationsResult: StationsResult = {
      dieStations: dieselStations,
      supStations: superStations,
      html: '',
    }
    const promQdie = []
    const promQsup = []
    for (let i = 1; i <= 9; i++) {
      promQdie.push(EControlService.fetchStations(i.toString(), 'DIE'))
      promQsup.push(EControlService.fetchStations(i.toString(), 'SUP'))
    }

    const dieResults = await Promise.all(promQdie)
    const supResults = await Promise.all(promQsup)

    for (let i = 0; i < dieResults.length; i++) {
      if (dieResults[i].length === 0 || supResults[i].length === 0) {
        // no data from e-control
        return stationsResult
      }
      stationsResult.dieStations?.push(dieResults[i])
      stationsResult.supStations?.push(supResults[i])
    }
    stationsResult.cacheTs = Date.now()
    return stationsResult
  }

  private static async fetchStations(code: string, fuelType: string): Promise<any> {
    const params = {
      type: 'BL',
      code: code,
      fuelType: fuelType,
    }
    const reqOptions: AxiosRequestConfig = {
      url: `${ECONTROLURL}/by-region?${qs.stringify(params)}`,
      method: 'GET',
      headers: HEADERS,
    }
    try {
      const response = await axios.request(reqOptions)
      const filtered = await EControlService.filterStations(response.data)
      const stations: any = filtered
      stations.sort(EControlService.sortStationsByPrice)
      return stations.splice(0, 5)
    } catch (error) {
      logger.error(error)
      return []
    }
  }

  private static async getAllStationsFromCache(): Promise<StationsResult> {
    this.cache = await getTrafficCache()
    const data = await this.cache.getWithFunction({
      key: 'national-all-stations',
      storeFunction: EControlService.getAllStationsFromEControl,
      params: undefined,
      ttl: CACHE_TTL,
    })

    if (data.cacheTs && (Date.now() - data.cacheTs) / 1000 > REFRESH_CHACHE_AFTER) {
      this.cache.getWithFunction({
        key: 'national-all-stations',
        storeFunction: EControlService.getAllStationsFromEControl,
        params: undefined,
        ttl: CACHE_TTL,
      })
    }
    return data
  }

  public static async getAllStations(): Promise<StationsResult> {
    const stationsResult = await EControlService.getAllStationsFromCache()
    stationsResult.dieStations = stationsResult.dieStations?.flat()
    stationsResult.supStations = stationsResult.supStations?.flat()
    stationsResult.html = await StationHtmlService.getStationsHtmlFromCache(
      stationsResult,
      'stations-html-national',
      true,
    )
    return stationsResult
  }

  public static async getStationsByDistrictName(districtName: string): Promise<StationsResult> {
    const district = getDistrictObject(districtName)
    let stationsResult: StationsResult = {
      dieStations: [],
      supStations: [],
      html: '',
    }
    const promQ = []
    if (district.ids) {
      for (let i = 0; i < district.ids.length; i++) {
        promQ.push(
          EControlService.getStationsByRegion(
            district.ids[i].toString(),
            'PB',
            `district-${districtName}`,
          ),
        )
      }
    } else if (district.locations) {
      for (let i = 0; i < district.locations.length; i++) {
        promQ.push(
          EControlService.getStationsByAddress(
            district.locations[i].lat,
            district.locations[i].lng,
            'district-address',
          ),
        )
      }
    }
    const results = await Promise.all(promQ)
    results.forEach((result) => {
      if (result.dieStations !== undefined) {
        const stations = result.dieStations
        stationsResult.dieStations?.push(...stations)
      }
      if (result.supStations !== undefined) {
        const stations = result.supStations
        stationsResult.supStations?.push(...stations)
      }
    })
    stationsResult = await EControlService.prepareStations(stationsResult)
    stationsResult.html = await StationHtmlService.getStationsHtml(stationsResult, districtName)
    return stationsResult
  }

  public static async getStationsByStateName(state: string): Promise<StationsResult> {
    const code = getStateCode(state)
    let stationsResult = await EControlService.getStationsByRegion(
      code.toString(),
      'BL',
      `state-${state}`,
    )
    stationsResult = await EControlService.prepareStations(stationsResult)
    stationsResult.html = await StationHtmlService.getStationsHtml(stationsResult, state)
    return stationsResult
  }

  private static getCacheKey(stationParams: StationParams): string {
    const key = [
      stationParams.prefix,
      stationParams.address?.latitude,
      stationParams.address?.longitude,
      stationParams.region?.code,
      stationParams.address?.fuelType,
      stationParams.region?.fuelType,
    ]
      .filter(Boolean)
      .join('-')
    return key
  }

  private static async filterStations(econtrolResult: any): Promise<EControlStationResult> {
    const filtered: any[] = econtrolResult.filter((el: any) => el.prices.length)

    const stations: any = filtered.map((station) => {
      const picked = (({ contact, location, name, prices, cacheTs }) => ({
        name,
        location,
        contact,
        prices,
        cacheTs,
      }))(station)
      return picked
    })
    return stations
  }

  private static sortStationsByPrice(statA: EControlStationResult, statB: EControlStationResult) {
    const priceA = statA.prices[0].amount
    const priceB = statB.prices[0].amount
    return priceA > priceB ? 1 : priceA < priceB ? -1 : 0
  }

  private static async prepareStations(stationsResult: StationsResult): Promise<StationsResult> {
    stationsResult.dieStations?.sort(EControlService.sortStationsByPrice)
    stationsResult.supStations?.sort(EControlService.sortStationsByPrice)

    if (stationsResult.dieStations && stationsResult.dieStations?.length > 10) {
      stationsResult.dieStations = stationsResult.dieStations.splice(0, 10)
    }
    if (stationsResult.supStations && stationsResult.supStations?.length > 10) {
      stationsResult.supStations = stationsResult.supStations.splice(0, 10)
    }
    return stationsResult
  }
}
