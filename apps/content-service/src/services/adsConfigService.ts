import { readItems } from '@rma-mono/directus-client'

import { publicClient } from '@/utils/directus/client.js'

import type { AdsConfig } from '@rma-mono/directus-client'

export class AdsConfigService {
  public static async getMergedAdsTxt(): Promise<string> {
    const adsConfig = await this.fetchAdsConfig()
    const mergedEntries = this.mergeAndDeduplicate(adsConfig)
    return mergedEntries.join('\n')
  }

  private static async fetchAdsConfig(): Promise<AdsConfig[]> {
    const response = await publicClient.request(
      readItems('ads_config', {
        fields: ['id', 'name', 'ad_entries'],
      }),
    )
    return response as AdsConfig[]
  }

  private static mergeAndDeduplicate(adsConfig: AdsConfig[]): string[] {
    const allEntries = adsConfig.flatMap((config) =>
      config.ad_entries
        .split('\n')
        .map((entry) => entry.trim())
        .filter((entry) => entry !== ''),
    )
    return [...new Set(allEntries)]
  }
}
