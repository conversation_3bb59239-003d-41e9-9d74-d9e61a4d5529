import { logger } from '@regionalmedienaustria/microservice-utils'
import { CacheClearingService } from '@rma-mono/cache-clearing-util'

import { getCache } from '@/cache/cache.js'
import { getCollectionCacheObject, getAllCollectionCacheObjects } from '@/cache/collections.js'

import type { CacheConfig } from '@rma-mono/cache-clearing-util'

/**
 * Service to manage cache invalidation
 * This class extends the existing service and integrates the new cache-clearing-util package
 */
export class CacheService {
  private static cacheClearingService: CacheClearingService | null = null

  /**
   * Initializes the CacheClearingService when needed
   */
  private static async getCacheClearingService(): Promise<CacheClearingService> {
    if (!this.cacheClearingService) {
      const cache = await getCache()
      this.cacheClearingService = new CacheClearingService(cache)
    }
    return this.cacheClearingService
  }

  /**
   * Existing API for backward compatibility
   * Deletes a cache entry based on Redis key and CDN route
   */
  public static async clearCache(redisKey: string, cdnRoute: string) {
    logger.info(`Legacy cache clearing for redis key: ${redisKey}, CDN route: ${cdnRoute}`)
    const config: CacheConfig = {
      redisPrefix: redisKey,
      cdnRoute: cdnRoute,
    }

    const service = await this.getCacheClearingService()
    await service.clearCache(config, '') // Empty ID, since the keys are passed directly
  }

  /**
   * New API for collection-based cache invalidation
   * Deletes the cache for a specific item of a collection
   */
  public static async clearCollectionItemCache(collection: string, id: string): Promise<boolean> {
    const config = getCollectionCacheObject(collection)
    if (!config) {
      const errorMsg = `Collection ${collection} not configured for cache clearing`
      logger.error(errorMsg)
      throw new Error(errorMsg)
    }

    try {
      logger.info(`Clearing cache for collection ${collection} with ID ${id}`)
      const service = await this.getCacheClearingService()
      await service.clearCache(config, id)
      return true
    } catch (error) {
      logger.error(`Error clearing cache for collection ${collection} with ID ${id}:`, error)
      throw error
    }
  }

  /**
   * Deletes the entire cache for a specific collection
   */
  public static async clearAllCollectionCache(collection: string): Promise<boolean> {
    const config = getCollectionCacheObject(collection)
    if (!config) {
      const errorMsg = `Collection ${collection} not configured for cache clearing`
      logger.error(errorMsg)
      throw new Error(errorMsg)
    }

    try {
      logger.info(`Clearing all cache for collection ${collection}`)
      const service = await this.getCacheClearingService()
      await service.clearAllCollectionCache(config)
      return true
    } catch (error) {
      logger.error(`Error clearing all cache for collection ${collection}:`, error)
      throw error
    }
  }

  /**
   * Deletes the cache for all configured collections
   */
  public static async clearAllCaches(): Promise<Record<string, boolean>> {
    const collections = getAllCollectionCacheObjects()
    const results: Record<string, boolean> = {}

    for (const [collection, config] of Object.entries(collections)) {
      try {
        const service = await this.getCacheClearingService()
        await service.clearAllCollectionCache(config)
        logger.info(`Cleared all cache for collection: ${collection}`)
        results[collection] = true
      } catch (error) {
        logger.error(`Failed to clear cache for collection ${collection}:`, error)
        results[collection] = false
      }
    }

    return results
  }
}
