import { Cache } from '@regionalmedienaustria/cache-util'
import { logger } from '@regionalmedienaustria/microservice-utils'
import { aggregate, createItem, readItem, updateItem } from '@rma-mono/directus-client'
import { createSchemaFromVotingChallenge } from '@rma-mono/voting-challenge-uploader/utils'
import { z } from 'zod'

import { getVotingCache } from '@/cache/votingCache.js'
import { getVotingMemoryCache } from '@/cache/votingMemoryCache.js'
import { uploadClient, votingClient } from '@/utils/directus/client.js'
import { uploadFileToDirectus } from '@/utils/directus/uploadFile.js'
import { GoogleCloudStorageUtil } from '@/utils/google-cloud/storage.js'
import { formatDate, hashString } from '@/utils/utils.js'

import { VotingChallengeService } from './votingChallengeService.js'

import type { GetSignedUrlConfig } from '@google-cloud/storage'
import type { VoteableUpload, VotingChallenge } from '@rma-mono/directus-client'

/**
 * Helper to parse the fieldOrder input or return a default order.
 * @param fieldOrderInput - The input value (string, array, or undefined)
 * @param defaultOrder - The default order to use if parsing fails or input is invalid
 * @returns The final field order array
 */
const getFieldOrderArray = (
  fieldOrderInput: string[] | string | undefined,
  defaultOrder: string[],
): string[] => {
  if (Array.isArray(fieldOrderInput)) {
    return fieldOrderInput
  }
  if (typeof fieldOrderInput === 'string') {
    try {
      const parsed = JSON.parse(fieldOrderInput)
      // Ensure the parsed value is actually an array of strings
      if (Array.isArray(parsed) && parsed.every((item) => typeof item === 'string')) {
        return parsed
      }
    } catch (e) {
      // Fall through to default if parsing fails or result is not a valid array
      logger.warn(`Failed to parse fieldOrderInput string: ${fieldOrderInput}`, e)
    }
  }
  // Return default if input is undefined, null, or invalid
  return defaultOrder
}

/**
 * Creates an object with keys arranged in a specific order.
 *
 * @param entries - Array of key-value pairs to be transformed into an ordered object
 * @param fieldOrderInput - Optional ordering specification, can be:
 *   - A string: JSON array of field names (will be parsed)
 *   - An array: Direct list of field names in desired order
 *   - Undefined: Will use original key order from entries
 *
 * @returns A new object with the same data but ordered as follows:
 *   1. The 'title' field (if it exists) always comes first
 *   2. Fields specified in fieldOrder (in that exact order)
 *   3. Any remaining fields not in fieldOrder (in their original order)
 *
 * Example:
 *   Input:
 *     entries: [['c', 3], ['a', 1], ['title', 'Title'], ['b', 2]]
 *     fieldOrderInput: ['a', 'b', 'c']
 *   Output:
 *     { 'title': 'Title', 'a': 1, 'b': 2, 'c': 3 }
 */
const createOrderedObject = (
  entries: [string, any][],
  fieldOrderInput?: string[] | string,
): Record<string, any> => {
  const data = Object.fromEntries(entries)
  const defaultOrder = entries.map(([key]) => key) // Preserve original order for fallback
  const fieldOrder = getFieldOrderArray(fieldOrderInput, defaultOrder)

  const orderedData: Record<string, any> = {}
  const addedKeys = new Set<string>()

  // 1. Add title first if it exists
  if (Object.prototype.hasOwnProperty.call(data, 'title')) {
    orderedData.title = data.title
    addedKeys.add('title')
  }

  // 2. Add keys specified in fieldOrder (respecting the order)
  fieldOrder.forEach((key) => {
    // Check if key exists in the original data and hasn't been added yet
    if (Object.prototype.hasOwnProperty.call(data, key) && !addedKeys.has(key)) {
      orderedData[key] = data[key]
      addedKeys.add(key)
    }
  })

  // 3. Add any remaining keys from the original data that were not in fieldOrder
  Object.keys(data).forEach((key) => {
    if (!addedKeys.has(key)) {
      orderedData[key] = data[key]
      // No need to add to addedKeys here, just ensuring all data keys are present
    }
  })

  return orderedData
}

const IP_CACHE_TTL = Cache.CONSTANTS.ONE_DAY
const VOTEABLE_UPLOAD_TTL = Cache.CONSTANTS.ONE_HOUR

const storageUtil = new GoogleCloudStorageUtil(process.env.GCS_DIRECTUS_MEDIA_UPLOADER_SA)

const directusMediaBucket = process.env.GCS_DIRECTUS_MEDIA_BUCKET
if (!directusMediaBucket) {
  throw new Error('No Directus Media Bucket defined')
}

const baseVoteableUploadDataValidator = z.object({
  title: z.string(),
  challengeSlug: z.string(),
  challengeId: z.string(),
  state: z.enum([
    'Niederösterreich',
    'Wien',
    'Burgenland',
    'Steiermark',
    'Kärnten',
    'Oberösterreich',
    'Salzburg',
    'Tirol',
    'Vorarlberg',
    'Österreich',
  ]),
  otherData: z.string(),
  fieldOrder: z.string().optional(),
  email: z.string().email().optional(),
})

const fileFieldsValidator = z.object({
  fileContentType: z.string(),
  originalFileName: z.string(),
})

const fullVoteableUploadDataValidator = baseVoteableUploadDataValidator.merge(fileFieldsValidator)
type FullVoteableUploadData = z.infer<typeof fullVoteableUploadDataValidator>

const updateFileDataValidator = z.object({
  // fileName of the client-side uploaded file
  fileName: z.string(),
  voteableUploadId: z.string(),
})

export class VoteableUploadService {
  public static async uploadItem({
    requestData,
    file,
    bypassCache,
  }: {
    requestData: Record<string, string>
    file?: Express.Multer.File
    bypassCache?: boolean
  }) {
    try {
      const votingChallenge = await VotingChallengeService.getChallenge({
        slug: requestData.challengeSlug,
        id: Number(requestData.challengeId),
        bypassCache,
      })

      if (!votingChallenge) {
        throw new Error('Something went wrong fetching the voting challenge')
      }

      const voteableUploadDataValidator = votingChallenge.upload_enabled
        ? fullVoteableUploadDataValidator
        : baseVoteableUploadDataValidator

      const data = voteableUploadDataValidator.parse(requestData)
      const { otherData, ...rest } = data

      // DEBUG: Log the received fieldOrder string
      logger.debug('Received data.fieldOrder string:', data.fieldOrder)

      // Parse the JSON string and create ordered data
      const parsedOtherData = JSON.parse(otherData)
      const orderedData = createOrderedObject(Object.entries(parsedOtherData), data.fieldOrder)

      // Validate data directly without stripping prefixes
      const schemaValidator = createSchemaFromVotingChallenge(votingChallenge as VotingChallenge)
      schemaValidator.parse(parsedOtherData)

      const isImage = votingChallenge.mediatype === 'image'
      const uploadedFile = file
        ? await uploadFileToDirectus({
            file,
            name: `${rest.title}-${formatDate(new Date())}`,
            folderPath: `Allgemein/VotingChallenges/${votingChallenge.title}`,
          })
        : undefined
      const fileUrl = uploadedFile ? `${process.env.CMS_URL}/assets/${uploadedFile.id}` : undefined

      // Use the ordered data for storage
      // Determine field order - either from provided data or generate a default
      let fieldOrderArray: string[]

      if (data.fieldOrder) {
        // Use provided field order if available
        fieldOrderArray = JSON.parse(data.fieldOrder)
      } else {
        // Otherwise create default field order:
        // Start with common fields, then add all other fields except internal ones
        fieldOrderArray = [
          'upload_fileinfo',
          'title',
          ...Object.keys(orderedData).filter((k) => k !== '__fieldOrder'),
        ]
      }

      const voteableUpload = await uploadClient.request(
        createItem('voteable_upload', {
          ...rest,
          other_data: orderedData,
          field_order: fieldOrderArray,
          voting_challenge: votingChallenge.id,
          video_link: !isImage ? fileUrl : undefined,
          image: isImage && uploadedFile ? uploadedFile.id : undefined,
        }),
      )

      if (!voteableUpload.id) {
        throw new Error('Something went wrong with the entry creation')
      }

      if (!file && votingChallenge.upload_enabled) {
        const { url, fileName } = await this.getSignedUrlForFileUpload({
          // Note: TS can't infer the type correctly and changing the logic is out of scope for this fix
          data: data as FullVoteableUploadData,
        })
        return {
          uploadUrl: url,
          fileName,
          voteableUploadId: voteableUpload.id,
        }
      }

      return {
        voteableUploadId: voteableUpload.id,
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation Error: ${error.message}`)
      } else {
        logger.error(error)
        const message = error instanceof Error ? error.message : 'Something went wrong'
        throw new Error(`Error: ${message}`)
      }
    }
  }

  public static async voteForItem({ itemId }: { itemId: number }) {
    const newVote = await votingClient.request(
      createItem('vote', {
        voteable_upload: itemId,
      }),
    )

    if (!newVote.id) {
      throw new Error('Something went wrong with the vote creation')
    }

    return newVote.id
  }

  static async setVoteForIp({ votingGroup, remoteIp }: { votingGroup: string; remoteIp: string }) {
    const [cache, memoryCache] = await Promise.all([getVotingCache(), getVotingMemoryCache()])
    const hash = hashString(remoteIp)
    const now = new Date().getTime()

    await Promise.all([
      cache.set(
        hash,
        {
          votedAt: now,
        },
        IP_CACHE_TTL,
        votingGroup,
      ),
      memoryCache.set(
        hash,
        {
          votedAt: now,
        },
        IP_CACHE_TTL,
        votingGroup,
      ),
    ])
  }

  static async hasVotedWithin24Hours({
    votingGroup,
    remoteIp,
  }: {
    votingGroup: string
    remoteIp: string
  }) {
    const [cache, memoryCache] = await Promise.all([getVotingCache(), getVotingMemoryCache()])

    //hash remote ip
    const hash = hashString(remoteIp)

    logger.info(`Checking if IP Hash ${hash} has voted within the last 24 hours`)
    const [cacheRes, memoryCacheRes] = await Promise.all([
      cache.get<
        | {
            votedAt: number
          }
        | undefined
      >(hash, votingGroup),
      memoryCache.get<
        | {
            votedAt: number
          }
        | undefined
      >(hash, votingGroup),
    ])

    if (!cacheRes && !memoryCacheRes) {
      return {
        ipHash: hash,
        hasVotedRecently: false,
      }
    }

    //if cacheRes or memoryCache is not undefined, the user has voted within the last 24 hours (because TTL is 24 hours)
    return {
      ipHash: hash,
      hasVotedRecently: true,
      votedAt: cacheRes?.votedAt || memoryCacheRes?.votedAt,
    }
  }

  /**
   * Check if item belongs to the correct voting challenge and if the voting period is valid
   * @returns Promise<boolean> - true if the vote is valid, false otherwise
   */
  static async isVoteValid({
    votingChallengeSlug,
    votingChallengeId,
    itemId,
  }: {
    votingChallengeSlug?: string
    votingChallengeId?: number
    itemId: number
  }) {
    const votingChallengePromise = VotingChallengeService.getChallengeSnippet({
      slug: votingChallengeSlug,
      id: votingChallengeId,
    })

    const voteableUploadPromise = this.getVoteableUploadById({ itemId })

    const [votingChallenge, voteableUpload] = await Promise.all([
      votingChallengePromise,
      voteableUploadPromise,
    ])

    if (
      !votingChallenge ||
      !votingChallenge.voteable_from ||
      !votingChallenge.voteable_till ||
      !voteableUpload ||
      !voteableUpload.voting_challenge
    ) {
      return false
    }

    if (votingChallenge.id !== voteableUpload.voting_challenge) {
      return false
    }

    const now = new Date()
    const voteableFrom = new Date(votingChallenge.voteable_from)
    const voteableTill = new Date(votingChallenge.voteable_till)
    return now >= voteableFrom && now <= voteableTill
  }

  public static async getVoteableUploadForWidget({
    itemId,
    votingChallengeSlug,
    votingChallengeId,
  }: {
    itemId: number
    votingChallengeSlug?: string
    votingChallengeId?: number
  }) {
    const voteableUploadPromise = this.getVoteableUploadById({
      itemId,
    })

    const voteableUploadVotesPromise = votingClient.request(
      aggregate('vote', {
        query: {
          filter: { voteable_upload: itemId },
        },
        aggregate: { count: '*' },
      }),
    )

    const votingChallengePromise = VotingChallengeService.getChallengeSnippet({
      slug: votingChallengeSlug,
      id: votingChallengeId,
    })

    const [voteableUpload, votingChallenge, voteableUploadVotes] = await Promise.all([
      voteableUploadPromise,
      votingChallengePromise,
      voteableUploadVotesPromise,
    ])

    if (!voteableUpload) {
      throw new Error('Something went wrong fetching the voteable upload')
    }

    if (!votingChallenge) {
      throw new Error('Something went wrong fetching the voting challenge')
    }

    if (!voteableUploadVotes) {
      logger.error('Something went wrong fetching the voteable upload votes')
    }

    const upload = {
      ...voteableUpload,
      voting_challenge: votingChallenge,
    }

    // This ugly type narrowing is necessary because the relation field is not typed correctly
    // The type of voteableUpload.voting_challenge is either a number or a VotingChallenge object.
    // The Directus SDK doesn't resolve this correctly
    const [voteableFrom, voteableTill, votingText, resultDate] =
      upload.voting_challenge !== undefined &&
      upload.voting_challenge !== null &&
      typeof upload.voting_challenge !== 'number'
        ? [
            upload.voting_challenge.voteable_from,
            upload.voting_challenge.voteable_till,
            upload.voting_challenge.voting_text,
            upload.voting_challenge.result_date,
          ]
        : [null, null, null, null]

    if (upload.image) {
      // override image id with directus embed url, as we don't have use for the id alone
      upload.image.id = `${process.env.CMS_API_URL}/assets/${upload.image.id}`
    }

    return {
      title: upload.title,
      image: upload.image,
      voteableFrom,
      voteableTill,
      votingText,
      resultDate,
      votesCount: voteableUploadVotes?.[0].count,
    }
  }

  public static async updateFile({ requestData }: { requestData: Record<string, string> }) {
    try {
      const data = updateFileDataValidator.parse(requestData)
      const storage = storageUtil.getStorageInstance()

      const [file] = await storage.bucket(directusMediaBucket!).file(data.fileName).get()
      const { host, pathname } = file.cloudStorageURI
      const publicUrl = `https://storage.cloud.google.com/${host}${pathname}`

      await uploadClient.request(
        updateItem('voteable_upload', data.voteableUploadId, {
          video_link: publicUrl,
        }),
      )

      return { success: true }
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation Error: ${error.message}`)
      } else {
        const message = error instanceof Error ? error.message : 'Something went wrong'
        throw new Error(`Error: ${message}`)
      }
    }
  }

  private static async getSignedUrlForFileUpload({
    data,
  }: {
    data: z.infer<typeof fullVoteableUploadDataValidator>
  }) {
    const storage = storageUtil.getStorageInstance()

    const fileNameSplit = data.originalFileName.split('.')
    const fileEnding = fileNameSplit[fileNameSplit.length - 1]

    // These options will allow temporary uploading of the file with the Content-Type the user provided
    const options: GetSignedUrlConfig = {
      version: 'v4',
      action: 'write',
      expires: Date.now() + 60 * 60 * 24 * 1000, // 24 hours
      contentType: data.fileContentType,
    }

    const fileName = `voting-challenges/${data.challengeId}/${
      data.title
    }-${Date.now()}.${fileEnding}`

    // Get a v4 signed URL for uploading file
    const [url] = await storage.bucket(directusMediaBucket!).file(fileName).getSignedUrl(options)

    return { url, fileName }
  }

  private static async getVoteableUploadById({
    itemId,
    fields = ['id', 'title', 'state', 'voting_challenge', 'image'],
  }: {
    itemId: number
    fields?: readonly (keyof VoteableUpload)[]
  }) {
    const cache = await getVotingCache()

    const data = await cache.getWithFunction({
      key: `voteable_upload-${itemId}-${fields.join('-')}`,
      storeFunction: async () =>
        votingClient.request(
          readItem('voteable_upload', itemId, {
            fields: fields.map((field) =>
              field === 'image'
                ? ({
                    image: ['copyright', 'focal_point_x', 'focal_point_y', 'width', 'height', 'id'],
                  } as const)
                : field,
            ),
          }),
        ),
      ttl: VOTEABLE_UPLOAD_TTL,
    })

    return data
  }
}
