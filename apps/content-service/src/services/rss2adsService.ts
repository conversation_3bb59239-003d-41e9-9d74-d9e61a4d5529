import { CONSTANTS } from '@regionalmedienaustria/cache-util/lib/src/cache/constants.js'
import { readItems } from '@rma-mono/directus-client'
import Parser from 'rss-parser'

import { getCache } from '@/cache/cache.js'
import { publicClient } from '@/utils/directus/client.js'

const RSS2ADS_TTL = CONSTANTS.ONE_DAY

export class RSS2AdsService {
  public static async getRSS2Ads(rss2adsId: number) {
    const cache = await getCache()

    const data = await cache.getWithFunction({
      key: `rss2ads-${rss2adsId}`,
      storeFunction: async () =>
        publicClient.request(
          readItems('rss_2_ads', {
            fields: [
              'id',
              'backgroundcolor',
              { company_logo: ['id', 'title', 'description', 'width', 'height'] },
              { defaultbild: ['id', 'title', 'description', 'width', 'height'] },
              'name',
              'rss_feed_url',
              'source',
            ],
            filter: { id: { _eq: rss2adsId } },
          }),
        ),
      ttl: RSS2ADS_TTL,
    })

    const rss2ads = data.length === 0 ? null : data[0]

    if (!rss2ads) {
      throw new Error('RSS2Ads not found')
    }

    const feedUrl = rss2ads.rss_feed_url

    if (!feedUrl) {
      throw new Error('Feed URL not found')
    }

    const feedItems = await this.fetchRSSFeedData(feedUrl)

    return { rss2ads, feedItems }
  }

  private static async fetchRSSFeedData(url: string) {
    const parser = new Parser()
    const feed = await parser.parseURL(url)

    return feed.items
  }
}
