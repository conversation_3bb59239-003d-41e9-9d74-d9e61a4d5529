import { logger } from '@regionalmedienaustria/microservice-utils'
import { DirectusRegions } from '@rma-mono/rma-regions'
import { buffer } from '@turf/buffer'
import { points } from '@turf/helpers'
import { pointsWithinPolygon } from '@turf/points-within-polygon'

import { getPharmacyCache } from '@/cache/pharmacyCache.js'
import { hasCategory, type PharmacyCategory } from '@/services/pharmacy/pharmacyStatus.js'
import { publicClient } from '@/utils/directus/client.js'
import { getCurrentWeekBoundaries } from '@/utils/utils.js'

import { PharmacyApiResponse, type PharmacyApiResponseType } from './external.js'
import { excludedDienstleistungenTests, excludedServices, type PharmacyType } from './mapping.js'
import { getServiceName, getDienstleistungenTestsName } from './mapping.js'

import type { PharmacyDienstleistungenTests, PharmacyServices } from './external.js'
import type { FeatureCollection, GeoJsonProperties, MultiPolygon } from 'geojson'

export class PharmacyService {
  private static async fetchPharmacies(): Promise<PharmacyType[]> {
    const username = process.env.PHARMACY_API_USERNAME
    const password = process.env.PHARMACY_API_PASSWORD
    const apiUrl = process.env.PHARMACY_API_URL

    if (!username || !password || !apiUrl) {
      throw new Error('Pharmacy API credentials or URL not set')
    }

    try {
      const response = await fetch(apiUrl, {
        headers: {
          Authorization: 'Basic ' + Buffer.from(username + ':' + password).toString('base64'),
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = (await response.json()) as PharmacyApiResponseType

      return await this.cleanPharmacies(data)
    } catch (error) {
      logger.error('Error fetching pharmacies from API:', error)
      throw new Error('Failed to fetch pharmacies from API')
    }
  }

  private static async cleanPharmacies(response: any): Promise<PharmacyType[]> {
    const parsedData = PharmacyApiResponse.parse(response)

    const cleanedPharmacies = parsedData.apotheken.map((pharmacy) => {
      const services = Object.entries(pharmacy.services)
        .filter(([, value]) => value !== 0)
        .map(([key]) => key as PharmacyServices)

      const dienstleistungenTests = Object.entries(pharmacy.dienstleistungen_tests)
        .filter(([, value]) => value !== 0 && value !== '')
        .map(([key]) => key as PharmacyDienstleistungenTests)

      const { monday, sunday } = getCurrentWeekBoundaries()

      const tage = Object.entries(pharmacy.tage).filter(([key, value]) => {
        const isWithinWeek = key >= monday && key <= sunday
        const hasOpeningHours = value.length > 0
        return isWithinWeek && hasOpeningHours
      })

      return {
        stammdaten: pharmacy.stammdaten,
        services: services
          .filter((service) => !excludedServices.includes(service))
          .map(getServiceName),
        dienstleistungen_tests: dienstleistungenTests
          .filter((test) => !excludedDienstleistungenTests.includes(test))
          .map(getDienstleistungenTestsName),
        tage: Object.fromEntries(tage),
      } satisfies PharmacyType
    })

    return cleanedPharmacies.sort((a, b) => a.stammdaten.name.localeCompare(b.stammdaten.name))
  }

  private static async getPharmaciesForRegion({
    pharmacies,
    regionSlug,
  }: {
    pharmacies: PharmacyType[]
    regionSlug: string
  }) {
    const { bounds, isVienna } = await this.getBoundsForRegion(regionSlug)
    const bufferedBounds = buffer(bounds, isVienna ? 1 : 5, { units: 'kilometers' })

    const pharmaciesWithin = pharmacies.filter((pharmacy) => {
      const coord = points([
        [parseFloat(pharmacy.stammdaten.longitude), parseFloat(pharmacy.stammdaten.latitude)],
      ])
      const result = pointsWithinPolygon(coord, bufferedBounds ?? bounds)
      return result.features.length > 0
    })

    return {
      pharmacies: pharmaciesWithin,
      bounds: bufferedBounds,
    }
  }

  private static async getPharmaciesFromCache(): Promise<PharmacyType[]> {
    const cache = await getPharmacyCache()

    const pharmacies = await cache.getWithFunction({
      key: 'pharmacies',
      storeFunction: async () => this.fetchPharmacies(),
      ttl: 60 * 60 * 24, // 1 day,
      staleWhileRevalidate: true,
    })

    return pharmacies
  }

  public static async getPharmacyInfo(filters: {
    region: string
    category?: PharmacyCategory[]
  }): Promise<{ pharmacies: PharmacyType[]; bounds: any }> {
    const pharmacies = await this.getPharmaciesFromCache()

    const filteredPharmacies = filters.category
      ? pharmacies.filter((pharmacy) => hasCategory({ pharmacy, category: filters.category }))
      : pharmacies

    return this.getPharmaciesForRegion({
      pharmacies: filteredPharmacies,
      regionSlug: filters.region,
    })
  }

  private static async getBoundsForRegion(regionSlug: string): Promise<{
    bounds: FeatureCollection<MultiPolygon, GeoJsonProperties>
    isVienna: boolean
  }> {
    const cache = await getPharmacyCache()
    const bounds = await cache.getWithFunction({
      key: `bounds-${regionSlug}`,
      storeFunction: async () => {
        const directusRegions = new DirectusRegions(publicClient)

        const boundsResponse = await directusRegions.getRegionBounds({
          regionSlug,
          includeFullBoundsIfNoChildrenFound: true,
        })

        return boundsResponse
      },
      ttl: 60 * 60 * 24, // 24 hours
      staleWhileRevalidate: true,
    })

    return bounds
  }
}
