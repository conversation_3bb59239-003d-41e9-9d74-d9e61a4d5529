import { logger } from '@regionalmedienaustria/microservice-utils'
import { z } from 'zod'

import type {
  ReadableServiceName,
  ReadableDienstleistungenTestsName,
  PharmacyType,
} from './mapping.js'

export const categoryValidator = z.enum([
  // Accessibility & Basic Services
  'barrierefrei',
  'bankomat',
  'postpartner',
  'sauerstoff',

  // Medical Services
  'hausbetreuung',
  'sterbeverfügung',
  'defibrillator',
  'medikationsanalyse',
  'impfservice',
  'e-impfpass',
  'e-rezept',
  'neuverblisterung',

  // Health Tests
  'bluttests',
  'körpermessung',
  'lungenfunktion',
  'venenfunktion',
  'vitamin-d',

  // Online Services
  'versandapotheke',
])

export const categoryValidatorArray = z.array(categoryValidator)

export type PharmacyCategory = z.infer<typeof categoryValidator>

/**
 * Maps user-friendly category identifiers to their corresponding
 * cleaned service names in the pharmacy data.
 * These mappings are used to filter pharmacies by specific services.
 */
export const categoryToServicesMap: Record<
  PharmacyCategory,
  Array<ReadableServiceName | ReadableDienstleistungenTestsName>
> = {
  // Accessibility & Basic Services
  barrierefrei: ['Barrierefreiheit'],
  bankomat: ['Bankomat vorhanden'],
  postpartner: ['Postpartner'],
  sauerstoff: ['Sauerstofftankstelle'],

  // Medical Services
  hausbetreuung: ['HAP Belieferung', 'Heimversorgung'],
  sterbeverfügung: ['Sterbeverfügung'],
  defibrillator: ['Defibrillator'],
  medikationsanalyse: ['Medikationsanalyse'],
  impfservice: ['Impfberatung'],
  'e-impfpass': ['E-Impfpass Nachtragung'],
  'e-rezept': ['Teilnahme E-Prescription', 'Teilnahme E-Privatrezept'],
  neuverblisterung: ['Neuverblisterung'],

  // Health Tests
  bluttests: ['Blutdruckmessung', 'Blutzuckermessung', 'Messen von Cholesterin Triglyceride'],
  körpermessung: ['Körperfettmessung', 'Pharmakogenetik', 'Testung Stoffwechsel'],
  lungenfunktion: ['Messen von Lungenfunktion'],
  venenfunktion: ['Messen von Venenfunktion'],
  'vitamin-d': ['Messen von Vitamin D'],

  // Online Services
  versandapotheke: ['Versandapotheke'],
}

export const hasCategory = ({
  pharmacy,
  category,
}: {
  pharmacy: PharmacyType
  category?: PharmacyCategory[]
}): boolean => {
  if (category) {
    const matchesService = category.every((singleCategory) => {
      if (!(singleCategory in categoryToServicesMap)) {
        logger.warn(`Category '${singleCategory}' not found in categoryToServicesMap.`)
        return false
      }
      const services = categoryToServicesMap[singleCategory]

      const hasService = services.some(
        (service) =>
          pharmacy.services.includes(service) || pharmacy.dienstleistungen_tests.includes(service),
      )

      return hasService
    })

    if (!matchesService) {
      return false
    }
  }

  return true
}
