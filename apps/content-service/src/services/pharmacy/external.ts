import { z } from 'zod'

/**
 * Handles number or string values:
 * - Numbers pass through unchanged
 * - Empty strings convert to 0
 * - Non-empty strings return undefined
 */
const numberOrStringEmptyTo0 = z.union([
  z.number(),
  z.string().transform((val) => {
    if (val === '') {
      return 0
    }

    return undefined
  }),
])

const servicesSchema = z.object({
  barrierefreiheit: numberOrStringEmptyTo0.optional(),
  hap_belieferung: numberOrStringEmptyTo0.optional(),
  heimversorgung: numberOrStringEmptyTo0.optional(),
  sterbeverfuegung: numberOrStringEmptyTo0.optional(),
  teilnahme_1450: numberOrStringEmptyTo0.optional(),
  defibrillator: numberOrStringEmptyTo0.optional(),
  p63_amg: numberOrStringEmptyTo0.optional(),
  p63_amg_grosshandel: numberOrStringEmptyTo0.optional(),
  zusaetzlicher_gewerbebetrieb_drogerie: numberOrStringEmptyTo0.optional(),
  gisa_nummer: numberOrStringEmptyTo0.optional(),
  bereitschaftsdienstgruppe_wien: numberOrStringEmptyTo0.optional(),
  andere_gewerbebetriebe: numberOrStringEmptyTo0.optional(),
})

const dienstleistungenTestsSchema = z.object({
  koerperfettmessung: numberOrStringEmptyTo0.optional(),
  blutdruckmessung: numberOrStringEmptyTo0.optional(),
  blutzuckermessung: numberOrStringEmptyTo0.optional(),
  messen_von_cholesterin_triglyceride: numberOrStringEmptyTo0.optional(),
  messen_von_lungenfunktion: numberOrStringEmptyTo0.optional(),
  messen_von_venenfunktion: numberOrStringEmptyTo0.optional(),
  messen_von_vitamin_d: numberOrStringEmptyTo0.optional(),
  pharmakogenetik: numberOrStringEmptyTo0.optional(),
  testung_stoffwechsel: numberOrStringEmptyTo0.optional(),
  covid19_test_antigen: numberOrStringEmptyTo0.optional(),
  covid19_test_pcr: numberOrStringEmptyTo0.optional(),
  covid19_test_pcr_poct: numberOrStringEmptyTo0.optional(),
  testung_influenza: numberOrStringEmptyTo0.optional(),
  testung_rsv: numberOrStringEmptyTo0.optional(),
  bankomat_vorhanden: numberOrStringEmptyTo0.optional(),
  sauerstofftankstelle: numberOrStringEmptyTo0.optional(),
  medikationsanalyse: numberOrStringEmptyTo0.optional(),
  teilnahme_eprescription: numberOrStringEmptyTo0.optional(),
  postpartner: numberOrStringEmptyTo0.optional(),
  impfberatung: numberOrStringEmptyTo0.optional(),
  eimpfpass_nachtragung: numberOrStringEmptyTo0.optional(),
  neuverblisterung: numberOrStringEmptyTo0.optional(),
  versandapotheke: numberOrStringEmptyTo0.optional(),
  teilnahme_eprivatrezept: numberOrStringEmptyTo0.optional(),
  shop_domain: z.string().optional(),
})

export const PharmacyApiResponse = z.object({
  apotheken: z.array(
    z.object({
      stammdaten: z.object({
        name: z.string(),
        typ: z.string(),
        strasse: z.string(),
        plz: z.number(),
        ort: z.string(),
        tel: z.string(),
        email: z.string(),
        url: z.string().optional(),
        latitude: z.string(),
        longitude: z.string(),
      }),
      services: servicesSchema,
      dienstleistungen_tests: dienstleistungenTestsSchema,
      tage: z.record(
        z.array(
          z.object({
            zeit: z.string(),
            typ: z.enum(['O', 'B', 'R']),
          }),
        ),
      ),
    }),
  ),
})

export type PharmacyApiResponseType = z.infer<typeof PharmacyApiResponse>
export type PharmacyApiType = PharmacyApiResponseType['apotheken'][number]
export type PharmacyServices = keyof typeof servicesSchema.shape
export type PharmacyDienstleistungenTests = keyof typeof dienstleistungenTestsSchema.shape
