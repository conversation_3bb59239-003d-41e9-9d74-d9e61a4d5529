import type {
  PharmacyServices,
  PharmacyDienstleistungenTests,
  PharmacyApiType,
} from './external.js'

export const excludedServices = ['sterbeverfuegung', 'teilnahme_1450']

const serviceMap = {
  barrierefreiheit: 'Barrierefreiheit',
  hap_belieferung: 'HAP Belieferung',
  heimversorgung: 'Heimversorgung',
  teilnahme_1450: 'Teilnahme 1450',
  defibrillator: 'Defibrillator',
  p63_amg: 'P63 AMG',
  p63_amg_grosshandel: 'P63 AMG Grosshandel',
  zusaetzlicher_gewerbebetrieb_drogerie: 'Zusätzlicher Gewerbebetrieb Drogerie',
  gisa_nummer: 'GISA Nummer',
  bereitschaftsdienstgruppe_wien: 'Bereitschaftsdienstgruppe Wien',
  andere_gewerbebetriebe: 'Andere Gewerbebetriebe',
} as const

export const excludedDienstleistungenTests: string[] = [
  'covid19_test_antigen',
  'covid19_test_pcr',
  'covid19_test_pcr_poct',
  'testung_influenza',
]

const dienstleistungenTestsMap = {
  koerperfettmessung: 'Körperfettmessung',
  blutdruckmessung: 'Blutdruckmessung',
  blutzuckermessung: 'Blutzuckermessung',
  messen_von_cholesterin_triglyceride: 'Messen von Cholesterin Triglyceride',
  messen_von_lungenfunktion: 'Messen von Lungenfunktion',
  messen_von_venenfunktion: 'Messen von Venenfunktion',
  messen_von_vitamin_d: 'Messen von Vitamin D',
  pharmakogenetik: 'Pharmakogenetik',
  testung_stoffwechsel: 'Testung Stoffwechsel',
  covid19_test_antigen: 'COVID-19 Test Antigen',
  covid19_test_pcr: 'COVID-19 Test PCR',
  covid19_test_pcr_poct: 'COVID-19 Test PCR POCT',
  testung_influenza: 'Testung Influenza',
  testung_rsv: 'Testung RSV',
  bankomat_vorhanden: 'Bankomat vorhanden',
  sauerstofftankstelle: 'Sauerstofftankstelle',
  medikationsanalyse: 'Medikationsanalyse',
  teilnahme_eprescription: 'Teilnahme E-Prescription',
  postpartner: 'Postpartner',
  impfberatung: 'Impfberatung',
  eimpfpass_nachtragung: 'E-Impfpass Nachtragung',
  neuverblisterung: 'Neuverblisterung',
  versandapotheke: 'Versandapotheke',
  teilnahme_eprivatrezept: 'Teilnahme E-Privatrezept',
  shop_domain: 'Shop Domain',
} as const

// Derive types from the mapping constants
export type ReadableServiceName = (typeof serviceMap)[keyof typeof serviceMap] | (string & {})
export type ReadableDienstleistungenTestsName =
  | (typeof dienstleistungenTestsMap)[keyof typeof dienstleistungenTestsMap]
  | (string & {})

export type PharmacyType = {
  stammdaten: PharmacyApiType['stammdaten']
  services: ReadableServiceName[]
  dienstleistungen_tests: ReadableDienstleistungenTestsName[]
  tage: PharmacyApiType['tage']
}

const formatUnknownName = (service: string) =>
  service
    .replace(/_/g, ' ')
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .replace(/([A-Z])([A-Z][a-z])/g, '$1 $2')
    .replace(/ue/g, 'ü')
    .replace(/oe/g, 'ö')
    .replace(/ae/g, 'ä')

const nameInMapOrFormatUnknown = <T extends string>(
  map: Record<string, string>,
  name: T,
): string => {
  const nameInMap = map[name as keyof typeof map]
  if (nameInMap) {
    return nameInMap
  }
  return formatUnknownName(name)
}

export const getServiceName = (service: PharmacyServices): ReadableServiceName =>
  nameInMapOrFormatUnknown(serviceMap, service)

export const getDienstleistungenTestsName = (
  service: PharmacyDienstleistungenTests,
): ReadableDienstleistungenTestsName => nameInMapOrFormatUnknown(dienstleistungenTestsMap, service)
