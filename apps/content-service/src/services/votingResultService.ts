import { Cache } from '@regionalmedienaustria/cache-util'
import { logger } from '@regionalmedienaustria/microservice-utils'
import { aggregate, readItems } from '@rma-mono/directus-client'

import { getVotingCache } from '@/cache/votingCache.js'
import { votingClient } from '@/utils/directus/client.js'

import { VotingChallengeService } from './votingChallengeService.js'

import type { VotingChallenge } from '@rma-mono/directus-client'

export class VotingResultService {
  public static async getVotingResults({
    votingChallengeSlug,
    votingChallengeId,
  }: {
    votingChallengeSlug?: string
    votingChallengeId?: number
  }) {
    const cache = await getVotingCache()
    const votingChallenge = (await VotingChallengeService.getChallenge({
      slug: votingChallengeSlug,
      id: votingChallengeId,
    })) as VotingChallenge

    if (!votingChallenge) {
      return null
    }

    const isVotingActive = votingChallenge.voteable_till
      ? new Date() < new Date(votingChallenge.voteable_till)
      : false

    const votingResult = await cache.getWithFunction({
      key: `voting-results-${votingChallenge.id}`,
      storeFunction: async () => this.calculateResults(votingChallenge),
      ttl: isVotingActive ? Cache.CONSTANTS.ONE_MINUTE * 15 : Cache.CONSTANTS.ONE_DAY, // 15 minutes or 24 hours
    })

    return {
      results: votingResult,
      votingChallengeTitle: votingChallenge.title,
      voteableTill: votingChallenge.voteable_till,
      isVotingActive,
    }
  }

  private static async calculateResults(votingChallenge: VotingChallenge) {
    try {
      if (!votingChallenge) {
        throw new Error('Voting challenge not found')
      }

      const voteableUploadPromise = votingClient.request(
        readItems('voteable_upload', {
          filter: { voting_challenge: votingChallenge.id },
          fields: ['id', 'title'],
          limit: -1,
        }),
      )
      const voteCountPromise = votingClient.request(
        aggregate('vote', {
          groupBy: ['voteable_upload'],
          aggregate: { count: '*' },
          query: {
            filter: { voteable_upload: { voting_challenge: votingChallenge.id } },
            limit: -1,
          },
        }),
      )

      const [voteableUploads, voteCounts] = await Promise.all([
        voteableUploadPromise,
        voteCountPromise,
      ])

      const results = voteableUploads.map((upload) => {
        const voteCount = voteCounts.find((vc) => vc.voteable_upload === upload.id)?.count || 0
        return {
          title: upload.title,
          votes: Number(voteCount),
          percentage: 0, // We'll calculate this after
        }
      })

      const totalVotes = results.reduce((sum, result) => sum + result.votes, 0)
      results.forEach((result) => {
        result.percentage = totalVotes > 0 ? (result.votes / totalVotes) * 100 : 0
      })

      return results.sort((a, b) => b.votes - a.votes)
    } catch (error) {
      logger.error('Error fetching voting results:', error)
      throw error
    }
  }
}
