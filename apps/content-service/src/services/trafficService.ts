import { CONSTANTS } from '@regionalmedienaustria/cache-util/lib/src/cache/constants.js'
import { logger } from '@regionalmedienaustria/microservice-utils'
import axios from 'axios'

import { getTrafficCache } from '@/cache/trafficCache.js'
import {
  getDataTrafficIds,
  getHighway,
  getCameraTitle,
  getStateCode,
  getStateData,
} from '@/data/traffic/index.js'

import type { Cache } from '@regionalmedienaustria/cache-util'
import type { AxiosRequestConfig } from 'axios'

interface TrafficServiceResult {
  webcameras: WebcamsResult
  html?: string
  routes: {
    highwayRoute?: Route
    federalRoute?: Route
  }
}

interface Route {
  title: string
  state: string
  nodes: Node[]
}

interface Node {
  name: string
  state: string
  cameras: RouteCam[]
}

interface RouteCam {
  url: string
  title: string
  direction: string
  location: string
}

interface WebcamData {
  name: string
  webcamUrl: string
  strassenNummer: string
  kilometrierung: string
  standort: string
  wgs84_X: number
  wgs84_Y: number
  bundesland: string
  tunnel: boolean
  rastplatz: boolean
  quelle: string | null
  morisID: number
  wcsid: string
}

interface WebcamsResult {
  highwayCams: WebcamData[]
  federalCams: WebcamData[]
}

const CACHE_TTL = CONSTANTS.ONE_HOUR

const ASFINAG_URL =
  'https://webcams2.asfinag.at/webcamviewer/ExportUserCamlistServlet?option=singlescreen&user=regionalmedienat&format=json'

export default class TrafficService {
  private static cache: Cache.RmaCache

  public static async getWebcamData(state: string): Promise<TrafficServiceResult> {
    const result: TrafficServiceResult = {
      webcameras: {
        highwayCams: [],
        federalCams: [],
      },
      routes: {
        highwayRoute: undefined,
        federalRoute: undefined,
      },
    }

    const webcameraData = await TrafficService.getCameraDataFromCache(state)

    if (webcameraData) {
      result.webcameras = webcameraData
    }

    return result
  }

  public static async getWebcamHtml(state: string): Promise<TrafficServiceResult> {
    const result: TrafficServiceResult = {
      webcameras: {
        highwayCams: [],
        federalCams: [],
      },
      html: '',
      routes: {
        highwayRoute: undefined,
        federalRoute: undefined,
      },
    }

    const webcameraData = await TrafficService.getCameraDataFromCache(state)

    if (webcameraData) {
      result.webcameras = webcameraData
      result.html = await TrafficService.getWebcamHtmlFromCache([
        ...(result.webcameras
          ? [...result.webcameras.highwayCams, ...result.webcameras.federalCams]
          : []),
      ])
    }

    return result
  }

  private static async getCameraDataFromCache(state: string): Promise<WebcamsResult | undefined> {
    this.cache = await getTrafficCache()
    const data = await this.cache.getWithFunction({
      key: `traffic-webcamdata-${state}`,
      storeFunction: TrafficService.fetchFromAsfinag,
      params: [state],
      ttl: CACHE_TTL,
    })

    return data
  }

  private static parseCameraData(cameraData: WebcamData[]) {
    return cameraData.map((camera) => {
      const picked = (({
        name,
        webcamUrl,
        strassenNummer,
        kilometrierung,
        standort,
        wgs84_X,
        wgs84_Y,
        bundesland,
        tunnel,
        morisID,
        rastplatz,
      }) => ({
        name,
        webcamUrl,
        strassenNummer,
        kilometrierung,
        standort,
        wgs84_X,
        wgs84_Y,
        bundesland,
        tunnel,
        morisID,
        rastplatz,
      }))(camera)
      return picked
    })
  }

  private static async fetchFromAsfinag(state: string) {
    const reqOptions: AxiosRequestConfig = {
      url: ASFINAG_URL,
      method: 'GET',
      headers: {
        Accept: '*/*',
        'Content-Type': 'application/json',
      },
    }
    try {
      const response = await axios.request(reqOptions)
      const cameraData: WebcamData[] = JSON.parse(JSON.stringify(response.data))

      const stateData = getStateData(state)

      const cameras: any = TrafficService.parseCameraData(
        cameraData.filter(
          (camera) =>
            (camera.bundesland === stateData?.code ||
              stateData?.aditionalCodes?.indexOf(camera.bundesland) !== -1) &&
            camera.quelle === null,
        ),
      )

      const federalCams: any[] = []
      if (stateData) {
        for (let i = 0; i < stateData.source.length; i++) {
          const source = stateData.source[i]
          const cams = TrafficService.parseCameraData(
            cameraData.filter((cam) => cam.quelle === source),
          )
          federalCams.push(...cams)
        }
      }

      return { highwayCams: cameras, federalCams: federalCams }
    } catch (error) {
      logger.error(error)
      throw error
    }
  }

  private static async getWebcamHtmlFromCache(cameraData: WebcamData[]): Promise<string> {
    this.cache = await getTrafficCache()
    const data = await this.cache.getWithFunction({
      key: 'traffic-html',
      storeFunction: TrafficService.getHtml,
      params: [cameraData],
      ttl: 20,
    })

    return data
  }

  private static async getHtml(cameraData: WebcamData[]): Promise<string> {
    const state = getStateData(cameraData[0].bundesland)
    const bl = state?.name
    const html = `<h2>Übersicht der aktuellen Straßenkameras in ${bl}</h2><div id="traf-map"></div>`
    return html
  }

  public static async getTrafficList(route: string, state: string): Promise<TrafficServiceResult> {
    const result: TrafficServiceResult = {
      webcameras: {
        highwayCams: [],
        federalCams: [],
      },
      routes: {},
    }

    const allCams = await TrafficService.getCameraDataFromCache(state)

    if (!allCams) {
      return result
    }

    const ids = getDataTrafficIds(route)

    const highwayCams = allCams.highwayCams.filter(
      (camera) =>
        ids.includes('' + camera.morisID) ||
        ids.includes('' + camera.wcsid) ||
        ids.includes(camera.name),
    )
    const federalCams = allCams.federalCams.filter(
      (camera) => ids.includes('' + camera.morisID) || ids.includes(camera.name),
    )

    if (allCams.highwayCams[0].bundesland === 'O') {
      result.webcameras.highwayCams = highwayCams.reverse()
      result.webcameras.federalCams = federalCams.reverse()
    } else {
      result.webcameras.highwayCams = highwayCams
      result.webcameras.federalCams = federalCams
    }

    result.routes.highwayRoute = await TrafficService.getRouteObject(
      highwayCams.slice().sort((a, b) => ids.indexOf('' + a.morisID) - ids.indexOf('' + b.morisID)),
      route,
    )
    result.routes.federalRoute = await TrafficService.getRouteObject(
      federalCams.slice().sort((a, b) => ids.indexOf('' + a.name) - ids.indexOf('' + b.name)),
      route,
      true,
    )

    return result
  }

  private static async getRouteObject(
    webcams: WebcamData[],
    route: string,
    federal: boolean = false,
  ): Promise<Route> {
    const routeObj: Route = {
      title: '',
      state: '',
      nodes: [],
    }

    if (webcams?.length) {
      const highwayTitle = getHighway(webcams[0].strassenNummer)
      const arr = route.split('-')
      for (let i = 0; i < arr.length; i++) {
        arr[i] = arr[i].charAt(0).toUpperCase() + arr[i].slice(1)
      }
      const routeName = arr.join(' & ')
      routeObj.title = `${
        !federal ? highwayTitle : 'Bundesstraßen & Landesstraßen'
      } zwischen ${routeName}`
      routeObj.state = getStateCode(webcams[0].bundesland)

      let positionName = ''
      let node = <Node>{}
      webcams.forEach((cam) => {
        const infos = cam.standort.split(',')
        let standort: any = infos[1].trim()
        if (standort.indexOf('zwischen ') !== -1) {
          standort = standort.replaceAll(' Anschlussstelle', '')
        } else if (standort.indexOf('bei Anschlussstelle') !== -1) {
          standort = standort.replace('bei Anschlussstelle', '').trim()
        } else if (standort.indexOf('bei ') !== -1) {
          standort = standort.replace('bei ', '')
        }
        let blickrichtung = infos[2].split('-')[0].trim()
        if (blickrichtung.indexOf('Knoten ') !== -1) {
          blickrichtung = blickrichtung.replace('Knoten ', '').trim()
        }
        if (positionName !== standort) {
          if (positionName !== '') {
            routeObj.nodes.push(node)
          }
          positionName = standort
          node = <Node>{}
          node.name = standort
          node.cameras = []
          node.state = cam.bundesland
        }
        node.cameras.push({
          url: cam.webcamUrl,
          title: getCameraTitle(route, cam.morisID),
          direction: blickrichtung,
          location: cam.standort,
        })
      })
      routeObj.nodes.push(node)
    }

    return routeObj
  }
}
