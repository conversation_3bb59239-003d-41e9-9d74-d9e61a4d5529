import { CONSTANTS } from '@regionalmedienaustria/cache-util/lib/src/cache/constants.js'
import { readItems } from '@rma-mono/directus-client'
import { bbox } from '@turf/bbox'
import { featureCollection, multiPolygon } from '@turf/helpers'

import { getAirQualityCache } from '@/cache/airQualityCache.js'
import { publicClient } from '@/utils/directus/client.js'

import type { Feature, FeatureCollection, GeoJsonProperties, MultiPolygon } from 'geojson'

interface AirQualityInfo {
  region: string
  data: AQStationData[]
}

interface AQRegionData {
  lat: number
  lon: number
  uid: number
  aqi: string
  station: {
    name: string
    time: string
  }
}

interface AQStationData {
  success?: boolean
  aqi: number
  idx: number
  attributions: [
    {
      url: string
      name: string
      logo?: string
    },
  ]
  city: {
    geo: number[]
    name: string
    url: string
    location: string
  }
  dominentpol: string
  iaqi: {
    h: { v: number }
    no2: { v: number }
    p: { v: number }
    pm10: { v: number }
    t: { v: number }
    w: { v: number }
    wg: { v: number }
  }
  time: {
    s: string
    tz: string
    v: number
    iso: string
  }
  forecast?: { daily: {} }
  debug?: { sync: string }
}

const API_TOKEN = process.env.AIR_QUALITY_API_TOKEN
const API_URL = process.env.AIR_QUALITY_API_URL

export class AirQualityService {
  /**
   * Fetches air quality information for a given region slug.
   * Retrieves data from cache if available, otherwise fetches from the external API
   * based on the geographical bounds derived from the region slug.
   *
   * @param region - The slug of the region (e.g., 'tirol', 'wien').
   * @returns A promise resolving to an AirQualityInfo object containing the region slug and an array of AQStationData.
   * @throws Error if fetching region bounds fails or if the external API request fails.
   */
  public static async getAirQualityInfo(region: string): Promise<AirQualityInfo> {
    // Retrieve geographical boundaries for the region slug.
    const bounds = await this.getRegionBounds(region)

    // Fetch air quality info using caching mechanism.
    const airQualityInfo = await this.getAirQualityFromCache(region, bounds)

    return airQualityInfo
  }

  /**
   * Retrieves air quality data from cache or fetches it if not present/stale.
   *
   * @param region - The region slug used as part of the cache key.
   * @param bounds - The geographical bounds used to fetch data if cache miss.
   * @returns A promise resolving to an object containing the region and fetched air quality data.
   */
  private static async getAirQualityFromCache(
    region: string,
    bounds: FeatureCollection<MultiPolygon, GeoJsonProperties>,
  ) {
    const cache = await getAirQualityCache()

    const data = await cache.getWithFunction({
      key: `aq-${region}`,
      storeFunction: async () => this.fetchAirQualityInfo(bounds),
      ttl: CONSTANTS.ONE_HOUR * 6,
    })

    return { region, data }
  }

  /**
   * Orchestrates the fetching of air quality data by first getting regional
   * station identifiers based on bounds, and then fetching detailed data for each station.
   *
   * @param bounds - The geographical bounds (FeatureCollection) for the target area.
   * @returns A promise resolving to an array of detailed air quality station data (AQStationData).
   */
  private static async fetchAirQualityInfo(
    bounds: FeatureCollection<MultiPolygon, GeoJsonProperties>,
  ) {
    // Fetch initial region data (list of station UIDs within bounds) from the external API.
    const regionData = await this.fetchRegionData(bounds)
    // Fetch detailed data for each station identified in the region data.
    const airQualityInfo = await this.getAirQuality(regionData)

    return airQualityInfo
  }

  /**
   * Fetches regional air quality station identifiers from the external API based on geographical bounds.
   *
   * @param bounds - A FeatureCollection representing the geographical area.
   * @returns A promise resolving to an array of AQRegionData, containing basic station info including UID.
   * @throws Error if API credentials are not set, bounding box calculation fails, or API request fails.
   */
  private static async fetchRegionData(
    bounds: FeatureCollection<MultiPolygon, GeoJsonProperties>,
  ): Promise<AQRegionData[]> {
    if (!API_TOKEN || !API_URL) {
      throw new Error('Air quality API token or URL not set')
    }

    let boxCoords: string
    try {
      if (!bounds.features || bounds.features.length === 0) {
        throw new Error('No valid features found in bounds')
      }

      const feature = bounds.features[0]
      if (
        !feature.geometry ||
        !feature.geometry.coordinates ||
        feature.geometry.coordinates.length === 0
      ) {
        throw new Error('Invalid geometry in first feature')
      }

      if (feature.geometry.coordinates[0]?.length > 0) {
        const polygon = multiPolygon(feature.geometry.coordinates)

        boxCoords = bbox(polygon).reverse().join(',')
      } else {
        throw new Error('No valid coordinates in first feature')
      }
    } catch (error) {
      throw new Error('Failed to calculate bounding box for region')
    }

    try {
      const boundsUrl = `${API_URL}/map/bounds?token=${API_TOKEN}&latlng=${boxCoords}`

      const response = await fetch(boundsUrl)

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`)
      }

      const result = await response.json()

      if (result.status === 'error') {
        throw new Error(`API Response Error: ${result.data}`)
      }

      const data = result.data as AQRegionData[]

      return data
    } catch (error) {
      throw new Error('Failed to fetch air quality region data from API')
    }
  }

  /**
   * Retrieves the geographical boundaries (as a FeatureCollection of MultiPolygons)
   * for a given region slug, primarily using data from the 'trees' collection in Directus.
   * Results are cached for one day.
   *
   * @param regionSlug - The slug of the region (e.g., 'tirol', 'wien').
   * @returns A promise resolving to a FeatureCollection containing MultiPolygon features.
   * Returns an empty FeatureCollection if no data is found or no valid geometries are processed.
   */
  private static async getRegionBounds(
    regionSlug: string,
  ): Promise<FeatureCollection<MultiPolygon>> {
    const boundaries: Feature<MultiPolygon>[] = []

    const cache = await getAirQualityCache()
    const boundsData = await cache.getWithFunction({
      key: `bounds-${regionSlug}`,
      storeFunction: async () =>
        publicClient.request(
          readItems('trees', {
            fields: [
              'slug',
              'title',
              'geo',
              {
                children: ['slug', 'title', 'geo'],
              },
              {
                parent_id: ['slug'],
              },
            ],
            filter: {
              slug: {
                _eq: regionSlug,
              },
            },
          }),
        ),
      ttl: CONSTANTS.ONE_DAY,
    })

    if (!boundsData || boundsData.length === 0) {
      return featureCollection([])
    }

    const data = boundsData[0]

    if (data.children?.length) {
      data.children.forEach((child) => {
        if (child.geo?.coordinates?.length && child.geo.type === 'MultiPolygon') {
          boundaries.push(
            multiPolygon(child.geo.coordinates, {
              name: child.title,
              slug: child.slug,
            }),
          )
        }
      })
    } else if (data.geo?.coordinates?.length && data.geo.type === 'MultiPolygon') {
      boundaries.push(
        multiPolygon(data.geo.coordinates, {
          name: data.title,
          slug: data.slug || regionSlug,
        }),
      )
    }

    return featureCollection(boundaries)
  }

  /**
   * Fetches detailed air quality data for multiple stations concurrently based on their UIDs.
   *
   * @param regionData - An array of AQRegionData, each containing a station UID.
   * @returns A promise resolving to an array of detailed AQStationData for each station.
   */
  private static async getAirQuality(regionData: AQRegionData[]): Promise<AQStationData[]> {
    // Concurrently fetch detailed data for all stations using their UIDs.
    const qaStationResults = await Promise.all(
      regionData.map((data) => this.fetchAQStationData(data.uid)),
    )

    return qaStationResults
  }

  /**
   * Fetches detailed air quality data for a single station from the external API using its unique ID.
   *
   * @param stationId - The unique identifier of the air quality station.
   * @returns A promise resolving to the detailed AQStationData for the specified station.
   * @throws Error if API credentials are not set or the API request fails.
   */
  private static async fetchAQStationData(stationId: number): Promise<AQStationData> {
    if (!API_TOKEN || !API_URL) {
      throw new Error('Air quality API token or URL not set')
    }

    try {
      const stationUrl = `${API_URL}/feed/@${stationId}?token=${API_TOKEN}`

      const response = await fetch(stationUrl)

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`)
      }

      const result = await response.json()

      const picked = (({ aqi, idx, city, iaqi, attributions, dominentpol, time }) => ({
        aqi,
        idx,
        city,
        iaqi,
        attributions,
        dominentpol,
        time,
      }))(result.data)

      return picked
    } catch (error) {
      throw new Error('Failed to fetch air quality station data from API')
    }
  }
}
