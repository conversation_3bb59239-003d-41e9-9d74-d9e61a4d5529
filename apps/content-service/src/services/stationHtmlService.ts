import { getFuelCache } from '@/cache/fuelCache.js'
import { getStateNameByValue } from '@/data/fuel/states.js'
import type { EControlStationResult, StationsResult } from '@/services/eControlService.js'

import type { Cache } from '@regionalmedienaustria/cache-util'

const CACHE_TTL = 60 * 60 // 1hr

export default class StationHtmlService {
  private static cache: Cache.RmaCache

  public static async getStationsHtmlFromCache(
    stationsObj: StationsResult,
    key: string,
    national: boolean,
  ): Promise<string> {
    this.cache = await getFuelCache()

    const data = await this.cache.getWithFunction({
      key: key,
      storeFunction: StationHtmlService.cacheHtmlHelper,
      params: [{ stationsObj, national }],
      ttl: CACHE_TTL,
    })

    return data
  }

  public static async getStationsHtml(
    stationsResult: StationsResult,
    prefix: string,
  ): Promise<string> {
    const html = await StationHtmlService.getStationsHtmlFromCache(
      stationsResult,
      `stations-html-${prefix}`,
      false,
    )
    return html
  }

  private static async cacheHtmlHelper(obj: {
    stationsObj: StationsResult
    national: boolean
  }): Promise<string> {
    let html = ''
    if (obj.national) {
      html = StationHtmlService.getHtmlStringNational(obj.stationsObj)
    } else {
      html = StationHtmlService.getHtmlString(obj.stationsObj)
    }
    return html
  }

  private static getHtmlString(stationsObj: StationsResult): string {
    let dieContainer = ''
    let supContainer = ''

    if (stationsObj.dieStations && stationsObj.dieStations.length > 0) {
      dieContainer = StationHtmlService.createContainersHtml(stationsObj.dieStations, 'die')
    }
    if (stationsObj.supStations && stationsObj.supStations.length > 0) {
      supContainer = StationHtmlService.createContainersHtml(stationsObj.supStations, 'sup')
    }
    const mainContainer = `<h2>Diesel</h2>${dieContainer}<div id="die-map"></div><h2>Super</h2>${supContainer}<div id="sup-map"></div>`
    return mainContainer
  }

  private static createContainersHtml(stations: EControlStationResult[], prefix: string): string {
    const containers: string[] = []
    let counter = 1
    stations.forEach((station) => {
      const html = `<div id="${prefix}-container"><p><b>${counter}. ${station.name}</b><br>${
        station.location.address
      }, ${station.location.postalCode} ${station.location.city}<br>${
        station.prices[0].label
      } €&thinsp;${String(station.prices[0].amount).replace('.', ',')}</p></div>`
      containers.push(html)
      counter++
    })
    return containers.join(' ')
  }

  private static getHtmlStringNational(stationsObj: StationsResult): string {
    let html = '<a class="button action" href="#nat-map" target="_self">Zur Kartenansicht</a><br>'
    if (stationsObj.dieStations && stationsObj.supStations) {
      const dieselMap = StationHtmlService.getHtmlFromAllStations(stationsObj.dieStations)
      const superMap = StationHtmlService.getHtmlFromAllStations(stationsObj.supStations)
      dieselMap.forEach((dieselHtmls, stateName) => {
        let prepos = 'in'
        if (stateName === 'Burgenland') {
          prepos = 'im'
        } else if (stateName === 'Steiermark') {
          prepos = 'in der'
        }
        html += `<h2>Günstig tanken ${prepos} ${stateName}</h2><b>Diesel:</b></br>${dieselHtmls.join(
          ' ',
        )}</br><b>Super:</b></br>${superMap.get(stateName)?.join(' ')}<br>`
      })
    }
    html += '<div id="nat-map"></div>'
    return html
  }

  private static getHtmlFromAllStations(stations: EControlStationResult[]): Map<string, string[]> {
    const stationsHtml: string[] = []
    for (let i = 0; i < stations.length; i++) {
      const html = `
      ${String(stations[i].prices[0].amount).replace('.', ',')} €,
      ${stations[i].name},
      ${stations[i].location.address},
      ${stations[i].location.postalCode},
      ${stations[i].location.city}
      <br>`
      stationsHtml.push(html)
    }
    const htmlMap: Map<string, string[]> = new Map<string, string[]>()
    const chunkSize = 5
    let count = 1
    for (let i = 0; i < stationsHtml.length; i += chunkSize) {
      const chunk = stationsHtml.slice(i, i + chunkSize)
      htmlMap.set(getStateNameByValue(count), chunk)
      count++
    }
    return htmlMap
  }
}
