import {
  constants,
  logger,
  requestUtil,
  RmaError,
  Security,
} from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'

import { RSS2AdsService } from '@/services/rss2adsService.js'

export class RSS2AdsController {
  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async getbyId(req: Request, res: Response) {
    const rss2adsId = requestUtil.getParameter(req, 'id', undefined)

    if (!rss2adsId) {
      const noIdError = new Error('No RSS2Ads ID provided')
      logger.error(noIdError)
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: noIdError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      const rss2adsData = await RSS2AdsService.getRSS2Ads(+rss2adsId)

      if (!rss2adsData) {
        res.status(constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND).send(
          new RmaError({
            error: new Error(`No RSS2Ads found with id: ${rss2adsId}`),
            statusCode: constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND,
          }),
        )
      } else {
        res.status(constants.STATUS_CODE.SUCESS.OK).send({
          data: {
            rss2adsData,
            directusUrl: process.env.CMS_API_URL,
          },
        })
      }
    } catch (error: any) {
      if (error.errors && error.errors.length > 0) {
        logger.error(error.errors[0])
      } else {
        logger.error(JSON.stringify(error, null, 2))
      }

      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }
}
