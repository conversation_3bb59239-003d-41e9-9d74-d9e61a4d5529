/* eslint-disable complexity */
/* eslint-disable max-lines-per-function */
import {
  RmaError,
  logger,
  requestUtil,
  Security,
  constants,
  helpers,
} from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'

import { CaptchaError } from '@/errors/captchaError.js'
import { CaptchaService } from '@/services/captchaService.js'
import { VoteableUploadService } from '@/services/voteableUploadService.js'
import { getVotingChallengeIdOrSlug } from '@/utils/voting.js'

const isLocal = process.env.APP_ENV === 'development'

export class VoteableUploadController {
  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async uploadItem(req: Request, res: Response) {
    const draftMode = requestUtil.getParameter(req, 'draftmode', undefined)
    try {
      const uploadServiceResponse = await VoteableUploadService.uploadItem({
        requestData: req.body,
        file: req.file,
        bypassCache: draftMode === 'true',
      })

      res.status(constants.STATUS_CODE.SUCESS.OK).send(uploadServiceResponse)
    } catch (error: any) {
      logger.error(error.message)
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
          message: error.message,
        }),
      )
    }
  }

  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async updateFile(req: Request, res: Response) {
    try {
      const uploadServiceResponse = await VoteableUploadService.updateFile({
        requestData: req.body,
      })

      res.status(constants.STATUS_CODE.SUCESS.OK).send(uploadServiceResponse)
    } catch (error: any) {
      logger.error(error.message)
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
          message: error.message,
        }),
      )
    }
  }

  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async voteForItem(req: Request, res: Response) {
    const itemId = requestUtil.getParameter(req, 'id', undefined)
    const solution = requestUtil.getParameter(req, 'solution', undefined)
    const votingGroup = requestUtil.getParameter(req, 'votingGroup', undefined)
    // honeypot field
    const name = requestUtil.getParameter(req, 'name', undefined)

    const { votingChallengeId, votingChallengeSlug } = getVotingChallengeIdOrSlug(votingGroup)

    if (!itemId) {
      const noItemIdError = new Error('No item id provided')
      logger.error(noItemIdError)
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: noItemIdError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    if (!votingGroup) {
      const noVotingGroupError = new Error('No voting group provided')
      logger.error(noVotingGroupError)
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: noVotingGroupError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    if (!solution) {
      const noSolutionError = new Error('No solution provided')
      logger.error(noSolutionError)
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: noSolutionError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      const remoteIp = isLocal
        ? '127.0.0.1'
        : helpers.getClientIp({ req: req, loadBalancerIp: `${process.env.LOAD_BALANCER_IP}` })
      logger.info('remoteIp', remoteIp, isLocal, req.headers['x-forwarded-for'])

      if (!remoteIp) {
        logger.error('No remote ip provided', {
          ip: remoteIp,
          headers: req.headers,
        })

        const noRemoteIpError = new Error('Abstimmen ist nicht möglich')
        res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
          new RmaError({
            error: noRemoteIpError,
            statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
          }),
        )
        return
      }

      if (name) {
        logger.warn(`Honeypot field filled: ${name} from ip ${remoteIp}`)
        // send random id above 300000
        return res
          .status(constants.STATUS_CODE.SUCESS.OK)
          .send({ id: Math.floor(Math.random() * 100000) + 300000 })
      }

      const hasVotedResult = await VoteableUploadService.hasVotedWithin24Hours({
        votingGroup,
        remoteIp,
      })

      if (hasVotedResult.hasVotedRecently) {
        logger.error(`IP Hash ${hasVotedResult.ipHash} has already voted within the last 24 hours`)

        return res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send({
          message: 'IP has already voted within the last 24 hours',
          votedAt: hasVotedResult.votedAt,
        })
      }

      const isVoteValid = await VoteableUploadService.isVoteValid({
        itemId,
        votingChallengeSlug,
        votingChallengeId,
      })

      if (!isVoteValid) {
        throw new Error('Vote is not valid')
      }

      const validationResult = await CaptchaService.validateSolution({ solution })

      if (!validationResult.success) {
        throw new CaptchaError(
          `FriendlyCaptcha returned errors: ${validationResult.errors} ${validationResult.details}`,
        )
      }

      const voteId = await VoteableUploadService.voteForItem({ itemId })

      await VoteableUploadService.setVoteForIp({ votingGroup, remoteIp })

      res.status(constants.STATUS_CODE.SUCESS.OK).send({ id: voteId })
    } catch (error: any) {
      logger.error(error.message)

      if (error instanceof CaptchaError) {
        throw error
      }

      throw new RmaError({
        error: error,
        statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
      })
    }
  }

  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async getMinimalVoteableUpload(req: Request, res: Response) {
    const itemId = requestUtil.getParameter(req, 'id', undefined)
    const votingGroup = requestUtil.getParameter(req, 'votingGroup', undefined)

    const { votingChallengeId, votingChallengeSlug } = getVotingChallengeIdOrSlug(votingGroup)

    if (!itemId || (!votingChallengeId && !votingChallengeSlug)) {
      const noItemIdError = new Error('No item id or votingGroup provided')
      logger.error(noItemIdError)
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: noItemIdError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      const data = await VoteableUploadService.getVoteableUploadForWidget({
        itemId,
        votingChallengeSlug,
        votingChallengeId,
      })

      res.status(constants.STATUS_CODE.SUCESS.OK).send({ data })
    } catch (error: any) {
      logger.error(error.message)
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }
}
