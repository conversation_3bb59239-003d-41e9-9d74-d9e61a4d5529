import { constants } from '@regionalmedienaustria/microservice-utils'

import { AdsConfigService } from '@/services/adsConfigService.js'

import type { Request, Response } from 'express'

export class AdsConfigController {
  public static async getMergedAdsTxt(req: Request, res: Response) {
    try {
      const mergedAdsTxt = await AdsConfigService.getMergedAdsTxt()
      res.setHeader('Content-Type', 'text/plain')
      res.status(constants.STATUS_CODE.SUCESS.OK).send(mergedAdsTxt)
    } catch (e) {
      console.error('Error fetching merged ads.txt:', e)
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send('Error fetching ads.txt')
    }
  }
}
