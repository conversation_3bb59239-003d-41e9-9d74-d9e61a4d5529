import { constants, logger, requestUtil } from '@regionalmedienaustria/microservice-utils'
import { Security } from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'
import { ZodError, type z } from 'zod'

import { PharmacyService } from '@/services/pharmacy/pharmacyService.js'
import { categoryValidatorArray } from '@/services/pharmacy/pharmacyStatus.js'

export class PharmacyController {
  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async getPharmacies(req: Request, res: Response) {
    try {
      const regionParam = requestUtil.getParameter(req, 'region', undefined)
      const categoryParam = requestUtil.getParameter(req, 'category', undefined)

      if (!regionParam) {
        throw new Error('Region is required')
      }

      let category: z.infer<typeof categoryValidatorArray> | undefined
      if (categoryParam) {
        category = categoryValidatorArray.parse(categoryParam.split(','))
      }

      const pharmacyInfo = await PharmacyService.getPharmacyInfo({
        region: regionParam,
        category,
      })

      res.status(constants.STATUS_CODE.SUCESS.OK).send(pharmacyInfo)
    } catch (error: any) {
      if (error instanceof ZodError) {
        logger.warn('Error fetching pharmacies:', error.issues)
        res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(error.message)
      } else {
        logger.error('Error fetching  pharmacies:', error)
        res
          .status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR)
          .send('Error fetching pharmacies')
      }
    }
  }
}
