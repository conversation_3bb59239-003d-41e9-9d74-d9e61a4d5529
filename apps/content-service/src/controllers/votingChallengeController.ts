import {
  RmaError,
  logger,
  requestUtil,
  Security,
  constants,
} from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'

import { VotingChallengeService } from '@/services/votingChallengeService.js'
import { getVotingChallengeIdOrSlug } from '@/utils/voting.js'

export class VotingChallengeController {
  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async getByIdOrSlug(req: Request, res: Response) {
    const id = requestUtil.getParameter(req, 'id', undefined)
    const bypassCache = requestUtil.getParameter(req, 'bypassCache', false)

    const { votingChallengeId, votingChallengeSlug } = getVotingChallengeIdOrSlug(id)

    if (!id) {
      const noIdError = new Error('No id provided')
      logger.error(noIdError)
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: noIdError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      const challengeData = await VotingChallengeService.getChallenge({
        id: votingChallengeId,
        slug: votingChallengeSlug,
        bypassCache,
      })

      if (!challengeData) {
        res.status(constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND).send(
          new RmaError({
            error: new Error(`No voting challenge found with id: ${id}`),
            statusCode: constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND,
          }),
        )
      } else {
        res.status(constants.STATUS_CODE.SUCESS.OK).send({
          data: {
            ...challengeData,
            directusUrl: process.env.CMS_API_URL,
          },
        })
      }
    } catch (error: any) {
      logger.error(error.message)
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }

  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async getVoteableItemsForChallenge(req: Request, res: Response) {
    const id = requestUtil.getParameter(req, 'id', undefined)
    const bypassCache = requestUtil.getParameter(req, 'bypassCache', false)

    const { votingChallengeId, votingChallengeSlug } = getVotingChallengeIdOrSlug(id)

    if (!id) {
      const noIdError = new Error('No id provided')
      logger.error(noIdError)
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: noIdError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      const voteableItems = await VotingChallengeService.getVoteableItemsForChallenge({
        id: votingChallengeId,
        slug: votingChallengeSlug,
        bypassCache,
      })

      if (!voteableItems) {
        res.status(constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND).send(
          new RmaError({
            error: new Error(`No voting challenge found with id: ${id}`),
            statusCode: constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND,
          }),
        )
      } else {
        res.status(constants.STATUS_CODE.SUCESS.OK).send({
          data: voteableItems,
        })
      }
    } catch (error: any) {
      logger.error(error.message)
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }
}
