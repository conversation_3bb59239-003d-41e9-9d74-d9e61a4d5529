import { RmaError, logger, requestUtil, Security } from '@regionalmedienaustria/microservice-utils'
import { constants } from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'

import { VotingResultService } from '@/services/votingResultService.js'
import { getVotingChallengeIdOrSlug } from '@/utils/voting.js'

export class VotingResultController {
  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async getVotingResults(req: Request, res: Response) {
    const challengeId = requestUtil.getParameter(req, 'challengeId', undefined)

    const { votingChallengeId, votingChallengeSlug } = getVotingChallengeIdOrSlug(challengeId)

    if (!challengeId) {
      const noChallengeIdError = new Error('No challenge id provided')
      logger.error(noChallengeIdError)
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: noChallengeIdError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      const votingResults = await VotingResultService.getVotingResults({
        votingChallengeId,
        votingChallengeSlug,
      })

      if (!votingResults) {
        res.status(constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND).send(
          new RmaError({
            error: new Error(`No voting results found for challenge with id/slug: ${challengeId}`),
            statusCode: constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND,
          }),
        )
        return
      }

      res
        .status(constants.STATUS_CODE.SUCESS.OK)
        .header('Cache-Control', votingResults.isVotingActive ? 'max-age=900' : 'max-age=86400')
        .send(votingResults)
    } catch (error: any) {
      logger.error(error.message)
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }
}
