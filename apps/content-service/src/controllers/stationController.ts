import { Security } from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'

import { getDistrictObject } from '@/data/fuel/districts.js'
import { getStateCode } from '@/data/fuel/states.js'
import EControlService from '@/services/eControlService.js'

export default class StationController {
  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  static async getStations(req: Request, res: Response) {
    const code = req.query.code
    const type = req.query.type
    const latitude = req.query.latitude
    const longitude = req.query.longitude

    let stationsResult
    if (typeof latitude === 'string' && typeof longitude === 'string') {
      stationsResult = await EControlService.getStationsByAddress(+latitude, +longitude, 'address')
    }
    if (typeof code === 'string' && typeof type === 'string') {
      stationsResult = await EControlService.getStationsByRegion(code, type, 'region')
    }
    res.status(200).send(stationsResult)
  }

  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  static async getAllStations(req: Request, res: Response) {
    const stationsResult = await EControlService.getAllStations()
    res.status(200).send(stationsResult)
  }

  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  static async getStationsByDistrictName(req: Request, res: Response) {
    let district = req.query.district

    if (typeof district === 'string') {
      district = district.replaceAll('-', '_')
      const distrObj = getDistrictObject(district)
      if (
        (distrObj.ids && distrObj.ids[0] !== -1) ||
        (distrObj.locations && distrObj.locations.length > 0)
      ) {
        const stationsResult = await EControlService.getStationsByDistrictName(district)
        res.status(200).send(stationsResult)
      } else {
        const body = { msg: 'District not found' }
        res.status(404).send(body)
      }
    }
  }

  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  static async getStationsByStateName(req: Request, res: Response) {
    const state = req.query.state

    if (typeof state === 'string') {
      const stateCode = getStateCode(state)
      if (stateCode !== -1) {
        const stations = await EControlService.getStationsByStateName(state)
        res.status(200).send(stations)
      } else {
        const body = { msg: 'State not found' }
        res.status(404).send(body)
      }
    }
  }
}
