import { constants, requestUtil } from '@regionalmedienaustria/microservice-utils'
import { GallerySponsoringService } from '@/services/gallerySponsoringService.js'

import type { Request, Response } from 'express'

export class GalleryController {
  public static async loadSponsorings(req: Request, res: Response) {
    try {
      const state = requestUtil.getParameter(req, 'state', undefined)

      if (!state) {
        res.status(constants.STATUS_CODE.SUCESS.OK).send({ sponsoringIds: [] })
        return
      }

      const sponsoringIds = await GallerySponsoringService.loadSponsoringIds(state)
      res.status(constants.STATUS_CODE.SUCESS.OK).send({ sponsoringIds })
    } catch (e) {
      //fail gracefully
      res.status(constants.STATUS_CODE.SUCESS.OK).send({ sponsoringIds: [] })
    }
  }
}
