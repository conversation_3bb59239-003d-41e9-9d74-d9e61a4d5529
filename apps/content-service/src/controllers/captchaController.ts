import { requestUtil, RmaError, Security } from '@regionalmedienaustria/microservice-utils'
import { constants } from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'

import { CaptchaError } from '@/errors/captchaError.js'
import { CaptchaService } from '@/services/captchaService.js'

export class CaptchaController {
  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async verify(req: Request, res: Response) {
    const solution = requestUtil.getParameter(req, 'solution', undefined)

    if (!solution) {
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send({
        error: 'No solution provided',
      })
      return
    }

    try {
      const validationResult = await CaptchaService.validateSolution({ solution })

      if (!validationResult.success) {
        throw new CaptchaError(
          `FriendlyCaptcha returned errors: ${validationResult.errors} ${validationResult.details}`,
        )
      }

      res.status(constants.STATUS_CODE.SUCESS.OK).send({ isCaptchaValid: validationResult.success })
    } catch (error: any) {
      if (error instanceof CaptchaError) {
        res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(error)
      } else {
        res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
          new RmaError({
            error: error,
            statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
          }),
        )
      }
    }
  }
}
