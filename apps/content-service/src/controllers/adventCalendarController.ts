import {
  RmaError,
  logger,
  requestUtil,
  constants,
  Security,
} from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'

import { AdventCalendarService } from '@/services/adventCalendarService.js'

export class AdventCalendarController {
  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async getById(req: Request, res: Response) {
    const calendarId = requestUtil.getParameter(req, 'id', undefined)

    if (!calendarId) {
      const noIdError = new Error('No calendar ID provided')
      logger.error(noIdError)
      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: noIdError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      const adventData = await AdventCalendarService.getCalendar(+calendarId)

      if (!adventData) {
        res.status(constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND).send(
          new RmaError({
            error: new Error(`No advent calendar found with id: ${calendarId}`),
            statusCode: constants.STATUS_CODE.CLIENT_ERROR.NOT_FOUND,
          }),
        )
      } else {
        res.status(constants.STATUS_CODE.SUCESS.OK).send({
          data: {
            adventData,
            directusUrl: process.env.CMS_API_URL,
          },
        })
      }
    } catch (error: any) {
      logger.error(error.message)
      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }
}
