import { logger, requestUtil, Security } from '@regionalmedienaustria/microservice-utils'
import { constants } from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'

import { AirQualityService } from '@/services/airQualityService.js'

export class AirQualityController {
  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  public static async getAirQuality(req: Request, res: Response) {
    try {
      const regionParam = requestUtil.getParameter(req, 'region', undefined)

      if (!regionParam) {
        throw new Error('Region is required')
      }

      const airQualityInfo = await AirQualityService.getAirQualityInfo(regionParam)

      res.status(constants.STATUS_CODE.SUCESS.OK).send(airQualityInfo)
    } catch (error) {
      logger.error(error)
      res
        .status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR)
        .send('Error fetching air quality data.')
    }
  }
}
