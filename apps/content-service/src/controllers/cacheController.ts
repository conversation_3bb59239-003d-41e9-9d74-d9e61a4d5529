import { constants, logger, requestUtil, RmaError } from '@regionalmedienaustria/microservice-utils'

import { CacheService } from '@/services/cacheService.js'

import type { Request, Response } from 'express'

export class CacheController {
  /**
   * Clears the cache for a specific item of a collection
   */
  public static async clearCache(req: Request, res: Response) {
    const collection = requestUtil.getParameter(req, 'collection', undefined)
    const itemId = requestUtil.getParameter(req, 'id', undefined)

    if (!collection || !itemId) {
      const missingParamError = new Error('No collection or ID provided')
      logger.error(missingParamError)

      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: missingParamError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      await CacheService.clearCollectionItemCache(collection, itemId)
      logger.info(`Cache cleared for collection ${collection} with ID ${itemId}`)
      return res.status(constants.STATUS_CODE.SUCESS.OK).send({
        message: `Cache cleared for collection ${collection} with ID ${itemId}`,
        success: true,
      })
    } catch (error) {
      logger.error(error)

      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }

  /**
   * Clears all cache entries for a specific collection
   */
  public static async clearCollectionCache(req: Request, res: Response) {
    const collection = requestUtil.getParameter(req, 'collection', undefined)

    if (!collection) {
      const missingParamError = new Error('No collection provided')
      logger.error(missingParamError)

      res.status(constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST).send(
        new RmaError({
          error: missingParamError,
          statusCode: constants.STATUS_CODE.CLIENT_ERROR.BAD_REQUEST,
        }),
      )
      return
    }

    try {
      await CacheService.clearAllCollectionCache(collection)
      return res.status(constants.STATUS_CODE.SUCESS.OK).send({
        message: `All cache entries cleared for collection ${collection}`,
        success: true,
      })
    } catch (error) {
      logger.error(error)

      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }

  public static async clearAllCache(req: Request, res: Response) {
    try {
      const results = await CacheService.clearAllCaches()

      // Check if all operations were successful
      const allSuccessful = Object.values(results).every((result) => result)

      return res.status(constants.STATUS_CODE.SUCESS.OK).send({
        message: 'Cache clearing completed',
        results,
        success: allSuccessful,
      })
    } catch (error) {
      logger.error(error)

      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }
}
