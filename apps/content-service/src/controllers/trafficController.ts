/* eslint-disable no-underscore-dangle */
import {
  constants,
  logger,
  requestUtil,
  RmaError,
  Security,
} from '@regionalmedienaustria/microservice-utils'
import { Request, Response } from 'express'

import TrafficService from '@/services/trafficService.js'

export default class TrafficController {
  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  static async getWebcamData(req: Request, res: Response) {
    const state: string = requestUtil.getParameter(req, 'state', '')

    try {
      const responseData = await TrafficService.getWebcamData(state)
      res.status(200).send(responseData)
    } catch (error) {
      logger.error(error)

      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }

  @Security.Grant(Security.ApiKeys.MeinBezirkWeb)
  static async getWebcamHtml(req: Request, res: Response) {
    const state: string = requestUtil.getParameter(req, 'state', '')

    try {
      const responseData = await TrafficService.getWebcamHtml(state)
      res.status(200).send(responseData)
    } catch (error) {
      logger.error(error)

      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }

  static async getTrafficList(req: Request, res: Response) {
    const route: string = requestUtil.getParameter(req, 'route', '')
    const state: string = requestUtil.getParameter(req, 'state', '')

    try {
      const responseData = await TrafficService.getTrafficList(route, state)
      res.status(200).send(responseData)
    } catch (error) {
      logger.error(error)

      res.status(constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR).send(
        new RmaError({
          error: error,
          statusCode: constants.STATUS_CODE.SERVER_ERROR.INTERNAL_SERVER_ERROR,
        }),
      )
    }
  }
}
