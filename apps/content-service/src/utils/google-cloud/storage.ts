import { Storage } from '@google-cloud/storage'
import type { StorageOptions } from '@google-cloud/storage'

export class GoogleCloudStorageUtil {
  private storage: Storage

  private credentials: StorageOptions['credentials']

  private initStorage() {
    this.storage = new Storage({
      credentials: this.credentials,
    })
  }

  constructor(base64Credentials?: string) {
    this.credentials = this.parseCredentials(base64Credentials)
    this.initStorage()
  }

  private parseCredentials(base64Credentials?: string) {
    if (!base64Credentials) {
      throw new Error('No base64 credentials for storage service account found')
    }

    try {
      const credentials = JSON.parse(Buffer.from(base64Credentials, 'base64').toString())
      return credentials
    } catch (err) {
      throw new Error('Error parsing base64 credentials for storage service account')
    }
  }

  getStorageInstance(): Storage {
    if (!this.storage) {
      this.initStorage()
    }
    return this.storage
  }
}
