import {
  createRMADirectusPublicClient,
  createRMADirectusTokenClient,
} from '@rma-mono/directus-client'

export const uploadClient = createRMADirectusTokenClient(
  process.env.CMS_API_URL!,
  process.env.DIRECTUS_UPLOADER_TOKEN!,
)

export const votingClient = createRMADirectusTokenClient(
  process.env.CMS_API_URL!,
  process.env.DIRECTUS_VOTING_TOKEN!,
)

export const publicClient = createRMADirectusPublicClient(process.env.CMS_API_URL!)
