import { createFolder, readFolders } from '@rma-mono/directus-client'

import { uploadClient } from './client.js'

import type { DirectusFolders } from '@rma-mono/directus-client'

export class DirectusFolderService {
  public static async getFolders({ parentId }: { parentId?: string | undefined }) {
    const folders = await uploadClient.request(
      readFolders({
        fields: ['id', 'name', 'parent'],
        ...(parentId
          ? {
              filter: {
                parent: {
                  _eq: parentId,
                },
              },
            }
          : {}),
      }),
    )

    return folders
  }

  /**
   *
   * @param folderPath needs a starting and trailing "/". i.e.: "/State/User Uploads/Challenge/"
   * @returns
   */
  public static async findOrCreateFolderViaPath(folderPath: string) {
    const filePathsWithFolder = await this.constructFolderPaths()

    const matchingFolderKey = Object.keys(filePathsWithFolder).find((path) => path === folderPath)

    const folderId = matchingFolderKey ? filePathsWithFolder[matchingFolderKey].id : undefined

    if (folderId) {
      return folderId
    }

    /**
     * Currently if the folder path isn't found we fetch down the entire folder structure and create the folder
     * So for every level we make an API Call
     * TODO: make even for efficient by finding nearest partial match and then creating the rest
     */

    const folderNames = folderPath.split('/').filter((folderName) => folderName.length > 0)
    let parentId: string | undefined = undefined
    for (const folderName of folderNames) {
      parentId = await this.getOrCreateFolder({
        parentId,
        folderName,
      })
    }

    return parentId
  }

  public static async getOrCreateFolder({
    folderName,
    parentId,
  }: {
    folderName: string
    parentId?: string | undefined
  }) {
    const folders = await this.getFolders({
      parentId,
    })

    const matchingFolder = folders.find((folder) => {
      const nameMatches = folder.name === folderName
      const shouldMatchParent = parentId !== undefined

      if (shouldMatchParent) {
        return nameMatches && folder.parent === parentId
      }

      return nameMatches && folder.parent === null
    })

    if (matchingFolder) {
      return matchingFolder.id
    }

    try {
      const createdFolder = await uploadClient.request(
        createFolder({
          name: folderName,
          ...(parentId ? { parent: parentId } : {}),
        }),
      )

      if (!createdFolder.id) {
        throw new Error()
      }

      return createdFolder.id
    } catch (error) {
      console.error(error, { folderName, parentId })
      throw new Error(`Could not create folder ${folderName} with parentId ${parentId}.`)
    }
  }

  /**
   *
   * @param folderPath
   * @returns
   */
  public static async constructFolderPaths() {
    const nestedFolders = await uploadClient.request(
      readFolders({
        fields: [
          'id',
          'name',
          'parent',
          // @ts-expect-error – a bit hacky but this ensures we get all nested folders 10 levels deep (max relational depth)
          'parent.*.*.*.*.*.*.*.*.*',
        ],
      }),
    )

    const buildFolderPath = (
      path: string,
      parent: DirectusFolders | string | undefined | null,
    ): string => {
      if (!parent || typeof parent === 'string') {
        return path
      }

      return buildFolderPath(`${parent.name}/${path}`, parent.parent)
    }

    const filePathsWithFolder = nestedFolders.reduce((acc, folder) => {
      const path = buildFolderPath('', folder)
      acc['/' + path] = folder
      return acc
    }, {} as Record<string, DirectusFolders>)

    return filePathsWithFolder
  }
}
