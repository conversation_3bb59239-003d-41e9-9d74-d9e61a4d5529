import { uploadFiles } from '@rma-mono/directus-client'

import { getReplacedFileName } from '../utils.js'

import { uploadClient } from './client.js'
import { DirectusFolderService } from './folders.js'

export async function uploadFileToDirectus({
  file,
  name,
  folderPath,
}: {
  file: Express.Multer.File
  name?: string
  folderPath: string
}) {
  const folder = await DirectusFolderService.findOrCreateFolderViaPath(folderPath)

  if (!folder) {
    throw new Error(`Failed to create folder "${folderPath}" in Directus`)
  }

  const formData = new FormData()
  formData.append('folder', folder)

  const fileName = getReplacedFileName({
    originalFileName: file.originalname,
    newName: name,
  })

  formData.append('title', fileName)
  formData.append('file', new Blob([file.buffer], { type: file.mimetype }), fileName)

  const directusFile = await uploadClient.request(uploadFiles(formData))

  if (!directusFile.id) {
    throw new Error('Failed to upload file to Directus')
  }

  return directusFile
}
