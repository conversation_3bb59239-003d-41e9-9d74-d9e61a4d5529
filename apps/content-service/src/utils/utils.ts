import { createHash } from 'node:crypto'

/**
 * Get a file from a base64 string
 * @example getFileFromBase64String('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII=')
 * @returns The file
 */
export const getFileFromBase64String = (base64String: string) => {
  const byteString = atob(base64String)
  const ab = new ArrayBuffer(byteString.length)
  const ia = new Uint8Array(ab)

  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i)
  }
  // Wrap the ArrayBuffer in a Uint8Array
  const uint8Array = new Uint8Array(ab)
  // Create Blob from Uint8Array
  const blob = new Blob([uint8Array], { type: 'image/png' })
  return blob
}

export const hashString = (str: string) => createHash('sha256').update(str).digest('hex')

export const isInteger = (value: any) => !isNaN(Number(value))

/**
 * Get a date at a point in time
 * @example getDateAtPoint({ days: 1 }) // Tomorrow
 * @example getDateAtPoint({ days: -3, months: 1 }) // 3 days ago, 1 month ago
 * @returns The date at the point in time
 */
export const getRelativeDate = ({
  days,
  months,
  years,
}: {
  days?: number
  months?: number
  years?: number
}) => {
  const date = new Date()

  if (days) {
    date.setDate(date.getDate() + days)
  }

  if (months) {
    date.setMonth(date.getMonth() + months)
  }

  if (years) {
    date.setFullYear(date.getFullYear() + years)
  }

  return date.toISOString().split('T')[0]
}

/**
 * Get the first and last day of the current week
 * @returns The object with the monday and sunday date of the current week
 */
export const getCurrentWeekBoundaries = () => {
  const today = new Date()
  const currentDay = today.getDay()

  return {
    monday: getRelativeDate({
      days: currentDay === 0 ? -6 : -(currentDay - 1),
    }),
    sunday: getRelativeDate({
      days: currentDay === 0 ? 0 : 7 - currentDay,
    }),
  }
}

/**
 * Get a new file name with a new name or the original file name with the same ending
 * @example getReplacedFileName({ originalFileName: 'test.png', newName: 'newName' }) // newName.png
 * @example getReplacedFileName({ originalFileName: 'test.png' }) // test.png
 * @returns The new file name
 */
export const getReplacedFileName = ({
  originalFileName,
  newName,
}: {
  originalFileName: string
  newName?: string
}) => {
  const fileNameSplit = originalFileName.split('.')
  const fileEnding = fileNameSplit[fileNameSplit.length - 1]

  return `${newName || fileNameSplit[0]}.${fileEnding}`
}

/**
 * Format a date to a readable format
 * @example formatDate(new Date()) // 30-01-2025-10:00:00
 * @returns The formatted date
 */
export const formatDate = (date: Date) =>
  date
    .toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    })
    .replace(/\//g, '-')
    .replace(',', '-')
    .replace(' ', '')
