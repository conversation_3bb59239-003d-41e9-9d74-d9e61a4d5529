import type { CacheConfig } from '@rma-mono/cache-clearing-util'

/**
 * Mapping of collection names to their cache configurations
 * Each collection has a Redis prefix and a CDN route
 * Only include collections that are connected to Directus
 */
const COLLECTIONS_MAPPING: Record<string, CacheConfig> = {
  rss_2_ads: {
    redisPrefix: 'rss2ads',
    cdnRoute: '/v1/content/rss2ads',
  },
  voting_challenge: {
    redisPrefix: 'voting-challenge',
    cdnRoute: '/v1/content/voting-challenge',
  },
  advent_calendar: {
    redisPrefix: 'advent-calendar',
    cdnRoute: '/v1/content/advent',
  },
  ads_config: {
    redisPrefix: 'ads-config',
    cdnRoute: '/v1/content/adsConfig',
  },
  voting_result: {
    redisPrefix: 'voting-result',
    cdnRoute: '/v1/content/voting-result',
  },
  gallery_sponsoring: {
    redisPrefix: 'gallery-sponsoring',
    cdnRoute: '/v1/content/gallery-sponsoring',
  },
  air_quality: {
    redisPrefix: 'air-quality',
    cdnRoute: '/v1/content/air-quality',
  },
}

/**
 * Returns the cache configuration for a specific collection
 */
export function getCollectionCacheObject(collection: string): CacheConfig | undefined {
  const collectionKey = collection as keyof typeof COLLECTIONS_MAPPING
  return COLLECTIONS_MAPPING[collectionKey]
}

/**
 * Returns all collection cache configurations
 */
export function getAllCollectionCacheObjects(): Record<string, CacheConfig> {
  return COLLECTIONS_MAPPING
}
