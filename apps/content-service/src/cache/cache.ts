import { Cache } from '@regionalmedienaustria/cache-util'
import { logger } from '@regionalmedienaustria/microservice-utils'

let cache: Cache.RmaCache

export const getCache = async () => {
  if (!cache) {
    if (
      process.env.NODE_ENV === 'development' ||
      process.env.NODE_ENV === 'local' ||
      !process.env.NODE_ENV
    ) {
      logger.info('Development mode: Using memoryCache by default')
      cache = await Cache.CacheFactory.getCache(Cache.CacheType.NodeCache, 20)

      return cache
    }
    try {
      logger.info(
        `try to connect to REDIS ${process.env.MICROSERVICE_REDIS_HOST}:${process.env.MICROSERVICE_REDIS_PORT}`,
      )
      const caString = Buffer.from(process.env.MICROSERVICE_REDIS_CA || '', 'base64').toString(
        'ascii',
      )

      cache = await Cache.CacheFactory.getCache(Cache.CacheType.Redis, 20, {
        host: process.env.MICROSERVICE_REDIS_HOST,
        port: process.env.MICROSERVICE_REDIS_PORT
          ? parseInt(process.env.MICROSERVICE_REDIS_PORT)
          : 6378,
        prefix: 'content',
        ca: caString,
      })
    } catch (error) {
      logger.error(`error connecting to redis: ${error}`)
      logger.info(`Fallback to memoryCache`)
      cache = await Cache.CacheFactory.getCache(Cache.CacheType.NodeCache, 20)
    }
  }
  return cache
}
