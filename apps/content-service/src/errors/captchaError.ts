import { RmaError } from '@regionalmedienaustria/microservice-utils'
import { constants } from '@regionalmedienaustria/microservice-utils'

export class CaptchaError extends RmaError {
  constructor(message: string) {
    super({
      message,
      body: {
        success: false,
        message,
        error: 'CAPTCHA_ERROR',
      },
      statusCode: constants.STATUS_CODE.CLIENT_ERROR.FORBIDDEN,
    })
  }
}
