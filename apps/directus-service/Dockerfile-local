FROM directus/directus:11.6.1

WORKDIR /directus

# copy peiq-role-mapping
COPY ./extensions/peiq-role-mapping/dist ./extensions/peiq-role-mapping/dist
COPY ./extensions/peiq-role-mapping/package.json ./extensions/peiq-role-mapping/package.json

# copy csv extension
COPY ./extensions/directus-extension-csv/dist ./extensions/directus-extension-csv/dist
COPY ./extensions/directus-extension-csv/package.json ./extensions/directus-extension-csv/package.json

# copy search extension
COPY ./extensions/directus-extension-custom-search/dist ./extensions/directus-extension-custom-search/dist
COPY ./extensions/directus-extension-custom-search/package.json ./extensions/directus-extension-custom-search/package.json

# copy migrations
COPY ./migrations ./migrations

COPY .env .env
COPY .npmrc .npmrc
COPY gcp-sa-key.json .

USER root
RUN corepack enable

# Workaround to be able to install extensions with 'node' user, only required in v11.1.1
RUN test -f node_modules/.modules.yaml && rm node_modules/.modules.yaml || true

ENV COREPACK_DEFAULT_TO_LATEST=0
# Install extensions while still root user
# the last 3 ai extension are for stage only... for now
RUN pnpm install directus-extension-sync @directus-labs/calculated-fields-bundle directus-extension-generate-types directus-extension-field-actions @directus-labs/rss-to-json-operation @directus-labs/ai-web-scraper-operation @directus-labs/ai-writer-operation @directus-labs/migration-bundle

RUN cd ./extensions/peiq-role-mapping && pnpm install
RUN cd ./extensions/directus-extension-csv && pnpm install
RUN cd ./extensions/directus-extension-custom-search && pnpm install

# Change ownership of the extension directories to node user
RUN chown -R node:node /directus/extensions

USER node

EXPOSE 8055

CMD npx directus bootstrap && npx directus database migrate:latest && npx directus start
