export async function up(knex) {
  try {
    await knex.schema.alterTable('basic_text', (table) => {
      table.text('description', 'mediumtext').alter()
    })
  } catch (error) {
    console.error('Error running migration up', error)
  }
}

export async function down(knex) {
  try {
    await knex.schema.alterTable('basic_text', (table) => {
      table.text('description').alter() // defaults to text (https://knexjs.org/guide/schema-builder.html#text)
    })
  } catch (error) {
    console.error('Error running migration down', error)
  }
}
