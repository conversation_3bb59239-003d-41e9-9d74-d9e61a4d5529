version: '3'
services:
  #https://github.com/directus/directus/discussions/17005
  database:
    container_name: ${STACK_PRFX}-database
    image: mysql:8.0
    volumes:
      - ./data:/var/lib/mysql
    restart: unless-stopped
    command:
      [
        '--character-set-server=utf8mb4',
        '--collation-server=utf8mb4_unicode_ci',
        '--default-authentication-plugin=mysql_native_password',
      ]
    env_file:
      - .env
    environment:
      - COREPACK_DEFAULT_TO_LATEST=0
    ports:
      - 3306:3306
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost', '-u$$MYSQL_USER', '-p$$MYSQL_PASSWORD']
      interval: 5s
      timeout: 5s
      retries: 5

  directus:
    build:
      dockerfile: Dockerfile-local
      context: ./
    depends_on:
      database:
        condition: service_healthy
    ports:
      - 8055:8055
    restart: unless-stopped
    volumes:
      - ./uploads:/directus/uploads
      - ./extensions:/directus/extensions
      - ./migrations:/directus/migrations
    env_file:
      - .env
    command:
      - /bin/sh
      - -c
      - |
        npx directus bootstrap &&
        echo 'migrating things' &&
        npx directus database migrate:latest &&
        npx directus start
