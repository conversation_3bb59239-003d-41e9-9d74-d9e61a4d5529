PUBLIC_URL="https://stage-cms.meinbezirk.at/"

#Security
PASSWORD_RESET_URL_ALLOW_LIST="https://stage-cms.meinbezirk.at/admin/reset-password"

DB_CLIENT="mysql"
DB_PORT="3306"
DB_DATABASE="directus"
DB_POOL__MIN=0 #https://github.com/directus/directus/issues/8634

#Assets
ASSETS_CACHE_TTL="30 days" #this is default
ASSETS_TRANSFORM_MAX_CONCURRENT=10 #default is 25

CACHE_ENABLED=true
CACHE_TTL="5m"
CACHE_AUTO_PURGE=true
CACHE_SYSTEM_TTL="10m"
CACHE_NAMESPACE="directus-cache"
CACHE_STATUS_HEADER="Dir-Cache-Status"
CACHE_CLEAR_URL="https://stage-api.meinbezirk.at/v1/content/cache/clear"
CACHE_CLEAR_URL_SERVICE_URL="https://stage.bezirk.at/clear"

CACHE_STORE=redis
REDIS_DB=0
REDIS_TLS__REJECT_UNAUTHORIZED=false

STORAGE_LOCATIONS="gmedia"
STORAGE_GMEDIA_DRIVER="gcs"
STORAGE_GMEDIA_KEY_FILENAME="./gcp-sa-key/directus.json" # will be set within the container
STORAGE_GMEDIA_BUCKET="stage-rma-directus-media"

LOG_LEVEL="trace"
LOG_STYLE="pretty"
LOGGER_LEVELS="trace:DEBUG,debug:DEBUG,info:INFO,warn:WARNING,error:ERROR,fatal:CRITICAL"
LOGGER_MESSAGE_KEY="message"
LOGGER_HTTP_USE_LEVEL="trace"

EMAIL_VERIFY_SETUP=true
EMAIL_FROM="<EMAIL>"
EMAIL_TRANSPORT="smtp"
EMAIL_SMTP_HOST="smtp.sendgrid.net"
EMAIL_SMTP_PORT=465
EMAIL_SMTP_USER="apikey"
#EMAIL_SMTP_PASSWORD #will be set within the container
EMAIL_SMTP_NAME="sendgrid"

#EMAIL_SENDGRID_API_KEY=""

MAX_PAYLOAD_SIZE=2gb

#FLOWS
FLOWS_ENV_ALLOW_LIST="EMAIL_SENDGRID_API_KEY,MB_WEB_KEY,CACHE_CLEAR_URL,CACHE_CLEAR_URL_SERVICE_URL"

CONTENT_SECURITY_POLICY_DIRECTIVES__FRAME_SRC="stage-blasmusik.meinbezirk.at"
CONTENT_SECURITY_POLICY_DIRECTIVES__FRAME_ANCESTORS="https://www.meinbezirk.at,portal05.peiq.dev,https://test.meinbezirk.at,http://localhost:5173"

ASSETS_CONTENT_SECURITY_POLICY="default-src 'self' blob: data:; img-src 'self' blob: data: https://stage-cms.meinbezirk.at https://portal05.meinbezirk.at; media-src 'self' blob: https://stage-cms.meinbezirk.at https://portal05.meinbezirk.at; frame-ancestors 'self' https://stage-cms.meinbezirk.at https://portal05.meinbezirk.at http://localhost:5173;"

#SSO
AUTH_PROVIDERS="peiq"

# AUTH_GOOGLE_DRIVER="openid"
# AUTH_GOOGLE_ISSUER_URL="https://accounts.google.com"
# AUTH_GOOGLE_IDENTIFIER_KEY="email"
# AUTH_GOOGLE_ICON="google"
# AUTH_GOOGLE_LABEL="Google"
# AUTH_GOOGLE_ALLOW_PUBLIC_REGISTRATION="true" # This allows users to be automatically created on logins. Use "false" if you want to create users manually
# AUTH_GOOGLE_DEFAULT_ROLE_ID="f9f1cbfb-78ba-4224-a075-5825ae37f62e" # Replace this with the Directus Role ID you would want for new users. If this is not properly configured, new users will not have access to Directus

AUTH_PEIQ_DRIVER="oauth2"
#AUTH_PEIQ_CLIENT_ID="XXXX" # will be set within the container
#AUTH_PEIQ_CLIENT_SECRET="XXXX" # will be set within the container
AUTH_PEIQ_AUTHORIZE_URL="https://www.meinbezirk.at/oauth/v2/auth"
AUTH_PEIQ_ACCESS_URL="https://www.meinbezirk.at/oauth/v2/token"
AUTH_PEIQ_ALLOW_PUBLIC_REGISTRATION="true" # This allows users to be automatically created on logins. Use "false" if you want to create users manually
AUTH_PEIQ_PROFILE_URL="https://www.meinbezirk.at/api/v2/users/me"
AUTH_PEIQ_DEFAULT_ROLE_ID="3e16bb87-8b6f-49de-bb59-738278c96882" # Peiq user
AUTH_PEIQ_SCOPE=""
AUTH_PEIQ_FIRST_NAME_KEY="first_name"
AUTH_PEIQ_LAST_NAME_KEY="last_name"
AUTH_PEIQ_ICON="interactive_space"
AUTH_PEIQ_LABEL="meinbezirk.at"

REFRESH_TOKEN_SECURE=true
REFRESH_TOKEN_COOKIE_SAME_SITE=none

SESSION_COOKIE_SAME_SITE=none
SESSION_COOKIE_SECURE=true

CORS_ENABLED=true
CORS_ORIGIN="*"

#WEBSOCKETS
WEBSOCKETS_ENABLED=true

# Non Directus Variables
MEINBEZIRK_API_BASE="https://www.meinbezirk.at/api/v2"
SEARCH_CONFIG=rma_search
