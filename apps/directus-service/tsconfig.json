{"extends": "@rma-mono/tsconfig-shared/base.json", "compilerOptions": {"allowJs": true, "checkJs": true, "target": "es2019", "module": "commonjs", "outDir": "dist", "strict": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "typeRoots": ["@types", "node_modules/@types"], "rootDirs": ["./src/", "./test/"], "inlineSourceMap": true, "inlineSources": true}, "include": ["./extensions/**/*"], "exclude": ["node_modules", "types"]}