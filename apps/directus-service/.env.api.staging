#General
PUBLIC_URL="https://stage-cmsapi.meinbezirk.at/"
SERVE_APP=false

#Database
DB_CLIENT="mysql"
DB_PORT="3306"
DB_DATABASE="directus"
DB_POOL__MIN=0 #https://github.com/directus/directus/issues/8634

#Assets
ASSETS_CACHE_TTL="30 days" #this is default
ASSETS_TRANSFORM_MAX_CONCURRENT=10 #default is 25

CACHE_ENABLED=true
CACHE_TTL="5m"
CACHE_AUTO_PURGE=true
CACHE_SYSTEM_TTL="10m"
CACHE_NAMESPACE="directus-cache"
CACHE_STATUS_HEADER="Dir-Cache-Status"
CACHE_CLEAR_URL="https://stage-api.meinbezirk.at/v1/content/cache/clear"
CACHE_CLEAR_URL_SERVICE_URL="https://stage.bezirk.at/clear"

API_GATEWAY_URL="https://stage-api.meinbezirk.at/v1"

CACHE_STORE=redis
REDIS_DB=0
REDIS_TLS__REJECT_UNAUTHORIZED=false

STORAGE_LOCATIONS="gmedia"
STORAGE_GMEDIA_DRIVER="gcs"
STORAGE_GMEDIA_KEY_FILENAME="./gcp-sa-key/directus.json" # will be set within the container
STORAGE_GMEDIA_BUCKET="stage-rma-directus-media"

LOG_LEVEL="trace"
LOG_STYLE="pretty"
LOGGER_LEVELS="trace:DEBUG,debug:DEBUG,info:INFO,warn:WARNING,error:ERROR,fatal:CRITICAL"
LOGGER_MESSAGE_KEY="message"
LOGGER_HTTP_USE_LEVEL="trace"

EMAIL_VERIFY_SETUP=true
EMAIL_FROM="<EMAIL>"
EMAIL_TRANSPORT="smtp"
EMAIL_SMTP_HOST="smtp.sendgrid.net"
EMAIL_SMTP_PORT=465
EMAIL_SMTP_USER="apikey"
#EMAIL_SMTP_PASSWORD #will be set within the container
EMAIL_SMTP_NAME="sendgrid"

MAX_PAYLOAD_SIZE=2gb

#FLOWS
FLOWS_ENV_ALLOW_LIST="EMAIL_SENDGRID_API_KEY,MB_WEB_KEY,CACHE_CLEAR_URL,CACHE_CLEAR_URL_SERVICE_URL"