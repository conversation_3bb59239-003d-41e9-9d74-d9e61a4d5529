KEY="replace-with-random-value"
SECRET="replace-with-random-value"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="d1r3ctu5"
WEBSOCKETS_ENABLED=true
EMAIL_VERIFY_SETUP=true
EMAIL_FROM="<EMAIL>"
EMAIL_TRANSPORT="sendgrid"
# EMAIL_SENDGRID_API_KEY="SET THIS IN YOUR .env FILE"

#MYSQL
DB_CLIENT=mysql
DB_HOST=database
DB_PORT=3306
DB_DATABASE=directus
DB_USER=directus
DB_PASSWORD=directus

MYSQL_ROOT_PASSWORD=secret
MYSQL_USER=${DB_USER}
MYSQL_PASSWORD=${DB_PASSWORD}
MYSQL_DATABASE=${DB_DATABASE}

STACK_PRFX=directus-app

# enables auto reload of extensions (if extension is run in development mode)
EXTENSIONS_AUTO_RELOAD=true

# PEIQ API keys used in the peiq role mapping extension
PEIQ_API_CLIENT_ID=
PEIQ_API_CLIENT_SECRET=

PUBLIC_URL="http://localhost:8055/"
CACHE_ENABLED=false

AUTH_PROVIDERS="peiq"
AUTH_PEIQ_DRIVER="oauth2"

# PEIQ OAuth2 keys
AUTH_PEIQ_CLIENT_ID=
AUTH_PEIQ_CLIENT_SECRET=

AUTH_PEIQ_AUTHORIZE_URL="https://www.meinbezirk.at/oauth/v2/auth"
AUTH_PEIQ_ACCESS_URL="https://www.meinbezirk.at/oauth/v2/token"
AUTH_PEIQ_ALLOW_PUBLIC_REGISTRATION="true" # This allows users to be automatically created on logins. Use "false" if you want to create users manually
AUTH_PEIQ_PROFILE_URL="https://www.meinbezirk.at/api/v2/users/me"
AUTH_PEIQ_DEFAULT_ROLE_ID="3e16bb87-8b6f-49de-bb59-738278c96882" # Peiq user
AUTH_PEIQ_SCOPE=""
AUTH_PEIQ_FIRST_NAME_KEY="first_name"
AUTH_PEIQ_LAST_NAME_KEY="last_name"
AUTH_PEIQ_ICON="interactive_space"
AUTH_PEIQ_LABEL="PEIQ"

MEINBEZIRK_API_BASE="https://www.meinbezirk.at/api/v2"