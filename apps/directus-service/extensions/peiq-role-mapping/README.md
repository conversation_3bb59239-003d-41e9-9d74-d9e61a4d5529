# PEIQ Role Mapping Extension

A Directus extension that manages user roles, policies, and states synchronization between PEIQ and Directus systems.

## Overview

This extension handles the automatic synchronization of user roles and permissions when users authenticate through the PEIQ provider. It ensures that users have the appropriate roles and policies based on their PEIQ group memberships.

## How It Works

### Authentication Flow

1. When a user attempts to log in via PEIQ:

   - The extension first validates if the user has more permissions than just the basic "Benutzer" role
   - If they only have the Benutzer role (ID: 2), login is denied
   - For valid users, the authentication process continues

2. For new users (`users.create`):

   - The extension fetches the user's PEIQ ID using their email
   - Retrieves the user's group IDs and region information from PEIQ
   - Synchronizes roles based on PEIQ group memberships
   - Sets up appropriate policies
   - Updates user states based on their PEIQ region

3. For existing users (`auth.update`):
   - Similar to new users, but updates existing role assignments
   - Removes roles that are no longer valid
   - Updates policies to match current roles
   - Ensures region information is current

### Role Management

The extension manages roles in two ways:

1. Primary Role:

   - Each user gets one primary role assigned directly to their user record
   - This is the first role from their PEIQ group mappings

2. Additional Roles:
   - Any extra roles are managed through a junction table
   - The extension automatically adds/removes role assignments as PEIQ groups change

### Policy Synchronization

Policies are synchronized through multiple steps:

1. Fetches policies from all user roles
2. Checks existing directly assigned policies
3. Verifies policies inherited from the primary role
4. Adds missing policies and removes outdated ones

## Features

- Prevents users with only the "Benutzer" role from logging in
- Synchronizes PEIQ user roles with Directus roles
- Manages user policies based on roles
- Updates user states based on PEIQ region information
- Handles both new user creation and existing user updates

## Prerequisites

The following environment variables must be set:

- `PEIQ_API_CLIENT_ID`
- `PEIQ_API_CLIENT_SECRET`

## Technical Details

### Hook Events

The extension listens to the following events:

1. `auth.create` (filter) - Pre-user creation validation
2. `users.create` (action) - Post-user creation role synchronization
3. `auth.update` (filter) - User update role synchronization

### Main Functions

#### `syncUserRoles(groupIds, userId, ctx)`

- Maps PEIQ group IDs to Directus roles
- Assigns a primary role and additional roles to users
- Manages the junction table for role assignments
- Returns primary role and additional roles for further processing

#### `syncUserPolicies({ userId, primaryRole, ctx })`

- Synchronizes user policies based on roles
- Manages direct policy assignments
- Handles policy inheritance from primary roles
- Removes outdated policies and adds new ones

#### `updateUserStates(userId, state, ctx)`

- Updates user states based on PEIQ region information
- Maintains an array of states, adding new ones without duplicates

## Notes

- Users with only the Benutzer role (ID: 2) are not allowed to log in
- Primary roles are assigned directly to users, while additional roles use a junction table
- Policy inheritance is considered when syncing user permissions (4 levels deep)
- Role synchronization is atomic - either all roles are updated or none
