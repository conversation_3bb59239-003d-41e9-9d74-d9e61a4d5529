{"name": "peiq-role-mapping", "description": "Please enter a description for your extension", "icon": "extension", "version": "1.0.0", "keywords": ["directus", "directus-extension", "directus-extension-hook"], "type": "module", "files": ["dist"], "directus:extension": {"type": "hook", "path": "dist/index.js", "source": "src/index.ts", "host": "^10.10.0"}, "scripts": {"build": "directus-extension build", "dev": "directus-extension build -w --no-minify", "link": "directus-extension link"}, "devDependencies": {"@directus/extensions-sdk": "^12.1.4", "@directus/types": "^12.2.2", "@types/node": "^22.5.0", "typescript": "^5.5.4"}}