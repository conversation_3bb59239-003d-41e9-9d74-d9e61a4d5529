import { defineHook } from '@directus/extensions-sdk'
import type { EventContext, Policy, Prettify, Role } from '@directus/types'
import { getPeiqIdByEmail, getPeiqUserInfo } from './utils/peiq'
import {
  getExistingPeiqUserRoles,
  getPeiqRolesInDirectus,
  getPoliciesForPeiqUserPrimaryRole,
  getPoliciesForPeiqUserRoles,
} from './utils/directus'

type OAuthUserPayload = {
  provider: 'peiq'
  first_name: string
  last_name: string
  email: string
  external_identifier: string
  role: string

  auth_data: string
  // auth_data -> JSON:
  //  {
  //   refresh_token: string
  // }
}

type Location = {
  id: number
  name: string
  name_norm: string
  lat?: number
  lng?: number
}

type Region = {
  id: number
  name: string
  created: string
  locations: Location[]
}

type MetaPayload = {
  event: 'auth.update'
  identifier: string
  provider: 'peiq'
  providerPayload: {
    accessToken: string
    userInfo: {
      email: string
      id: number
      first_name: string
      last_name: string
    }
  }
}

type UsersCreateActionPayload = {
  event: 'users.create'
  payload: OAuthUserPayload
  key: string
  collection: 'directus_users'
}

const PEIQ_BENUTZER_ROLE_ID = 2

export default defineHook(({ filter, action }, { logger, services, getSchema, database }) => {
  logger.info('PEIQ Role Mapping Extension loaded')
  const { UsersService, ItemsService } = services

  if (!process.env.PEIQ_API_CLIENT_ID || !process.env.PEIQ_API_CLIENT_SECRET) {
    throw new Error('Missing PEIQ_API_CLIENT_ID or PEIQ_API_CLIENT_SECRET')
  }

  // filter is running before new user is created
  // this is only to prevent users with just the Benutzer role from logging in
  filter(
    'auth.create',
    async (user: OAuthUserPayload, meta: Record<string, any>, ctx: EventContext) => {
      if (user.provider !== 'peiq') {
        return user
      }

      const { group_ids } = await getPeiqUserInfo(meta.providerPayload.userInfo.id)

      if (group_ids.length === 1 && group_ids[0] === PEIQ_BENUTZER_ROLE_ID) {
        throw new Error(`User ${user.email} with Benutzer role is not allowed to log in`)
      }

      return user
    },
  )

  // this is running after new user is created
  action('users.create', async (meta, ctx) => {
    const { key, payload } = meta as UsersCreateActionPayload

    if (!payload?.provider || payload.provider !== 'peiq') {
      return
    }

    logger.info('User created with PEIQ provider, fetching groups...')

    const peiqId = await getPeiqIdByEmail(payload.email)
    const { group_ids, region } = await getPeiqUserInfo(peiqId)

    const { primaryRole } = await syncUserRoles(group_ids, key, ctx)
    await syncUserPolicies({ userId: key, primaryRole, ctx })

    if (!region) {
      return
    }
    await updateUserStates(key, region, ctx)
  })

  filter(
    'auth.update',
    async (user: OAuthUserPayload, meta: Record<string, any>, ctx: EventContext) => {
      if (meta.provider !== 'peiq') {
        return user
      }

      try {
        const { group_ids, region } = await getPeiqUserInfo(meta.providerPayload.userInfo.id)
        const userService = new UsersService({
          schema: ctx.schema,
          knex: ctx.database,
        })

        const directusUser = await userService.getUserByEmail(meta.providerPayload.userInfo.email)

        if (!directusUser) {
          logger.warn(`User with email ${meta.providerPayload.userInfo.email} not found`)
          return user
        }

        const { primaryRole } = await syncUserRoles(group_ids, directusUser.id, ctx)
        await syncUserPolicies({
          userId: directusUser.id,
          primaryRole,
          ctx,
        })

        if (!region) {
          return user
        }

        await updateUserStates(directusUser.id, region, ctx)
      } catch (error) {
        console.error('Error in handlePeiqAuth', error)
        logger.error('Error in handlePeiqAuth', error)
      }

      return user
    },
  )

  async function updateUserStates(userId: string, state: string, ctx: EventContext) {
    const userService = new UsersService({
      schema: ctx.schema,
      knex: ctx.database,
    })

    const user = await userService.readOne(userId, {
      fields: ['states'],
    })

    if (!user) {
      logger.warn(`User with id ${userId} not found`)
      return
    }

    const currentStates = user.states || []

    if (currentStates.includes(state)) {
      return
    }

    await userService.updateOne(userId, {
      states: [...currentStates, state],
    })
  }

  async function syncUserRoles(groupIds: number[], userId: string, ctx: EventContext) {
    const schema = await getSchema()
    const userService = new UsersService({
      schema: schema,
      knex: database,
    })

    const rolesService = new ItemsService('directus_roles', {
      schema: schema,
      knex: ctx.database,
    })

    const junctionTable = new ItemsService('junction_directus_users_directus_roles', {
      schema: ctx.schema,
      knex: ctx.database,
    })

    // Fetch roles from Directus where peiq_id matches group_ids
    const rolesUserShouldHave = await getPeiqRolesInDirectus({
      groupIds,
      itemsService: rolesService,
    })

    // primary role will be assigned to the directus user "role" property,
    // additional roles will be assigned to our custom junction table  (which will then be shown in the user's roles property)
    const [primaryRole, ...additionalRoles] = rolesUserShouldHave

    if (!primaryRole) {
      throw new Error(`No primary role found for user with group_ids ${groupIds}`)
    }

    const peiqAccessUserHas = await getExistingPeiqUserRoles({
      userId,
      itemsService: junctionTable,
    })

    const rolesToAdd = additionalRoles.filter(
      (role) =>
        !peiqAccessUserHas.some(
          (existingAccess) => existingAccess.directus_roles_id.id === role.id,
        ),
    )

    const accessToDelete = peiqAccessUserHas.filter((existingAccess) => {
      return !additionalRoles.some((role) => role.id === existingAccess.directus_roles_id.id)
    })

    const promises: Promise<any>[] = [
      userService.updateOne(userId, {
        role: primaryRole.id,
      }),
    ]

    if (rolesToAdd.length) {
      promises.push(
        junctionTable.createMany(
          rolesToAdd.map((role) => ({
            directus_users_id: userId,
            directus_roles_id: role.id,
          })),
        ),
      )
    }

    if (accessToDelete.length) {
      promises.push(junctionTable.deleteMany(accessToDelete.map((access) => access.id)))
    }
    await Promise.all(promises)

    return {
      primaryRole,
      additionalRoles,
    }
  }

  async function syncUserPolicies({
    userId,
    primaryRole,
    ctx,
  }: {
    userId: string
    primaryRole: Prettify<Pick<Role, 'id'>>
    ctx: EventContext
  }) {
    const schema = await getSchema()

    const accessTable = new ItemsService('directus_access', {
      schema: schema,
      knex: ctx.database,
    })

    const rolesService = new ItemsService('directus_roles', {
      schema: schema,
      knex: ctx.database,
    })

    const junctionTable = new ItemsService('junction_directus_users_directus_roles', {
      schema: schema,
      knex: ctx.database,
    })

    // get policies that the user should have based on the roles they have
    const policies = await getPoliciesForPeiqUserRoles({
      userId,
      itemsService: junctionTable,
    })

    // get policies that are currently directly assigned to the user
    const currentAccess = (await accessTable.readByQuery({
      filter: {
        user: userId,
      },
    })) as { id: string; policy: string; user: string }[]

    const currentPoliciesViaPrimaryRole = await getPoliciesForPeiqUserPrimaryRole({
      roleId: primaryRole.id,
      itemsService: rolesService,
    })

    const policiesToAdd = policies
      // filter out policies that are already assigned directly to the user
      .filter((policy) => !currentAccess.some((access) => access.policy === policy))
      // filter out policies that are already assigned via the primary role (and it's parents)
      .filter((policy) => !currentPoliciesViaPrimaryRole.some((p) => p === policy))

    const policiesToDelete = currentAccess
      .filter((access) => !policies.some((policy) => policy === access.policy))
      .map((access) => access.id)

    const promises: Promise<any>[] = []

    if (policiesToAdd.length) {
      promises.push(
        accessTable.createMany(policiesToAdd.map((policy) => ({ policy, user: userId }))),
      )
    }

    if (policiesToDelete.length) {
      promises.push(accessTable.deleteMany(policiesToDelete))
    }

    await Promise.all(promises)
  }
})
