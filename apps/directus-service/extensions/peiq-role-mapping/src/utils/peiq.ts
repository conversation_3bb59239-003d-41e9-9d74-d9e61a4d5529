async function getPeiqAccessToken(): Promise<string> {
  const headers = {
    Accept: '*/*',
    'Content-Type': 'application/json',
  }
  const body = JSON.stringify({
    grant_type: 'client_credentials',
    client_id: process.env.PEIQ_API_CLIENT_ID,
    client_secret: process.env.PEIQ_API_CLIENT_SECRET,
  })
  const tokenReq = await fetch('https://www.meinbezirk.at/oauth/v2/token', {
    method: 'POST',
    headers,
    body,
  })
  const data = await tokenReq.json()
  if (!data || !data.access_token) {
    throw new Error('Could not get access token')
  }
  return data.access_token
}
export async function getPeiqIdByEmail(email: string): Promise<number> {
  const accessToken = await getPeiqAccessToken()
  const usersWithEmail = await fetch(
    encodeURI(`https://www.meinbezirk.at/api/v2/users?email=${email}`),
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    },
  )
  const users = await usersWithEmail.json()
  if (!users || !users.data?.length) {
    throw new Error('Could not get user groups')
  }
  return users.data[0].id
}
export async function getPeiqUserInfo(
  userId: number,
): Promise<{ group_ids: number[]; region: string | undefined }> {
  const accessToken = await getPeiqAccessToken()
  const userGroups = await fetch(`https://www.meinbezirk.at/api/v2/users/${userId}`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  })
  const userInfo = await userGroups.json()
  if (!userInfo) {
    throw new Error('Could not get user info')
  }
  const { group_ids, location_id } = userInfo
  if (!group_ids) {
    throw new Error('Could not get user groups')
  }
  if (!location_id) {
    throw new Error('Could not get user location')
  }
  const locations = await fetch(`https://www.meinbezirk.at/api/v2/locationgroups`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  })
  const locationsData = (await locations.json()) as Region[]
  if (!locationsData) {
    throw new Error('Could not get locations')
  }
  const userRegion = locationsData.find(
    (region) =>
      // region with id 33 is "Österreich ganz"
      region.locations.find((location) => location.id === location_id) && region.id !== 33,
  )
  // first location name is the region name, e.g. "Wien" or "Burgenland"
  return { group_ids, region: userRegion?.locations[0]?.name_norm }
}
