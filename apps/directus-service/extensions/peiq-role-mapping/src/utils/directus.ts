import { Prettify, Role } from '@directus/types'
import {
  ExistingRoleQueryResult,
  PolicyWithRole,
  AccessWithPoliciesQueryResult,
  RoleWithPoliciesQueryResult,
  PolicyFromRole,
} from '../types'

// get all roles that are assigned to a user and have a peiq_id
// we ignore all roles that don't have a peiq_id, as they are irrelevant to us and shouldn't be modified
export async function getExistingPeiqUserRoles({
  userId,
  itemsService,
}: {
  userId: string
  itemsService: any
}) {
  const existingRoles = (await itemsService.readByQuery({
    filter: { directus_users_id: userId },
    fields: ['id', 'directus_roles_id', 'directus_roles_id.*', 'directus_roles_id.policies.*'],
  })) as ExistingRoleQueryResult[]

  return existingRoles.filter((role) => role.directus_roles_id.peiq_id !== null)
}

export async function getPoliciesForPeiqUserRoles({
  userId,
  itemsService,
}: {
  userId: string
  itemsService: any
}) {
  const rolesWithPolicies = (await itemsService.readByQuery({
    filter: { directus_users_id: userId },
    fields: [
      'id',
      'directus_roles_id.policies.*',
      'directus_roles_id.name',
      'directus_roles_id.parent.policies.*',
      'directus_roles_id.parent.name',
      'directus_roles_id.parent.parent.policies.*',
      'directus_roles_id.parent.parent.name',
      'directus_roles_id.parent.parent.parent.policies.*',
      'directus_roles_id.parent.parent.parent.name',
    ],
  })) as AccessWithPoliciesQueryResult[]

  // loop over all policies and parents + policies of parents
  const finalPolicies: PolicyWithRole[] = []
  for (const role of rolesWithPolicies) {
    const policies = role.directus_roles_id.policies
    const parents = role.directus_roles_id.parent?.policies ?? []
    const grandParents = role.directus_roles_id.parent?.parent?.policies ?? []
    const greatGrandParents = role.directus_roles_id.parent?.parent?.parent?.policies ?? []

    finalPolicies.push(...policies)
    finalPolicies.push(...parents)
    finalPolicies.push(...grandParents)
    finalPolicies.push(...greatGrandParents)
  }

  return finalPolicies.map((policy) => policy.policy)
}

export async function getPoliciesForPeiqUserPrimaryRole({
  roleId,
  itemsService,
}: {
  roleId: string
  itemsService: any
}) {
  const rolesWithPolicies = (await itemsService.readByQuery({
    filter: { id: roleId },
    fields: [
      'id',
      'policies.*',
      'name',
      'parent.policies.*',
      'parent.name',
      'parent.parent.policies.*',
      'parent.parent.name',
      'parent.parent.parent.policies.*',
      'parent.parent.parent.name',
    ],
  })) as RoleWithPoliciesQueryResult[]

  // loop over all policies and parents + policies of parents
  const finalPolicies: PolicyFromRole[] = []
  for (const role of rolesWithPolicies) {
    const policies = role.policies
    const parents = role.parent?.policies ?? []
    const grandParents = role.parent?.parent?.policies ?? []
    const greatGrandParents = role.parent?.parent?.parent?.policies ?? []

    finalPolicies.push(...policies)
    finalPolicies.push(...parents)
    finalPolicies.push(...grandParents)
    finalPolicies.push(...greatGrandParents)
  }

  return finalPolicies.map((policy) => policy.policy)
}

export async function getPeiqRolesInDirectus({
  groupIds,
  itemsService,
}: {
  groupIds: number[]
  itemsService: any
}) {
  const roles = (await itemsService.readByQuery({
    fields: ['id'],
    filter: { peiq_id: { _in: groupIds } },
  })) as Prettify<Pick<Role, 'id'>>[]

  return roles
}
