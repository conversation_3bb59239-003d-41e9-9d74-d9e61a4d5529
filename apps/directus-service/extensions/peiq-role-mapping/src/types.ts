import { Policy, Role } from '@directus/types'

export type ExistingRoleQueryResult = {
  id: string
  directus_roles_id: Role & {
    policies: Policy[]
    peiq_id: number | null
  }
}

export type PolicyWithRole = {
  id: string
  role: string
  user: null
  policy: string
  sort: number
}

type AccessWithPolicies = {
  name: string
  policies: PolicyWithRole[]
  parent: AccessWithPolicies | null
}

export type AccessWithPoliciesQueryResult = {
  id: number
  directus_roles_id: AccessWithPolicies
}

export type PolicyFromRole = {
  policy: string
}

type RoleWithPolicies = {
  id: string
  policies: PolicyFromRole[]
  name: string
  parent: RoleWithPolicies | null
}

export type RoleWithPoliciesQueryResult = {
  id: string
  policies: PolicyFromRole[]
  name: string
  parent: RoleWithPolicies | null
}
