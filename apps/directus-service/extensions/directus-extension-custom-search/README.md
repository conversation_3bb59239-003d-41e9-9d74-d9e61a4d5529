# Search Configuration for Directus

Provides a way to configure the Directus search filters for a collection. This allows you to supercharge your Directus `search` field with AND/OR groups, strict equality, case-insensitive searches, and nested relational searches, fully under your control.

# Usage

1. Add a new field named `_search_config` in the collection where you want to add search configuration for.

   > **The field must have the key `_search_config`** <br> > **Or you configure a custom name inside as env variable** > `SEARCH_CONFIG=my_search_field_name`

2. Configure your filter in the `Search Config` interface options. Use `$SEARCH` as a placeholder for the user's search query.

Search the collection. Both app and API searches will now use the filter pattern you've specified. You can use nested relational fields for the search.<br>
The search gets **extended and not replaced** if you have already set a filter.

![](./assets/screenshots/search-config.png)
