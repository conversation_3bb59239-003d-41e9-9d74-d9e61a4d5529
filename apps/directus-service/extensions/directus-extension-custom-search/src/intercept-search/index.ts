import { defineHook } from '@directus/extensions-sdk'
import { EventContext } from '@directus/types'

// Recursively run a replace operation on any string object values.
function recursivelyReplaceString(
  value: any,
  // User-defined string replacement function.
  replaceFunc: (string: string) => string,
) {
  if (value == null) return value

  if (Array.isArray(value)) {
    // Iterate through arrays and recursively replace any strings in them.
    for (let i = 0; i < value.length; i++) {
      value[i] = recursivelyReplaceString(value[i], replaceFunc)
    }
  } else if (typeof value === 'object') {
    // Iterate through object and recursively replace any strings in them.
    for (let key in value) {
      value[key] = recursivelyReplaceString(value[key], replaceFunc)
    }
  } else if (typeof value === 'string' && value === '$SEARCH') {
    // Replace strings with user defined function.
    value = replaceFunc(value)
  } else if (typeof value === 'string' && value == '-1') {
    const result = +replaceFunc('$SEARCH')
    if (!isNaN(result)) value = result
  }

  return value
}

// Overrides the search functionality with additional configuration from a _search_config field from a collection.
export default defineHook(({ filter }, { services, env }) => {
  filter(
    'items.query',
    async (
      query: { search?: string; filter: any },
      meta: Record<string, any>,
      context: EventContext,
    ) => {
      if (!query.search) return query
      // Load _search_config field metadata from Directus.
      // Unfortunately, we can't filter by interface, so the field name is hardcoded for all collections.
      const fieldsService = new services.FieldsService({
        schema: context?.schema,
        accountability: { admin: true, roles: [] },
      })

      // Bail out early if we don't find any search configuration information.
      let searchConfig = null
      try {
        const searchConfigString = env.SEARCH_CONFIG || '_search_config'
        searchConfig = (await fieldsService.readOne(meta.collection, searchConfigString))?.meta
          ?.options?.search_config
        if (!searchConfig) return query
      } catch (e) {
        return query
      }

      const searchFilter = recursivelyReplaceString(searchConfig, (entry) =>
        entry.replace('$SEARCH', query.search || ''),
      )

      // Take search out of the query.
      const modifiedQuery = { ...query, search: undefined }
      if (!modifiedQuery.filter) {
        modifiedQuery.filter = searchFilter
      } else {
        modifiedQuery.filter = { _and: [modifiedQuery.filter, searchFilter] }
      }
      return modifiedQuery
    },
  )
})
