import { defineInterface } from '@directus/extensions-sdk'
import CustomSearch from './options.vue'

export default defineInterface({
  id: 'search-configuration',
  name: 'Configure Search',
  icon: 'search',
  description:
    'Override the Directus internal search system with a custom search filter - supports relationships.',
  component: CustomSearch,
  options: ({ collection }) => {
    return [
      {
        field: 'search_config', // this field name is referenced at intercept-search
        name: 'Search Config',
        type: 'json',
        meta: {
          interface: 'system-filter',
          note: '$SEARCH will be replaced by the current search string',
          options: {
            collectionName: collection,
            collectionRequired: true,
          },
        },
      },
    ]
  },
  hideLabel: true,
  hideLoader: true,
  types: ['alias'],
  localTypes: ['presentation'],
  group: 'presentation',
})
