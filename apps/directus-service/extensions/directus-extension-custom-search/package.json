{"name": "directus-extension-custom-search", "description": "Override the Directus internal search system with a custom search filter - supports relationships", "icon": "extension", "version": "0.1.0", "license": "MIT", "contributors": [{"name": "TBD", "url": "TBD"}], "repository": {"type": "git", "url": "TBD"}, "keywords": ["directus", "directus-extension", "directus-extension-bundle"], "type": "module", "files": ["dist"], "directus:extension": {"type": "bundle", "sandbox": {"enabled": true, "requestedScopes": {}}, "path": {"app": "dist/app.js", "api": "dist/api.js"}, "entries": [{"type": "hook", "name": "intercept-search", "source": "src/intercept-search/index.ts"}, {"type": "interface", "name": "search-configuration", "source": "src/search-configuration/index.ts"}], "host": "^10.10.0"}, "scripts": {"build": "directus-extension build", "dev": "directus-extension build -w --no-minify", "link": "directus-extension link", "add": "directus-extension add"}, "devDependencies": {"@directus/extensions-sdk": "13.0.1", "@directus/types": "^13.0.0", "@types/node": "^22.13.4", "typescript": "^5.7.3", "vue": "^3.5.13"}}