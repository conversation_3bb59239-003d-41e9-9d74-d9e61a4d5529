# CSV Export Operation

This Directus custom operation converts an array of objects into a CSV (Comma Separated Values) string. It offers an optional Base64 encoding for the resulting CSV string.
This extension uses the package json-2-csv

## Functionality

The operation takes a JSON payload with the following structure:

```json
{
  "csvData": [ /* Array of objects to be converted */ ],
  "base64": /* Optional boolean to enable Base64 encoding */,
  "fieldOrder": /* Optional array or object to specify field order */,
  "nestedFields": /* Optional array of fields to flatten */,
  "excludeFields": /* Optional array of fields to exclude */
}
```

### Parameters

- `csvData` (required): An array of objects, where each object represents a row in the resulting CSV. The keys of the first object are used as the header row of the CSV.
- `base64` (optional): A boolean flag indicating whether the output should be Base64 encoded. Defaults to false if not provided.
- `fieldOrder` (optional): Can be either:
  - An array of strings: Specifies the order of fields for all nested objects
  - An object: Maps nested field names to their specific field order arrays
- `nestedFields` (optional): Array of field names that should be flattened. Nested objects within these fields will be flattened with their keys appended to the header.
- `excludeFields` (optional): Array of field names that should be excluded from the CSV output.

### Special Handling

- HTML entities in field names are automatically simplified (e.g., `&ouml;` becomes `oe`)
- Field names are truncated to a maximum length of 50 characters
- The field `upload_fileinfo` is ignored
- If a key contains the string "image" and the value is not empty, the string "https://cms.meinbezirk.at/assets/" is prepended to the value

## Example Usage

**Basic Input (without options):**

```json
{
  "csvData": [
    { "name": "John Doe", "age": 30, "address": { "street": "123 Main St", "city": "Anytown" } },
    { "name": "Jane Doe", "age": 25, "address": { "street": "456 Oak Ave", "city": "Anytown" } }
  ],
  "base64": false
}
```

**Input with Field Ordering:**

```json
{
  "csvData": [
    {
      "name": "John Doe",
      "age": 30,
      "address": { "street": "123 Main St", "city": "Anytown" }
    }
  ],
  "nestedFields": ["address"],
  "fieldOrder": {
    "address": ["city", "street"]
  }
}
```

**Input with Multiple Nested Fields:**

```json
{
  "csvData": [
    {
      "id": 1,
      "user_data": {
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "product_data": {
        "id": "P-123",
        "title": "Product 1"
      }
    }
  ],
  "nestedFields": ["user_data", "product_data"],
  "fieldOrder": {
    "user_data": ["name", "email"],
    "product_data": ["id", "title"]
  }
}
```

**Output (without Base64):**

```
"id","name","email","id","title"
"1","John Doe","<EMAIL>","P-123","Product 1"
```

**Output (with Base64 - example, actual output will vary):**

```
Im5hbWUiLCJhZ2UiLCJzdHJlZXQiLCJjaXR5IiIKIkpvaG4gRG9lIiwizDAiLCIxMjMgTWFpbiBTdCIsIkFueXRvd24iCiJKYW5lIERvZSIsIjI1IiwiNDU2IE9hayBBdmUiLCJBbnl0b3duIgo=
```

## Return Value

The operation returns either a CSV string or a Base64 encoded string depending on the `base64` input parameter. If the input `csvData` is empty or null, an empty string is returned.
