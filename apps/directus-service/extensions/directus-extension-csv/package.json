{"name": "directus-operation-csv", "description": "Please enter a description for your extension", "icon": "extension", "version": "1.0.0", "keywords": ["directus", "directus-extension", "directus-extension-operation"], "type": "module", "files": ["dist"], "directus:extension": {"type": "operation", "path": {"app": "dist/app.js", "api": "dist/api.js"}, "source": {"app": "src/app.ts", "api": "src/api.ts"}, "host": "^10.10.0"}, "scripts": {"build": "directus-extension build", "dev": "directus-extension build -w --no-minify", "link": "directus-extension link"}, "devDependencies": {"@directus/extensions-sdk": "13.0.0", "@types/node": "^22.13.1", "typescript": "^5.7.3", "vue": "^3.5.13"}, "dependencies": {"json-2-csv": "^5.5.8"}}