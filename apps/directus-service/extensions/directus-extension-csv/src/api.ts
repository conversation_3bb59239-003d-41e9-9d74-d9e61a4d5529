import { defineOperationApi } from '@directus/extensions-sdk'
import { json2csv } from 'json-2-csv'
import {
  parseConfigArray,
  parseFieldOrder,
  flattenData,
  transformData,
  determineOrderedKeys,
  csvToBase64,
} from './utils'

type Options = {
  csvData: any[]
  base64: boolean
  fieldOrder?: string[] | Record<string, string[]>
  nestedFields?: string[]
  excludeFields?: string[]
}

export default defineOperationApi<Options>({
  id: 'rma-operation-csv',
  handler: (
    { csvData, base64, fieldOrder = [], nestedFields = [], excludeFields = [] },
    { env },
  ) => {
    // Early return for empty data
    if (!csvData || csvData.length === 0) {
      return base64 ? '' : ''
    }

    // 1. Parse configuration values
    const parsedNestedFields = parseConfigArray(nestedFields)
    const parsedExcludeFields = parseConfigArray(excludeFields)
    const parsedFieldOrder = parseFieldOrder(fieldOrder, parsedNestedFields)

    // 2. Transform nested fields first
    const transformedData = transformData(csvData, parsedNestedFields, env.PUBLIC_URL)

    // 3. Flatten the data structure
    const flattenedData = flattenData(transformedData, parsedNestedFields, parsedExcludeFields)
    // 4. Determine ordered keys for CSV
    const orderedKeys = determineOrderedKeys(flattenedData, parsedFieldOrder)

    // 5. Generate CSV with ordered keys
    const csvString = json2csv(flattenedData, {
      excelBOM: true,
      trimHeaderFields: true,
      keys: orderedKeys,
    })

    // 5. Return as-is or convert to base64
    if (!base64) {
      return csvString
    }

    return csvToBase64(csvString)
  },
})
