import { defineOperationApp } from '@directus/extensions-sdk'

export default defineOperationApp({
  id: 'rma-operation-csv',
  name: 'Object to CSV',
  icon: 'description',
  description: 'Converts the given Array of object to a csv string',
  overview: ({ text }) => [
    {
      label: 'Converts objects to CSV',
      text: text,
    },
  ],
  options: [
    {
      field: 'csvData',
      name: 'CSV Daten',
      type: 'json',
      meta: {
        width: 'full',
        interface: 'input',
      },
    },
    {
      field: 'base64',
      name: 'Base64 Mode',
      type: 'boolean',
      meta: {
        width: 'full',
        note: 'When enabled, the CSV will be returned as Base64, e.g. for sending via email.',
      },
      schema: {
        default_value: false,
      },
    },
    {
      field: 'nestedFields',
      name: 'Nested Fields',
      type: 'json',
      meta: {
        width: 'full',
        interface: 'input',
        note: 'List of field names that contain nested data to be flattened in the CSV export (e.g. ["user_data"])',
      },
      schema: {
        default_value: null,
      },
    },
    {
      field: 'fieldOrder',
      name: 'Field Order',
      type: 'json',
      meta: {
        width: 'full',
        interface: 'input',
        note: 'Define order of nested fields in the CSV export. Can be an array for all nested fields, or an object mapping each nested field to its specific order. Example: {"user_data": ["name","email"], "product_data": ["id","price"]}',
      },
      schema: {
        default_value: null,
      },
    },
    {
      field: 'excludeFields',
      name: 'Exclude Fields',
      type: 'json',
      meta: {
        width: 'full',
        interface: 'input',
        note: 'List of field names to exclude from the CSV export',
      },
      schema: {
        default_value: null,
      },
    },
  ],
})
