/**
 * CSV Export Operation Utility Functions
 */

// Maximum length for keys - based on observed behavior
export const MAX_KEY_LENGTH = 50

/**
 * Simplifies keys by removing HTML entities and truncating to maximum length
 * This ensures compatibility with CSV formats and improves readability
 */
export function simplifyKey(key: string): string {
  // First replace all HTML entities
  let simplified = key
    .replace(/,/g, '')
    .replace(/&amp;/g, '')
    .replace(/&lt;/g, '')
    .replace(/&gt;/g, '')
    .replace(/&quot;|&ldquo;|&rdquo;|&bdquo;/g, '')
    .replace(/&apos;|&lsquo;|&rsquo;/g, '')
    .replace(/&ouml;/g, 'oe')
    .replace(/&auml;/g, 'ae')
    .replace(/&uuml;/g, 'ue')
    .replace(/&szlig;/g, 'ss')
    .replace(/&nbsp;/g, '')
    .replace(/&copy;/g, '')
    .replace(/&reg;/g, '')
    .replace(/&ndash;|&mdash;/g, '-')
    .replace(/&#\d+;/g, '')
    .replace(/&[a-zA-Z0-9]+;/g, '')

  if (simplified.length > MAX_KEY_LENGTH) {
    simplified = simplified.substring(0, MAX_KEY_LENGTH)
  }

  return simplified
}

/**
 * Parse configuration values that could be either strings or arrays
 * @param value - The configuration value to parse
 * @returns Parsed array of strings
 */
export function parseConfigArray(value: string[] | string | undefined): string[] {
  if (Array.isArray(value)) {
    return value
  } else if (typeof value === 'string') {
    try {
      return JSON.parse(value)
    } catch (e) {
      return []
    }
  }
  return []
}

/**
 * Parse and normalize field order configuration
 * @param fieldOrder - The field order configuration
 * @param nestedFields - List of nested fields
 * @returns Map of field names to their ordering arrays
 */
export function parseFieldOrder(
  fieldOrder: string[] | Record<string, string[]> | string | undefined,
  nestedFields: string[],
): Record<string, string[]> {
  let parsedFieldOrder: Record<string, string[]> = {}

  if (typeof fieldOrder === 'string') {
    try {
      const parsed = JSON.parse(fieldOrder)
      if (Array.isArray(parsed)) {
        // If it's an array, apply to all nested fields
        nestedFields.forEach((field) => {
          parsedFieldOrder[field] = parsed
        })
      } else if (typeof parsed === 'object') {
        // If it's an object, it maps nested fields to their orders
        parsedFieldOrder = parsed
      }
    } catch (e) {
      console.error('Error parsing fieldOrder:', e)
    }
  } else if (Array.isArray(fieldOrder)) {
    // If directly provided as array, apply to all nested fields
    nestedFields.forEach((field) => {
      parsedFieldOrder[field] = fieldOrder
    })
  } else if (fieldOrder && typeof fieldOrder === 'object') {
    // If directly provided as object, use it directly
    parsedFieldOrder = fieldOrder as Record<string, string[]>
  }

  return parsedFieldOrder
}

/**
 * Flatten the data for CSV export
 * @param csvData - Original data array
 * @param nestedFields - Fields to be flattened
 * @param excludeFields - Fields to exclude
 * @returns Flattened data array
 */
export function flattenData(
  csvData: any[],
  nestedFields: string[],
  excludeFields: string[],
): Record<string, any>[] {
  return csvData.map((item) => {
    const flattened: Record<string, any> = {}

    Object.keys(item)
      .filter((key) => !excludeFields.includes(key))
      .forEach((key) => {
        if (nestedFields.includes(key) && typeof item[key] === 'object') {
          Object.entries(item[key] || {}).forEach(([nestedKey, value]) => {
            flattened[`${key}.${simplifyKey(nestedKey)}`] = value
          })
        } else if (!excludeFields.includes(key)) {
          flattened[key] = item[key]
        }
      })

    return flattened
  })
}

/**
 *  Process a single nested field and return transformed value if applicable
 *  Builds urls for assets
 *  Can be extended for future transformations
 *
 * @param field - field name to process from the nested fields list
 * @param item - The data item containing the field
 * @returns The processed field with added URL if it's an asset, undefined otherwise
 */
const processNestedField = (
  field: string,
  item: Record<string, any>,
  baseUrl: string,
): string | undefined => {
  if (!(field in item)) {
    return undefined
  }

  const nestedItem = item[field]
  if (!nestedItem || typeof nestedItem !== 'object') {
    return undefined
  }

  if (!nestedItem.filename_disk || !nestedItem.id) {
    return undefined
  }

  return `${baseUrl}assets/${nestedItem.id}`
}

/**
 * transformes incoming directus data
 *
 * currently only joins urls for assets
 * additional transformations should be added in processNestedField
 *
 * @param data - Data array to transform
 * @param nestedFields - List of nested fields to process
 * @returns Transformed data array
 */
export function transformData(
  data: Record<string, any>[],
  nestedFields: string[] = [],
  baseUrl: string = '',
): Record<string, any>[] {
  return data.map((item) => {
    const transformed = { ...item }

    nestedFields.forEach((field) => {
      const processedField = processNestedField(field, item, baseUrl)
      if (!processedField) {
        return
      }

      transformed[field] = processedField
    })
    return transformed
  })
}

/**
 * Determine the ordered keys for CSV output
 * @param flattenedData - Flattened data array
 * @param fieldOrder - Field order configuration
 * @returns Array of keys in the desired order
 */
export function determineOrderedKeys(
  flattenedData: Record<string, any>[],
  fieldOrder: Record<string, string[]>,
): string[] {
  let orderedKeys: string[] = []

  // 1. Start with base fields (excluding nested and excluded)
  const baseItem = flattenedData[0] || {}
  const baseFields = Object.keys(baseItem).filter((key) => !key.includes('.'))
  orderedKeys.push(...baseFields)

  // 2. Find all unique nested field prefixes
  const nestedPrefixes = new Set<string>()
  flattenedData.forEach((item) => {
    Object.keys(item)
      .filter((key) => key.includes('.'))
      .forEach((key) => {
        const prefix = key.split('.')[0]
        if (prefix) {
          nestedPrefixes.add(prefix)
        }
      })
  })

  // 3. Process nested fields and apply specific orders
  nestedPrefixes.forEach((prefix) => {
    const prefixDot = `${prefix}.`
    const specificOrder = fieldOrder[prefix] || []

    // Add fields in the specified order
    if (specificOrder.length > 0) {
      specificOrder.forEach((field: string) => {
        const safeField = field || ''
        const fullKey = `${prefixDot}${simplifyKey(safeField)}`
        if (flattenedData.some((item) => fullKey in item)) {
          orderedKeys.push(fullKey)
        }
      })
    }

    // Add any remaining nested fields not in the specific order
    flattenedData.forEach((item) => {
      Object.keys(item)
        .filter((key) => key.startsWith(prefixDot) && !orderedKeys.includes(key))
        .forEach((key) => {
          if (!orderedKeys.includes(key)) {
            orderedKeys.push(key)
          }
        })
    })
  })

  return orderedKeys
}

/**
 * Convert CSV string to base64
 * @param csvString - The CSV string to encode
 * @returns Base64 encoded string
 */
export function csvToBase64(csvString: string): string {
  const encoder = new TextEncoder()
  const utf8Bytes = encoder.encode(csvString)

  // Convert the Uint8Array to a Base64-encoded string
  // IMPORTANT DO NOT USE SINGLE LINE CODE LIKE String.fromCharCode(...utf8Bytes)
  // this results in a maximum call stack exceeded error
  let s = ''
  for (let i = 0; i < utf8Bytes.length; i++) {
    s += String.fromCharCode(utf8Bytes[i] as number)
  }
  return btoa(s)
}
