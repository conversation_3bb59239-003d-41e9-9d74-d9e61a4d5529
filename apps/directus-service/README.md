# Directus Service

This service provides a headless CMS and application framework based on Directus for the RMA monorepo.

## Overview

The directus-service is a containerized Directus instance that serves as the content management system and application framework for various applications within the RMA monorepo. It provides a flexible API for content management, user authentication, and data storage.

## Environment Configuration

The service supports multiple environments with dedicated configuration files:

- `.env` - Local development environment
- `.env.example` - Template for required environment variables
- `.env.staging` - Staging environment configuration
- `.env.production` - Production environment configuration
- `.env.api.staging` - API-specific staging configuration
- `.env.api.production` - API-specific production configuration

## Extensions

The service includes the following custom Directus extensions:

### directus-extension-csv

A custom extension that provides CSV import/export functionality for Directus collections. This extension enhances the data management capabilities by allowing bulk operations through CSV files.

See [directus-extension-csv/README.md](./extensions/directus-extension-csv/README.md) for more information.

### peiq-role-mapping

A specialized extension that handles role mapping for PEIQ oAuth integration. This extension manages user permissions and access control based on external role definitions.

See [peiq-role-mapping/README.md](./extensions/peiq-role-mapping/README.md) for more information.

## Deployment

### Docker

The service is containerized using Docker with environment-specific Dockerfiles:

- `Dockerfile-local` - For local development
- `Dockerfile-stage` - For staging environment
- `Dockerfile-prod` - For production environment

## Local Development

To run the service locally:

run the npm build command to install and build all extensions for directus-service

```bash
npm build:extensions
```

```bash
docker-compose up -d

# With new config
docker-compose up -d --build
```

This will start the Directus service along with its dependencies as defined in the `docker-compose.yml` file.

To shut down the service:

```bash
docker-compose down
# Also remove the data/volumes
docker-compose down -v && rm -rf data
```

## Directus Sync

The monorepo uses [directus-sync](https://github.com/tractr/directus-sync) for syncing data schema, and configurations between environments (local, staging, and production).

For detailed instructions on syncing data between environments, see the [directus-config README](../../misc/directus-config/README.md).

## Google Cloud Integration

The service is configured to run on Google Cloud Platform:

- Deployed as a containerized service on Google Cloud Run
- Uses Google Cloud Storage for asset storage
- Configured with service account credentials (`gcp-sa-key.json`)

## CI/CD

The service is deployed using Bitbucket Pipelines with environment-specific configurations.

## Related Services

This Directus instance serves as a backend for various frontend applications within the RMA monorepo.
