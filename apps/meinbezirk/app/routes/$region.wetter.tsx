import { json } from '@remix-run/node'
import { type MetaFunction, type LinksFunction, type LoaderFunctionArgs } from '@remix-run/node'
import { useLoaderData } from '@remix-run/react'
import leafletStylesheetRef from 'leaflet/dist/leaflet.css?url'
import { cacheHeader } from 'pretty-cache-header'
import invariant from 'tiny-invariant'
import { throwIfRegionDoesNotExist } from 'services/regions/utils'
import { weatherLoader } from 'services/weather/weatherLoader'
import { Layout } from 'ui/components/layout'
import { WeatherPage } from 'ui/features/weather/weatherPage'
import { expires } from 'utils/expires'
import { generateOGTags, generateSchema } from 'utils/generateSchema'
import { keywordsForPage } from 'utils/keywordsForPage'
import { getOGImgInfo } from 'utils/ogImageUtils'

export function headers() {
  return {
    'Cache-Control': cacheHeader({
      public: true,
      mustRevalidate: true,
      proxyRevalidate: true,
    }),
    Expires: expires({ hours: 1 }),
    'Last-Modified': new Date().toUTCString(),
  }
}

export const links: LinksFunction = () => [
  {
    rel: 'stylesheet',
    href: leafletStylesheetRef,
  },
]

export async function loader({ request, params }: LoaderFunctionArgs) {
  invariant(params.region, 'Region fehlt')
  throwIfRegionDoesNotExist({ region: params.region, statusText: 'Region nicht gefunden' })

  const data = await weatherLoader({ region: params.region })

  if (!data) {
    throw json('Es ist ein Fehler aufgetreten', {
      status: 500,
    })
  }

  const ogImgInfo = getOGImgInfo({
    request,
    type: 'wetter',
    additionalParams: { title: data.regionInfo?.title ?? 'Österreich' },
  })

  return json(
    { ...data, ogImgInfo },
    {
      headers: headers(),
    },
  )
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  const location = data?.isDistrict ? data?.regionInfo?.title : ''
  const state = data?.isDistrict ? data?.regionForBounds?.title : data?.regionInfo?.title
  const slug = data?.regionInfo?.slug
  const url = `https://meinbezirk.at/${slug}/wetter/`
  const canonicalUrl = `https://www.meinbezirk.at/${slug}/wetter/`
  const title = `Wettervorhersage für ${location ? location : state} auf meinbezirk.at`

  const schema = generateSchema({
    url,
    headline: title,
    ogImgInfo: data?.ogImgInfo,
  })

  const description = `Wetterinformationen ${
    data?.isDistrict ? 'für den Bezirk ' + location : 'für das Bundesland ' + state
  }. Wetter ${location ? location + ' -' : ''} ${state} - Österreich.`

  const ogTags = generateOGTags({
    title,
    description,
    url,
    ogImgInfo: data?.ogImgInfo,
  })

  return [
    { title },
    {
      name: 'description',
      content: description,
    },
    {
      name: 'keywords',
      content: keywordsForPage({ type: 'weatherRegion', region: location, state }),
    },
    {
      'script:ld+json': schema,
    },
    {
      tagName: 'link',
      rel: 'canonical',
      href: canonicalUrl,
    },
    ...ogTags,
  ]
}

export default function RegionWeather() {
  const data = useLoaderData<typeof loader>()

  return (
    <Layout showSidebar mostReadArticles={data.mostReadArticles}>
      <WeatherPage
        environment={data.environment}
        isDistrict={data.isDistrict}
        regionInfo={data.regionInfo}
        weatherInfo={data.weatherInfo}
        weatherInfoParent={data.weatherInfoParent}
        regionBounds={data.bounds}
        regionForBounds={data.regionForBounds}
        weatherSponsoring={data.weatherSponsoring}
      />
    </Layout>
  )
}
