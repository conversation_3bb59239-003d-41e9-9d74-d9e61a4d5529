import { regionAustria } from '@rma-mono/rma-regions'
import type { MetaFunction } from '@remix-run/node'
import { legalPageHeaders, legalPageMeta, LegalPageRoute } from 'ui/features/staticPages/legalPage'
import { imprintLoader } from 'ui/features/staticPages/legalPage/legalPageLoader'

export { legalPageHeaders as headers }

export const meta: MetaFunction<typeof loader> = ({ data }) =>
  legalPageMeta({ title: data?.title, teaser: data?.teaser })

export const loader = () =>
  imprintLoader({
    slug: 'imprint',
    region: regionAustria,
  })

export default LegalPageRoute
