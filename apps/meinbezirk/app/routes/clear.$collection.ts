import { json } from '@remix-run/node'
import { logger } from '../utils/logger.server'
import type { LoaderFunction } from '@remix-run/node'

/**
 * API endpoint for clearing all cache entries for a specific collection
 * This follows the API pattern from the OpenAPI specification
 */
export const loader: LoaderFunction = async ({ params, request }) => {
  try {
    const { collection } = params

    // Validate parameters
    if (!collection) {
      logger.error('Missing collection parameter for cache clearing')
      return json({ error: 'Missing required parameter: collection' }, { status: 400 })
    }

    // Clear all cache for the specified collection
    //await CacheService.clearAllCollectionCache(collection)

    logger.info(`All cache entries cleared for collection ${collection}`)
    return json({
      message: `All cache entries cleared for collection ${collection}`,
      success: true,
    })
  } catch (error) {
    logger.error('Error clearing collection cache:', error)
    return json(
      {
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false,
      },
      { status: 500 },
    )
  }
}

/**
 * Should not support other HTTP methods
 */
export const action = () => {
  return json({ error: 'Method not allowed' }, { status: 405 })
}
