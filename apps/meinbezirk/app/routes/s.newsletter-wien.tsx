import type { MetaFunction } from '@remix-run/node'
import {
  newsletterHeaders,
  newsletterMeta,
  NewsletterRoute,
} from 'ui/features/staticPages/newsletter'
import { newsletterLoader } from 'ui/features/staticPages/newsletter/newsletterLoader'

export { newsletterHeaders as headers }

export const meta: MetaFunction<typeof loader> = () =>
  newsletterMeta({
    region: 'wien',
  })

export const loader = () => newsletterLoader({ region: 'wien' })

export default NewsletterRoute
