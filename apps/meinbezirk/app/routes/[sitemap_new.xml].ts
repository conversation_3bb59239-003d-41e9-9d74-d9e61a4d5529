import { allRegions } from 'services/regions'

const buildWeatherSitemap = async () => {
  const regionsInHierachy = (await allRegions()).getFlatRegionSlugs()

  const baseUrl = 'https://www.meinbezirk.at'

  return regionsInHierachy
    .map((slug) =>
      `
    <url>
      <loc>${baseUrl}${slug ? `/${slug}` : ''}/wetter</loc>
      <changefreq>hourly</changefreq>
    </url>
  `.trim(),
    )
    .join('')
}

export const headers = {
  'Content-Type': 'application/xml',
  'Cache-Control': 'public, max-age=3600, s-maxage=3600',
}

export const loader = async () => {
  const xml = `
    <?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${await buildWeatherSitemap()}
    </urlset>
    `.trim()

  return new Response(xml, { headers })
}
