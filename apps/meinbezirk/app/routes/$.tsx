import { css } from 'panda/css'
import { Layout } from 'ui/components/layout'
import { NotFoundErrorPage } from 'ui/features/errorPages/notFoundErrorPage'

export default function NoMatchingRouteErrorBoundary() {
  return (
    <Layout variant="boxed">
      <section
        className={css({
          width: '100%',
        })}
        aria-label="Fehlermeldung"
      >
        <NotFoundErrorPage />
      </section>
    </Layout>
  )
}
