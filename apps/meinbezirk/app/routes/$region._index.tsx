import { json } from '@remix-run/node'
import { cacheHeader } from 'pretty-cache-header'
import invariant from 'tiny-invariant'
import type { LoaderFunctionArgs } from '@remix-run/node'
import { throwIfRegionDoesNotExist } from 'services/regions/utils'
import { InternalDashboard } from 'ui/features/internal/internal-dashboard'

export function headers() {
  return {
    'Cache-Control': cacheHeader({
      public: true,
      maxAge: '2hours',
      sMaxage: '12hours',
      staleWhileRevalidate: '1day',
    }),
  }
}

export const loader = async ({ params }: LoaderFunctionArgs) => {
  invariant(params.region, 'Region fehlt')
  throwIfRegionDoesNotExist({ region: params.region, statusText: 'Region nicht gefunden' })

  return json({})
}

export default function RegionPage() {
  return <InternalDashboard />
}
