import type { MetaFunction } from '@remix-run/node'
import { ePaperHeaders, ePaperMeta, EPaperRoute } from 'ui/features/staticPages/ePaper'
import { regionEPaperLoader } from 'ui/features/staticPages/ePaper/epaperLoader'

export { ePaperHeaders as headers }

export const meta: MetaFunction<typeof loader> = ({ data }) =>
  ePaperMeta({
    location: data?.currentRegion?.title,
    slug: data?.currentRegion?.slug,
  })

export const loader = () => regionEPaperLoader({ region: 'salzburg' })

export default EPaperRoute
