import {
  contactPageLinks,
  ContactRoutePage,
  contactHeaders,
  contactMeta,
} from '../ui/features/staticPages/contact/index'
import type { MetaFunction } from '@remix-run/node'
import { contactLoader } from 'ui/features/staticPages/contact/contactLoader'

export const loader = () => contactLoader('kaernten')
export const meta: MetaFunction<typeof loader> = ({ data }) =>
  contactMeta({ region: 'kaernten', title: data?.contactPage.title })

export { contactPageLinks as links }

export { contactHeaders as headers }

export default ContactRoutePage
