import { json, type LoaderFunctionArgs } from '@remix-run/node'
import { cacheHeader } from 'pretty-cache-header'
import { z } from 'zod'
import type { OGImageFormat } from 'utils/ogImageUtils'
import { createOGImage } from 'utils/createOGImage.server'
import { logger } from 'utils/logger.server'
import { formatSchema } from 'utils/ogImageUtils'

const titleSchema = z.string().min(1, 'Title is required and cannot be empty.')

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const formatResult = formatSchema.safeParse(params.format)

  if (!formatResult.success) {
    return json({ error: formatResult.error.message }, { status: 400 })
  }

  const { searchParams } = new URL(request.url)
  const titleResult = titleSchema.safeParse(searchParams.get('title'))

  if (!titleResult.success) {
    return json({ error: titleResult.error.message }, { status: 400 })
  }

  const title = titleResult.data
  const format = formatResult.data

  try {
    const ogImage = await createOGImage({
      title,
      format: format as OGImageFormat,
      baseURL: process.env.REMIX_DIRECT_URL!,
      type: 'weather',
    })

    return new Response(ogImage, {
      status: 200,
      headers: {
        'Content-Type': 'image/jpeg',
        'Cache-Control': cacheHeader({
          public: true,
          maxAge: '1month',
          sMaxage: '1month',
          staleWhileRevalidate: '1day',
        }),
      },
    })
  } catch (error) {
    logger.error('Error creating og image', error)
    return json({ error: 'Error creating og image' }, { status: 500 })
  }
}
