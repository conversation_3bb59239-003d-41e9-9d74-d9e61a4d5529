import { json } from '@remix-run/node'
import { logger } from '../utils/logger.server'
import type { LoaderFunction } from '@remix-run/node'

/**
 * API endpoint for clearing all caches
 */
export const loader: LoaderFunction = async ({ request }) => {
  try {
    // Clear all caches
    /*const results = await CacheService.clearAllCaches()

    // Calculate success/failure status
    const allSuccessful = Object.values(results).every((result) => result === true)

    logger.info('Cache clearing completed', { results, allSuccessful })
    return json(
      {
        message: 'Cache clearing completed',
        results,
        success: allSuccessful,
      },
      { status: allSuccessful ? 200 : 207 },
    ) // 207 Multi-Status if some failed
     */
  } catch (error) {
    logger.error('Error clearing all caches:', error)
    return json(
      {
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false,
      },
      { status: 500 },
    )
  }
}

/**
 * Should not support other HTTP methods
 */
export const action = () => {
  return json({ error: 'Method not allowed' }, { status: 405 })
}
