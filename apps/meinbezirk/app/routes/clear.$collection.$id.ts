import { json } from '@remix-run/node'
import { logger } from '../utils/logger.server'
import type { LoaderFunction } from '@remix-run/node'

/**
 * API endpoint for clearing the cache for a specific collection item
 */
export const loader: LoaderFunction = async ({ params, request }) => {
  try {
    const { collection, id } = params

    // Validate parameters
    if (!collection || !id) {
      logger.error('Missing parameters for cache clearing', { collection, id })
      return json({ error: 'Missing required parameters: collection and/or id' }, { status: 400 })
    }

    //await CacheService.clearCollectionItemCache(collection, id)

    logger.info(`Cache cleared for collection ${collection} with ID ${id}`)
    return json({
      message: `Cache cleared for collection ${collection} with ID ${id}`,
      success: true,
    })
  } catch (error) {
    logger.error('Error clearing cache:', error)
    return json(
      {
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false,
      },
      { status: 500 },
    )
  }
}

/**
 * Should not support other HTTP methods
 */
export const action = () => {
  return json({ error: 'Method not allowed' }, { status: 405 })
}
