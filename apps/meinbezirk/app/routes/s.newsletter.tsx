import { regionAustria } from '@rma-mono/rma-regions'
import type { MetaFunction } from '@remix-run/node'
import {
  newsletterHeaders,
  newsletterMeta,
  NewsletterRoute,
} from 'ui/features/staticPages/newsletter'
import { newsletterLoader } from 'ui/features/staticPages/newsletter/newsletterLoader'

export { newsletterHeaders as headers }

export const meta: MetaFunction<typeof loader> = ({ params }) =>
  newsletterMeta({
    region: params.region,
  })

export const loader = () => newsletterLoader({ region: regionAustria })

export default NewsletterRoute
