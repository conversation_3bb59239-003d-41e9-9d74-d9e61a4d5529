import type { MetaFunction } from '@remix-run/node'
import {
  ContactRoutePage,
  contactHeaders,
  contactMeta,
  contactPageLinks,
} from 'ui/features/staticPages/contact'
import { contactLoader } from 'ui/features/staticPages/contact/contactLoader'

export const loader = () => contactLoader('niederoesterreich')
export const meta: MetaFunction<typeof loader> = ({ data }) =>
  contactMeta({ region: 'niederoesterreich', title: data?.contactPage.title })

export { contactHeaders as headers }

export { contactPageLinks as links }

export default ContactRoutePage
