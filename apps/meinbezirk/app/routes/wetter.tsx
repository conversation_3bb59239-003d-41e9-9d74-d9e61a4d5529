import { json } from '@remix-run/node'
import { useLoaderData } from '@remix-run/react'
import { regionAustria } from '@rma-mono/rma-regions'
import leafletStylesheetRef from 'leaflet/dist/leaflet.css?url'
import { cacheHeader } from 'pretty-cache-header'
import type { LoaderFunctionArgs, MetaFunction, LinksFunction } from '@remix-run/node'
import { weatherLoader } from 'services/weather/weatherLoader'
import { Layout } from 'ui/components/layout'
import { WeatherPage } from 'ui/features/weather/weatherPage'
import { expires } from 'utils/expires'
import { generateOGTags, generateSchema } from 'utils/generateSchema'
import { keywordsForPage } from 'utils/keywordsForPage'
import { getOGImgInfo } from 'utils/ogImageUtils'

const title = 'Wettervorhersage für ganz Österreich auf meinbezirk.at'
const description =
  'Wetterinformationen für alle Bundesländer und Bezirke in Österreich. Wetter - Österreich.'
const url = 'https://meinbezirk.at/wetter'
const canonicalUrl = 'https://www.meinbezirk.at/wetter'

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  const schema = generateSchema({
    url,
    headline: title,
    description,
    ogImgInfo: data?.ogImgInfo,
  })

  const ogTags = generateOGTags({
    title,
    description,
    url,
    ogImgInfo: data?.ogImgInfo,
  })

  return [
    { title },
    {
      name: 'description',
      content: description,
    },
    {
      name: 'keywords',
      content: keywordsForPage({ type: 'weather' }),
    },
    {
      'script:ld+json': schema,
    },
    {
      tagName: 'link',
      rel: 'canonical',
      href: canonicalUrl,
    },
    ...ogTags,
  ]
}

export function headers() {
  return {
    Expires: expires({ hours: 1 }),
    'Last-Modified': new Date().toUTCString(),
    'Cache-Control': cacheHeader({
      public: true,
      mustRevalidate: true,
      proxyRevalidate: true,
    }),
  }
}

export const links: LinksFunction = () => [
  {
    rel: 'stylesheet',
    href: leafletStylesheetRef,
  },
]

export async function loader({ request }: LoaderFunctionArgs) {
  const data = await weatherLoader({
    region: regionAustria,
  })

  if (!data) {
    throw json('Es ist ein Fehler aufgetreten', {
      status: 500,
    })
  }

  const ogImgInfo = getOGImgInfo({
    request,
    type: 'wetter',
    additionalParams: { title: data.regionInfo?.title ?? 'Österreich' },
  })

  return json(
    { ...data, ogImgInfo },
    {
      headers: headers(),
    },
  )
}

export default function Weather() {
  const data = useLoaderData<typeof loader>()

  return (
    <Layout showSidebar mostReadArticles={data.mostReadArticles}>
      <WeatherPage
        isDistrict={data.isDistrict}
        regionInfo={data.regionInfo}
        weatherInfo={data.weatherInfo}
        weatherInfoParent={data.weatherInfoParent}
        regionBounds={data.bounds}
        regionForBounds={data.regionForBounds}
        environment={data.environment}
        weatherSponsoring={data.weatherSponsoring}
      />
    </Layout>
  )
}
