import { regionAustria } from '@rma-mono/rma-regions'
import { z } from 'zod'
import type { ActionFunctionArgs } from '@remix-run/node'
import type { AllRegions } from 'services/regions'
import { allRegions } from 'services/regions'
import { getMostReadArticles } from 'services/search'
import { buildWeatherQuery } from 'services/weather/weatherUtil'

const validIntervals = z.enum(['1h', '12h'])
const DELAY_MS = 300

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method !== 'POST') {
    return new Response(null, {
      status: 405,
      statusText: 'Method Not Allowed',
    })
  }

  const body = await request.json()

  const { secret } = z
    .object({
      secret: z.string(),
    })
    .parse(body)

  if (process.env.ENVIRONMENT !== 'development' && secret !== process.env.PREFETCH_SECRET) {
    return new Response(null, {
      status: 401,
      statusText: 'Unauthorized',
    })
  }

  const { searchParams } = new URL(request.url)
  const interval = validIntervals.safeParse(searchParams.get('interval'))
  if (!interval.success) {
    return new Response(null, {
      status: 400,
      statusText: 'Bad Request',
    })
  }

  const regions = await allRegions()

  if (interval.data === '1h') {
    const mostReadArticles = await fetchMostReadArticles({ regions })
    return new Response(JSON.stringify(mostReadArticles), {
      status: 200,
      statusText: 'OK',
    })
  }

  const results = await prefetchWeather({ regions })
  return new Response(JSON.stringify(results), {
    status: 200,
    statusText: 'OK',
  })
}

const prefetchWeather = async ({ regions }: { regions: AllRegions }) => {
  const regionSlugs = regions.getRegionSlugsInHierachy()

  // Fetch top level weather
  let responses: Awaited<ReturnType<typeof Promise.allSettled>> = await Promise.allSettled([
    fetchWeather({ region: regionAustria, regions }),
  ])

  // Fetch weather for all states
  responses = responses.concat(
    await Promise.allSettled(
      regionSlugs.states.map((slug, idx) =>
        executeWithDelay(() => fetchWeather({ region: slug!, regions }), idx * DELAY_MS),
      ),
    ),
  )

  // Fetch weather for all districts
  responses = responses.concat(
    await Promise.allSettled(
      regionSlugs.districts.map((slug, idx) =>
        executeWithDelay(() => fetchWeather({ region: slug!, regions }), idx * DELAY_MS),
      ),
    ),
  )

  const results = { rejected: 0, fulfilled: 0 } as Record<'rejected' | 'fulfilled', number>

  responses.forEach((response) => {
    results[response.status] += 1
  })

  return results
}

const fetchWeather = async ({ region, regions }: { region: string; regions: AllRegions }) => {
  const regionInfo = regions.getRegionBySlug(region)

  if (!regionInfo) return null

  const isDistrict = regions.isDistrict(regionInfo)

  const regionForBounds = isDistrict
    ? regions.getParentBySlug(region)
    : regions.getRegionBySlug(region)

  if (!regionForBounds?.slug) return null

  const [weatherInfo] = await Promise.all([
    buildWeatherQuery({ region, isDistrict, forceCacheUpdate: true }),
  ])

  if (!regionInfo?.slug || !weatherInfo) {
    throw new Error('Could not load data')
  }

  return weatherInfo
}

const fetchMostReadArticles = async ({
  regions,
}: {
  regions: Awaited<ReturnType<typeof allRegions>>
}) => {
  const regionSlugs = regions.getRegionSlugsInHierachy()
  let responses: Awaited<ReturnType<typeof Promise.allSettled>> = await Promise.allSettled([
    getMostReadArticles({ region: regionAustria }),
  ])

  responses = responses.concat(
    await Promise.allSettled(
      regionSlugs.states.map((slug, idx) =>
        executeWithDelay(() => getMostReadArticles({ region: slug! }), idx * DELAY_MS),
      ),
    ),
  )

  responses = responses.concat(
    await Promise.allSettled(
      regionSlugs.districts.map((slug, idx) =>
        executeWithDelay(() => getMostReadArticles({ region: slug! }), idx * DELAY_MS),
      ),
    ),
  )

  return responses
}

const executeWithDelay = async <T>(fn: () => Promise<T>, delay: number) => {
  await new Promise((resolve) => setTimeout(resolve, delay))
  return fn()
}
