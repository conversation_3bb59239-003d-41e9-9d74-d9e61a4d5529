//  Because of our assets folder is set to /rma_build we can't just place the robots.txt file in the root of the public folder as it will only be available at /rma_build/robots.txt.
//  Instead we can create a route that returns the robots.txt file content.
export const loader = async () => {
  const robotsText = `# www.robotstxt.org/
# www.google.com/support/webmasters/bin/answer.py?hl=en&answer=156449
Sitemap: https://www.meinbezirk.at/sitemap.xml

User-agent: *
Disallow: /build/html/
Disallow: /customerfonts/
Allow: /resources/mediadb/
Disallow: /resources/
Disallow: /uploads/
Disallow: /event/download/`

  return new Response(robotsText, {
    status: 200,
    headers: {
      'Cache-Control': 'max-age=0, s-maxage=86400',
      'Content-Type': 'text/plain',
    },
  })
}
