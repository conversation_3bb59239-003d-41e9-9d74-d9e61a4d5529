import { json, type LoaderFunctionArgs } from '@remix-run/node'
import { logger } from 'utils/logger.server'
import { PeiqApiWrapper } from 'utils/peiqApiWrapper.server'

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const referer = request.headers.get('Referer')

  if (!matchesReferer(referer)) {
    return new Response(null, {
      status: 403,
      statusText: 'Referer not allowed',
    })
  }

  const url = new URL(request.url)
  const userId = url.searchParams.get('id')

  if (!userId) {
    return new Response(null, {
      status: 400,
      statusText: 'Bad Request',
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
    })
  }

  const user = await getUserWithStats(Number(userId))

  if (!user) {
    return new Response(null, {
      status: 404,
      statusText: 'Not Found',
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
    })
  }

  return json(user, {
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  })
}

const getUserWithStats = async (userId: number) => {
  try {
    const userStats = await PeiqApiWrapper.UserApi.fetchUserStats(userId)
    return {
      name: userStats.user.full_name,
      role: userStats.user.user_segment,
      userId: userStats.user.id,
      image: userStats.user.avatar?.url ? modifyUrlSize(userStats.user.avatar.url, 'S') : undefined,
      photos: undefined, // not implemented yet
      articles: userStats.publishedArticles,
      articleDrafts: userStats.draftArticles,
      photoDrafts: userStats.draftImagePosts,
      eventDrafts: userStats.draftEvents,
    }
  } catch (error) {
    logger.error('Error fetching user stats', error)
    return null
  }
}

const matchesReferer = (referer: string | null) => {
  const partials = ['run.app', 'localhost:3000', 'meinbezirk.at']

  return referer && partials.some((partial) => referer.includes(partial))
}

const sizeModifiers = ['NATIVE', 'T', 'XS', 'S', 'M', 'L', 'XL', 'XXL'] as const

/**
 * Change the size modifier of the image url
 * @param url Looks like this https://media04.meinbezirk.at/user/2024/01/25/6/155026_XL.png
 * @param size
 */
const modifyUrlSize = (urlString: string, size: (typeof sizeModifiers)[number]) => {
  const groups = /^(?<base>.*)_(?<size>.*)\.(?<extension>.*)$/.exec(urlString)?.groups

  if (!groups) {
    return urlString
  }

  const { base, extension } = groups

  return `${base}_${size}.${extension}`
}
