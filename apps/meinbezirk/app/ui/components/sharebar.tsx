import { useLocation } from '@remix-run/react'
import { FacebookIcon, MailIcon, SharingIcon, XIcon } from '@rma-mono/rma-icons'
import { useEffect, useState } from 'react'
import { css, cva } from 'panda/css'

export const Sharebar = () => {
  const location = useLocation()
  const [shareUrl, setShareUrl] = useState('https://meinbezirk.at')
  const [shareTitle, setShareTitle] = useState('Meinbezirk.at')
  const [canShare, setCanShare] = useState(false)

  useEffect(() => {
    setShareUrl(encodeURI(window.location.href))
    setShareTitle(document.title)
  }, [location])

  useEffect(() => {
    setCanShare(!!navigator.share)
  }, [])

  return (
    <aside
      aria-label="Seite Teilen"
      className={css({
        my: '1rem',
      })}
    >
      <ul
        className={css({
          display: 'flex',
          justifyContent: 'center',
          listStyle: 'none',
          padding: 0,
          margin: 0,
          '& li': {
            margin: '0 0.5rem',
          },
        })}
      >
        <li>
          <a
            href={`https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`}
            target="_blank"
            rel="noreferrer noopener"
          >
            <FacebookIcon
              width={32}
              height={32}
              className={socialIconVariant({ type: 'facebook' })}
            />
          </a>
        </li>
        <li>
          <a
            href={`https://x.com/intent/tweet?url=${shareUrl}`}
            target="_blank"
            rel="noreferrer noopener"
          >
            <XIcon width={32} height={32} className={socialIconVariant({ type: 'twitter' })} />
          </a>
        </li>
        <li>
          <a
            href={`mailto:?subject=${shareTitle}&body=${shareUrl}`}
            target="_blank"
            rel="noreferrer noopener"
          >
            <MailIcon
              name="mail"
              width={32}
              height={32}
              className={socialIconVariant({ type: 'mail' })}
            />
          </a>
        </li>
        <li
          className={css({
            display: 'none',
            mdDown: {
              display: canShare ? 'block' : 'none',
            },
          })}
        >
          <button
            onClick={(e) => {
              e.preventDefault()

              if (navigator.share) {
                navigator.share({
                  title: shareTitle,
                  url: shareUrl,
                })
              }
            }}
            type="button"
          >
            <SharingIcon
              name="share"
              width={32}
              height={32}
              className={socialIconVariant({ type: 'share' })}
            />
          </button>
        </li>
      </ul>
    </aside>
  )
}

const socialIconVariant = cva({
  base: {
    backgroundColor: 'rmaNeutral.50',
    borderRadius: '9999px',
    transition: '150ms color, background-color ease-in',
    color: 'rmaNeutral.900',
    _hover: {
      color: 'white',
    },
  },
  variants: {
    type: {
      facebook: {
        _hover: {
          backgroundColor: '#344F75',
        },
      },
      twitter: {
        _hover: {
          backgroundColor: '#008ACC',
        },
      },
      mail: {
        _hover: {
          backgroundColor: 'rmaNeutral.800',
        },
      },
      share: {
        cursor: 'pointer',
        _hover: {
          backgroundColor: 'rmaNeutral.800',
        },
      },
    },
  },
})
