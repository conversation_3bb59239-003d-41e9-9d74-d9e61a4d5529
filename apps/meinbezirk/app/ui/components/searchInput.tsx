import { SearchIcon } from '@rma-mono/rma-icons'
import type { RefObject, ChangeEvent } from 'react'
import { css, cva, cx } from 'panda/css'
import { buttonVariant } from 'variants/button'

interface SearchInputProps {
  placeholder?: string
  activeIcon?: boolean
  size?: 'topBar' | 'ePaper'
  className?: string
  inputRef?: RefObject<HTMLInputElement>
  onInputChange?: (event: ChangeEvent<HTMLInputElement>) => void
  onInputFocusChange?: (isFocused: boolean) => void
}

export const SearchInput = ({
  placeholder,
  onInputChange,
  onInputFocusChange,
  inputRef,
  activeIcon = false,
  size,
  className,
}: SearchInputProps) => {
  return (
    <div
      className={cx(
        css({
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'stretch',
          position: 'relative',
        }),
        className,
      )}
    >
      <input
        className={cx(
          searchInputVariant({ size }),
          css({
            position: 'relative',
            zIndex: 2,
          }),
        )}
        type="text"
        placeholder={placeholder}
        ref={inputRef}
        onChange={onInputChange}
        onFocus={() => {
          onInputFocusChange?.(true)
        }}
        onBlur={() => {
          onInputFocusChange?.(false)
        }}
      />
      <div
        className={css({
          position: 'absolute',
          right: 0,
          zIndex: 3,
          width: 9,
          height: '100%',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
        })}
      >
        {activeIcon ? (
          <button
            type="submit"
            className={cx(
              buttonVariant({ type: 'iconButtonGhosted' }),
              css({
                width: '100%',
                height: '100%',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
              }),
            )}
            aria-label="Search"
          >
            <SearchIcon
              size={20}
              className={css({
                fill: 'rmaNeutral.500',
              })}
            />
          </button>
        ) : (
          <SearchIcon
            size={20}
            className={css({
              fill: 'rmaNeutral.500',
            })}
          />
        )}
      </div>
    </div>
  )
}

const searchInputVariant = cva({
  base: {
    backgroundColor: 'white',
    py: 2,
    pl: 3,
    pr: 9,
    borderWidth: '1px',
    width: '100%',
    outline: 'none',
    borderColor: 'rmaNeutral.300',
    borderRadius: 'none',
    _placeholder: {
      fontSize: 'sm',
      fontWeight: 'regular',
      color: 'rmaNeutral.500',
    },
  },
  variants: {
    size: {
      topBar: {
        height: 9,
      },
      ePaper: {
        height: 10,
      },
    },
  },
})
