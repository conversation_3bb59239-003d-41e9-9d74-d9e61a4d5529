import * as Popover from '@radix-ui/react-popover'
import { ChevronDownBoldIcon } from '@rma-mono/rma-icons'
import type { Argument } from 'panda/css'
import { css, cx } from 'panda/css'
import { buttonVariant } from 'variants/button'

export const SlideUpMenu = ({
  label,
  contentClassName,
  children,
  isOpen,
  setIsOpen,
}: {
  label: string
  children: React.ReactNode
  contentClassName?: Argument
  isOpen?: boolean
  setIsOpen?: (isOpen: boolean) => void
}) => {
  return (
    <div
      className={css({
        md: { display: 'none' },
        position: 'fixed',
        bottom: 0,
        left: '0',
        right: '0',
        width: '100%',
        backgroundColor: 'rmaWhite',
        zIndex: 'tooltip', // using tooltip (5000005) instead of popover (4999999) because it needs to be above header (5000000)
      })}
    >
      <Popover.Root
        modal
        open={isOpen}
        onOpenChange={(isOpen) => {
          setIsOpen?.(isOpen)
        }}
      >
        <Popover.Trigger
          onClick={() => setIsOpen?.(!isOpen)}
          className={cx(
            'group',
            css({
              backgroundColor: 'rmaWhite',
              display: 'flex',
              alignItems: 'center',
              position: 'relative',
              zIndex: 'toast', // Content should slide below the trigger button
              justifyContent: 'center',
              padding: '2.5',
              width: '100%',
              _groupRadixOpen: {
                boxShadow: '0px 1px 3px 0px rgba(0, 0, 0, 0.1),0px 1px 2px 0px rgba(0, 0, 0, 0.06)',
              },
            }),
          )}
        >
          <div
            className={cx(
              buttonVariant({
                type: 'secondary',
              }),
              css({
                display: 'flex',
                backgroundColor: 'white',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '2',
                color: 'rmaRed.100',
                fontWeight: 'semiBold',
                width: '100%',
                shadow: 'base',
              }),
            )}
          >
            {label}
            <ChevronDownBoldIcon
              size={24}
              className={css({
                _groupRadixClosed: {
                  transform: 'rotate(0deg)',
                },
                _groupRadixOpen: {
                  transform: 'rotate(-180deg)',
                },
                transition: 'transform .2s ease',
                fill: 'rmaRed.100',
              })}
            />
          </div>
        </Popover.Trigger>
        <Popover.Anchor />
        <Popover.Portal>
          <Popover.Content
            side="top"
            sideOffset={52}
            align="center"
            className={cx(
              css({
                width: '100vw',
                height: 'var(--radix-popper-available-height)',
                overflowY: 'auto',
                transformOrigin: 'var(--radix-popover-transform-origin)',
                animation: 'slideInBottom 300ms ease-out',
                position: 'relative',
                zIndex: 'modal',
                border: 'none',
                p: 0,
                backgroundColor: 'rmaWhite',
              }),
              contentClassName,
            )}
          >
            {children}
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
    </div>
  )
}
