import type { ReactNode } from 'react'
import type { ArticleItemType } from 'services/search'
import { css } from 'panda/css'
import { MostReadArticles } from 'ui/components/mostReadArticles'
import { GoogleAdSlot } from 'ui/features/ads/googleAds/googleAdSlot'

export const Sidebar = ({
  children,
  mostReadArticles,
}: {
  children?: ReactNode
  mostReadArticles?: ArticleItemType[] | null
}) => {
  return (
    <div
      className={css({
        display: 'flex',
        flexDirection: 'column',
        borderLeftWidth: {
          base: 'none',
          md: '1px',
        },
        borderLeftColor: 'rmaNeutral.200',
        width: {
          base: '100%',
          md: '332px',
        },
        flexBasis: '332px',
        flexShrink: 0,
        bg: 'rmaNeutral.50',
        direction: 'column',
      })}
    >
      {children ?? (
        <>
          <div
            className={css({
              mdDown: {},
              margin: '0px auto',
              maxWidth: '300px',
            })}
          >
            <GoogleAdSlot
              className={css({
                marginY: '4',
              })}
              slotId="mediumRectangle2"
            />
          </div>
          {mostReadArticles && (
            <div
              className={css({
                textAlign: 'center',
              })}
            >
              <MostReadArticles articles={mostReadArticles} />
            </div>
          )}
        </>
      )}
    </div>
  )
}
