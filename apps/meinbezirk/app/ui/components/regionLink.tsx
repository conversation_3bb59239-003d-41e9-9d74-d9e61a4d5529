import { Link } from '@remix-run/react'
import { LocationIcon } from '@rma-mono/rma-icons'
import type { ReactNode } from 'react'
import { useRegion } from 'hooks/useRegion'
import { css, cva } from 'panda/css'

interface RegionPropsType {
  variant?: 'mainNavigation' | 'burgerMenu'
  rightIcon?: ReactNode
  to: string
  target?: '_blank'
}

export const RegionLink = ({ variant, rightIcon, to, target }: RegionPropsType) => {
  const { region } = useRegion()

  return (
    <button
      className={regionLinkVariant({
        type: variant,
      })}
    >
      <Link
        target={target}
        to={to}
        className={css({
          _hover: {
            textDecoration: 'none',
          },
          flexGrow: 1,
          display: 'block',
        })}
      >
        <LocationIcon
          className={css({
            position: 'absolute',
            top: '50%',
            transform: 'translateY(-50%)',
            fill: 'rmaRed.100',
          })}
          size={24}
        />
        <div
          className={regionLinkTextVariant({
            type: variant,
          })}
        >
          {region?.title}
        </div>
        {rightIcon && (
          <div
            className={css({
              position: 'absolute',
              top: '50%',
              transform: 'translateY(-50%)',
              right: 3,
            })}
          >
            {rightIcon}
          </div>
        )}
      </Link>
    </button>
  )
}

export const regionLinkVariant = cva({
  base: {
    display: 'flex',
    flexWrap: 'wrap',
    textAlign: 'left',
    cursor: 'pointer',
    position: 'relative',
    width: '100%',
    _hover: {
      textDecoration: 'none',
      backgroundColor: 'rmaRed.4',
    },
    _focusVisible: {
      outline: 0,
      backgroundColor: 'rmaRed.4',
    },
  },
  variants: {
    type: {
      mainNavigation: {
        py: 4,
        pl: 2,
        pr: 3,
        height: '100%',
        alignContent: 'center',
      },
      burgerMenu: {
        px: 3,
        py: 2,
        height: '3.125rem',
        borderYWidth: '1px',
        borderColor: 'rmaNeutral.200',
        alignContent: 'center',
      },
    },
  },
})

export const regionLinkTextVariant = cva({
  base: {
    color: 'rmaRed.100',
    ml: 7,
  },
  variants: {
    type: {
      mainNavigation: {
        fontSize: 'sm',
        lineHeight: 'none',
        fontWeight: 'bold',
      },
      burgerMenu: {
        fontSize: 'md',
        lineHeight: 'none',
        fontWeight: 'semiBold',
      },
    },
  },
})
