import { useLocation } from '@remix-run/react'
import { useEffect, useState } from 'react'
import { useConsent } from 'ui/features/tracking/useConsent'
interface OutbrainWidgetProps {
  environment: string | undefined
  externalId: string | null | undefined
  externalSecondaryId: string | undefined
  widgetId: (typeof OutbrainWidgetIds)[keyof typeof OutbrainWidgetIds]
}

export const OutbrainWidgetIds = {
  AR10: 'AR_10', //smartfeed desktop
  AR2: 'AR_2', //smartfeed desktop oben - small
  MB3: 'MB_3', //gallery mobile - small above image
} as const

export const OutbrainWidget = ({
  environment,
  externalId,
  externalSecondaryId,
  widgetId,
}: OutbrainWidgetProps) => {
  const [url, setUrl] = useState('')
  const { pathname } = useLocation()
  const { tcfConsentProfile } = useConsent()
  const [isActive, setIsActive] = useState(false)

  useEffect(() => {
    setUrl(window.location.origin + window.location.pathname)
  }, [pathname])

  useEffect(() => {
    if (!tcfConsentProfile) {
      return
    }

    const scriptId = 'outbrain-widget-script'

    if (document.getElementById(scriptId)) {
      return
    }

    const script = document.createElement('script')
    script.src = 'https://widgets.outbrain.com/outbrain.js'
    script.async = true
    script.onload = () => {
      setIsActive(true)
    }
    script.id = scriptId
    document.body.appendChild(script)

    return () => {
      document.querySelector(`#${scriptId}`)?.remove()
    }
  }, [tcfConsentProfile, url])

  useEffect(() => {
    window.OBR?.extern?.refreshWidget()
  }, [url, isActive])

  return (
    <aside>
      <div
        className="OUTBRAIN"
        data-src={url}
        data-external-id={externalId}
        data-external-secondary-id={externalSecondaryId}
        data-widget-id={widgetId}
        data-ob-test={environment === 'production' ? undefined : 'true'}
      ></div>
    </aside>
  )
}

declare global {
  interface Window {
    OBR: any
  }
}
