import * as Dialog from '@radix-ui/react-dialog'
import { CloseIcon } from '@rma-mono/rma-icons'
import { useEffect, type ReactNode } from 'react'
import { css, cva } from 'panda/css'

interface SideMenuProps {
  isOpen: boolean
  onClose?: () => void
  onOpen?: () => void
  header?: ReactNode
  body?: ReactNode
  footer?: ReactNode
  variant?: 'menu' | 'userMenu'
  keyboard?: 'closed' | 'open'
}

export const SideMenu = ({
  isOpen,
  onClose,
  onOpen,
  header,
  body,
  footer,
  variant = 'menu',
  keyboard = 'closed',
}: SideMenuProps) => {
  useEffect(() => {
    if (isOpen) {
      onOpen?.()
    } else {
      onClose?.()
    }
  }, [isOpen, onClose, onOpen])

  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          onClose?.()
        }
      }}
      modal
    >
      <Dialog.Portal>
        <Dialog.Overlay className={drawerOverlayVariants({ type: variant })} />
        <Dialog.Content className={drawerContentVariants({ type: variant, keyboard })}>
          {!header && <SideMenuCloseButton variant={variant} />}

          {header && (
            <div className={drawerHeaderVariants({ type: variant })}>
              <SideMenuCloseButton variant={variant} />
              {header}
            </div>
          )}

          {body && (
            <>
              <div
                className={css({
                  px: 0,
                  py: 0,
                })}
              >
                {body}
              </div>
            </>
          )}

          {footer && (
            <>
              <div
                className={css({
                  borderTopWidth: '1px',
                  borderTopColor: 'rmaNeutral.300',
                  pt: 2,
                  mt: 2,
                })}
              >
                {footer}
              </div>
            </>
          )}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

const SideMenuCloseButton = ({ variant = 'menu' }: { variant?: 'menu' | 'userMenu' }) => {
  return (
    <Dialog.Close asChild>
      <button className={drawerCloseButtonVariants({ type: variant })}>
        <CloseIcon
          size={24}
          className={css({
            fill: 'rmaNeutral.900',
          })}
        />
      </button>
    </Dialog.Close>
  )
}

const drawerContentVariants = cva({
  base: {
    backgroundColor: 'white',
    position: 'fixed',
    top: 0,
    zIndex: 'modal',
    height: '100dvh',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'auto',
    maxWidth: '100%',
    width: '340px',
  },
  variants: {
    type: {
      menu: {
        left: 0,
        transform: 'translateX(-100%)',
        '&[data-state=open]': {
          animationDelay: '200ms',
          animationName: 'slideInLeft',
          animationDuration: '200ms',
          animationTimingFunction: 'ease-in',
          animationFillMode: 'forwards',
        },
        '&[data-state=closed]': {
          animationName: 'slideOutLeft',
          animationDuration: '200ms',
          animationTimingFunction: 'ease-out',
          animationFillMode: 'forwards',
        },
      },
      userMenu: {
        right: 0,
        transform: 'translateX(100%)',
        '&[data-state=open]': {
          animationDelay: '200ms',
          animationName: 'slideInRight',
          animationDuration: '200ms',
          animationTimingFunction: 'ease-in',
          animationFillMode: 'forwards',
        },
        '&[data-state=closed]': {
          animationName: 'slideOutRight',
          animationDuration: '200ms',
          animationTimingFunction: 'ease-out',
          animationFillMode: 'forwards',
        },
      },
    },
    keyboard: {
      open: {
        paddingBottom: '300px',
      },
      closed: {
        paddingBottom: '0',
      },
    },
  },
})

const drawerOverlayVariants = cva({
  base: {
    opacity: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    position: 'fixed',
    inset: 0,
    zIndex: 'overlay',
    '&[data-state=open]': {
      animationName: 'fadeIn',
      animationDuration: '200ms',
      animationTimingFunction: 'ease-in',
      animationFillMode: 'forwards',
    },
    '&[data-state=closed]': {
      animationName: 'fadeOut',
      animationDuration: '200ms',
      animationTimingFunction: 'ease-out',
      animationFillMode: 'forwards',
    },
  },
  variants: {
    type: {
      menu: {},
      userMenu: {},
    },
  },
})

const drawerHeaderVariants = cva({
  base: {
    position: 'sticky',
    top: 0,
    backgroundColor: 'white',
    zIndex: 'overlay',
  },
  variants: {
    type: {
      menu: {
        py: 2,
        pr: 2,
        pl: 12,
        borderBottomWidth: '1px',
        borderBottomColor: 'rmaNeutral.300',
      },
      userMenu: {
        py: 9,
        px: 0,
        borderBottomWidth: '1px',
        borderBottomColor: 'rmaNeutral.300',
      },
    },
  },
})

const drawerCloseButtonVariants = cva({
  base: {
    display: 'flex',
    cursor: 'pointer',
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 'md',
    width: 8,
    height: 8,
    zIndex: 'modal',
    transition: 'background 0.2s ease-in-out',
    _hover: {
      bg: 'rmaNeutral.200',
    },
    _focusVisible: {
      bg: 'rmaNeutral.200',
    },
  },
  variants: {
    type: {
      menu: {
        top: 3.5,
        left: 3,
        right: 'auto',
      },
      userMenu: {
        top: 3.5,
        right: 3,
      },
    },
  },
})
