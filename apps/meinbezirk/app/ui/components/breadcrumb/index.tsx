import { Link, useLocation, useParams } from '@remix-run/react'
import { ChevronRightIcon, HomeIcon } from '@rma-mono/rma-icons'

import type { RegionTreeChildrenType } from 'services/regions'
import { css, cx } from 'panda/css'
import { routes } from 'routes'
import { strings } from 'strings'

const BreadcrumbItem = ({
  children,
  isCurrentPage = false,
}: {
  children: React.ReactNode
  isCurrentPage?: boolean
}) => {
  return (
    <li
      className={cx(
        css({
          display: 'flex',
          outline: '1px solid rmaRed.100',
          outlineOffset: '2px',
          alignItems: 'center',
          fontSize: 'xs',
        }),
      )}
    >
      <span
        className={css({
          color: isCurrentPage ? 'rmaNeutral.900' : 'rmaNeutral.700',
          _hover: {
            color: 'rmaRed.100',
          },
        })}
      >
        {children}
      </span>
      {!isCurrentPage && (
        <span
          className={css({
            display: isCurrentPage ? 'none' : 'block',
            marginInline: 2,
            color: 'rmaNeutral.700',
            fontWeight: 'medium',
          })}
          role="presentation"
          aria-hidden="true"
        >
          <ChevronRightIcon
            size={16}
            className={css({
              fill: 'rmaNeutral.700',
              _hover: {
                fill: 'rmaRed.100',
              },
            })}
          />
        </span>
      )}
    </li>
  )
}

export const Breadcrumb = ({ regions }: { regions: RegionTreeChildrenType }) => {
  const params = useParams()
  const { pathname } = useLocation()

  const breadcrumbs = getBreadcrumbData(
    regions,
    params?.region as string,
    pathname.split('/').slice(1).pop(),
  )

  if (breadcrumbs?.length === 0) {
    return null
  }

  return (
    <ol
      className={css({
        px: 3,
        pb: 3,
        pt: 0,
        rowGap: 2,
        display: 'flex',
        alignItems: 'center',
        flexWrap: 'wrap',
      })}
    >
      <BreadcrumbItem>
        <Link reloadDocument to={routes.index}>
          <HomeIcon
            size={16}
            className={css({
              fill: 'rmaNeutral.700',
              _hover: {
                fill: 'rmaRed.100',
              },
            })}
          />
        </Link>
      </BreadcrumbItem>

      {breadcrumbs.map(({ title, slug }, index) => (
        <BreadcrumbItem isCurrentPage={index + 1 === breadcrumbs.length} key={index}>
          <Link reloadDocument to={'/' + slug} className={css({ fontWeight: 500 })}>
            {title}
          </Link>
        </BreadcrumbItem>
      ))}
    </ol>
  )
}

const getBreadcrumbData = (
  regions: RegionTreeChildrenType,
  currentRegion: string | undefined,
  currentPage: string | undefined,
) => {
  if (!regions) {
    return []
  }

  const crumbs: { title?: string; slug: string }[] = []

  if (currentRegion === undefined) {
    return crumbs
  }

  regions.forEach((region) => {
    const subregionslug = region.children?.find(
      ({ slug, title }) => slug === currentRegion && title !== 'Alle Bezirke',
    )?.slug
    const regionMatch = region.slug === currentRegion || subregionslug !== undefined

    if (regionMatch && region.slug) {
      crumbs.push({
        title: strings.regions.regionBySlug({ slug: region.slug }),
        slug: region.slug,
      })
    }

    if (subregionslug) {
      crumbs.push({
        title: strings.regions.regionBySlug({ slug: subregionslug }),
        slug: subregionslug,
      })
    }

    const hasNonregionPath =
      currentPage !== undefined && region.slug !== currentPage && subregionslug !== currentPage

    if (hasNonregionPath && regionMatch) {
      crumbs.push({
        title: strings.breadcrumb.breadcrumbBySlug({ slug: currentPage }),
        slug: currentPage,
      })
    }
  })

  return crumbs
}
