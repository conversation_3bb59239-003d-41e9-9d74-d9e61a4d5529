import { useLocation } from '@remix-run/react'

const matchState =
  '(?:burgenland|steiermark|wien|salzburg|tirol|vorarlberg|niederoesterreich|oberoesterreich|kaernten)?'

export type PageTypeType = keyof typeof pageTypes

export const pageTypes = {
  weather: new RegExp(`^/?wetter$`),
  weatherState: new RegExp(`^/${matchState}/wetter$`),
  weatherDistrict: new RegExp('^/[^/]+/wetter$'),
  legalPage: new RegExp('^/s/(agbs|datenschutz|verhaltenskodex)$'),
  imprintPage: new RegExp('^/s/impressum(-(' + matchState + '))?$'),
  epaper: new RegExp('^/s/epaper(-(' + matchState + '))?$'),
  newsletter: new RegExp('^(/[^/]+)?/newsletter(-(' + matchState + '))?$'),
  contact: new RegExp('^/s/ueber-uns-(' + matchState + ')$'),
} as const

export const getPageTypeForPath = (pathname: string): PageTypeType | undefined => {
  for (const [key, value] of Object.entries(pageTypes)) {
    if (pathname.match(value)) {
      return key as keyof typeof pageTypes
    }
  }

  return undefined
}

export const usePageType = () => {
  const { pathname } = useLocation()

  return getPageTypeForPath(pathname)
}
