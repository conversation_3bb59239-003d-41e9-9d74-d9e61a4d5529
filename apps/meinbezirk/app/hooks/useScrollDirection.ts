import { useEffect, useState } from 'react'

const ScrollDirection = {
  up: 'up',
  down: 'down',
} as const

type ScrollDirectionType = (typeof ScrollDirection)[keyof typeof ScrollDirection]

export const useScrollDirection = (threshold = 50) => {
  const [scrollDir, setScrollDir] = useState<ScrollDirectionType>(ScrollDirection.up)
  const [prevDocumentHeight, setPrevDocumentHeight] = useState(0)

  useEffect(() => {
    const updateDocumentHeight = () => {
      setPrevDocumentHeight(document.documentElement.scrollHeight)
    }

    updateDocumentHeight()

    let previousScrollY = window.scrollY

    const scrolledMoreThanThreshold = (currentScrollY: number) =>
      Math.abs(currentScrollY - previousScrollY) > threshold

    const isScrollingUp = (currentScrollY: number) =>
      currentScrollY > previousScrollY &&
      !(previousScrollY > 0 && currentScrollY === 0) &&
      !(currentScrollY > 0 && previousScrollY === 0)

    const updateScrollDirection = () => {
      const currentScrollY = window.scrollY

      if (scrolledMoreThanThreshold(currentScrollY)) {
        const newScrollDirection = isScrollingUp(currentScrollY)
          ? ScrollDirection.down
          : ScrollDirection.up

        setScrollDir(newScrollDirection)

        previousScrollY = currentScrollY > 0 ? currentScrollY : 0
      }
    }

    const onScroll = () =>
      window.requestAnimationFrame(() => {
        updateScrollDirection()
        updateDocumentHeight()
      })

    window.addEventListener('scroll', onScroll)

    return () => window.removeEventListener('scroll', onScroll)
  }, [threshold, prevDocumentHeight])

  return scrollDir
}
