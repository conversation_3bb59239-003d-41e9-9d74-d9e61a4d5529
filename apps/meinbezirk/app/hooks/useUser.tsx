import { useLocation, useParams } from '@remix-run/react'
import { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { getCookieValue } from 'utils/getCookieValue'

export type UserAuthorRoleType =
  | 'redakteur'
  | 'regionaut'
  | 'kommerzieller-regionaut'
  | 'freier-mitarbeiter'

export type UserRoleType =
  | 'redakteur'
  | 'oesterreich'
  | 'regionaut'
  | 'kommerzieller-regionaut'
  | 'freier-mitarbeiter'

type UserType = {
  name: string
  role: UserRoleType
  userId: number
  image?: string | undefined
  photos?: number
  articles?: number
  articleDrafts?: number
  photoDrafts?: number
  eventDrafts?: number
}

interface UserContextValues {
  user: UserType | null
  status: 'authenticated' | 'unauthenticated'
  logout: () => void
}

const UserContext = createContext<UserContextValues>({
  user: null,
  status: 'unauthenticated',
  logout: () => undefined,
})

const getPeiqUserIdFromCookie = () => {
  const value = getCookieValue('pp_rma_user_id')

  if (!value) return null

  return Number(value)
}

export const UserProvider = ({
  children,
  directUrl,
}: {
  children: React.ReactNode
  directUrl: string
}) => {
  const { pathname } = useLocation()
  const params = useParams()
  const [userId, setUserId] = useState<number | null>(getPeiqUserIdFromCookie())
  const [user, setUser] = useState<UserType | null>(null)

  const logout = useCallback(() => {
    setUser(null)
    window.location.href = 'https://www.meinbezirk.at/logout'
  }, [setUser])

  useEffect(() => {
    setUserId(getPeiqUserIdFromCookie())
  }, [pathname, params])

  useEffect(() => {
    if (userId) {
      fetch(`${directUrl}/rma-api/fetchPeiqUser?id=${userId}`)
        .then((res) => {
          if (!res.ok) {
            throw new Error(res.statusText)
          }

          return res.json()
        })
        .catch((err) => {
          console.error(err)
          setUser(null)
        })
        .then((user) => {
          setUser(user)
        })
    }
  }, [userId, directUrl])

  return (
    <UserContext.Provider
      value={{
        user,
        status: user ? 'authenticated' : 'unauthenticated',
        logout,
      }}
    >
      {children}
    </UserContext.Provider>
  )
}

export const useUser = () => useContext(UserContext)
