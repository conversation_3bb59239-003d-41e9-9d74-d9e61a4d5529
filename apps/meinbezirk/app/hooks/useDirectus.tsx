import { createContext, useContext, useCallback } from 'react'
import type { DirectusFiles } from '@rma-mono/directus-client'

type DirectusAssetsParams = {
  assetId: string | DirectusFiles
  options?: {
    key?: string
    download?: boolean
  }
}

interface DirectusContextValues {
  directusAsset: ({ assetId, options }: DirectusAssetsParams) => string
}

const DirectusContext = createContext<DirectusContextValues>({
  directusAsset: ({ assetId, options }) => '',
})

export const DirectusProvider = ({
  children,
  baseUrl,
}: {
  children: React.ReactNode
  baseUrl: string
}) => {
  const directusAsset = useCallback(
    ({ assetId, options }: DirectusAssetsParams) => {
      const searchParams = new URLSearchParams()

      for (const [key, value] of Object.entries(options ?? {})) {
        if (value === undefined) continue
        if (typeof value === 'boolean' && !value) continue
        if (typeof value === 'string') searchParams.set(key, value)
        if (typeof value === 'boolean') searchParams.append(key, 'true')
      }

      const id = typeof assetId === 'string' ? assetId : assetId.id

      return `${baseUrl}/assets/${id}?${searchParams.toString()}`
    },
    [baseUrl],
  )

  return (
    <DirectusContext.Provider
      value={{
        directusAsset,
      }}
    >
      {children}
    </DirectusContext.Provider>
  )
}

export const useDirectus = () => useContext(DirectusContext)
