// TODO: refactor this once adition is added back
// see dataLayer.tsx for the new implementation

import { useMediaQuery } from '@mantine/hooks'
import { useLocation } from '@remix-run/react'
import { useRegion } from 'hooks/useRegion'

type MetadataType = {
  deviceType: 'desktop' | 'mobile'
  pagetype: 'weather' | null
  region: string | null
  tags: string
}

const locationToPageType = (location: string) => {
  if (location.includes('wetter')) {
    return 'weather'
  }

  return null
}

export function useMetadata(): { metadataForAdition: MetadataType } {
  const location = useLocation()
  const device = useMediaQuery('(max-width: 768px)')
  const { region } = useRegion()

  return {
    metadataForAdition: {
      deviceType: device ? 'mobile' : 'desktop',
      pagetype: locationToPageType(location.pathname),
      region: region?.slug ?? null,
      tags: 'photovoltaik_oberoesterreich_windkraft_strom_wasserkraft_pv-anlage_netz-ooe_energie-energien-oberoesterreich',
    },
  }
}
