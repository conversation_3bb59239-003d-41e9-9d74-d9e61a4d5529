import { regionAustria } from '@rma-mono/rma-regions'
import { usePageType } from './usePageType'
import { useRegion, useStateRegion } from './useRegion'
import type { PageTypeType } from './usePageType'

const OEWA_BASE_PATHS = {
  weather: 'RedCont/Wetter/Oesterreichwetter/meinbezirk.at',
  weatherRegion: 'RedCont/Wetter/Wetterueberblick/meinbezirk.at',
  epaper: 'RedCont/Nachrichten/Nachrichtenueberblick/meinbezirk.at',
  newsletter: 'Service/Rubrikenmaerkte/Sonstiges/meinbezirk.at',
  companyCommunication: 'Service/Unternehmenskommunikation/Unternehmenskommunikation/meinbezirk.at',
  marketSection: 'Service/Rubrikenmaerkte/Sonstiges/meinbezirk.at',
} as const

const oewaPathForPageType = ({ pageType }: { pageType: PageTypeType }) => {
  switch (pageType) {
    case 'weather':
      return OEWA_BASE_PATHS.weather
    case 'weatherState':
    case 'weatherDistrict':
      return OEWA_BASE_PATHS.weatherRegion
    case 'newsletter':
      return OEWA_BASE_PATHS.newsletter
    case 'epaper':
      return OEWA_BASE_PATHS.epaper
    case 'contact':
      return OEWA_BASE_PATHS.marketSection
    case 'legalPage':
      return OEWA_BASE_PATHS.companyCommunication
    case 'imprintPage':
      return OEWA_BASE_PATHS.marketSection
  }
}

export const useOewaPath = () => {
  const { region } = useRegion()
  const state = useStateRegion()
  const pageType = usePageType()

  if (!pageType) {
    return ''
  }

  const hasRegion = !!region?.slug || !!state?.slug

  const oewaBasePath = oewaPathForPageType({ pageType })

  if (!hasRegion || pageType === 'legalPage') {
    return oewaBasePath
  }

  if (region?.slug === regionAustria) {
    return `${oewaBasePath}/oesterreich`
  }

  if (state?.slug === region?.slug) {
    return `${oewaBasePath}/${state?.slug}`
  }

  return `${oewaBasePath}/${state?.slug}/${region?.slug}`
}
