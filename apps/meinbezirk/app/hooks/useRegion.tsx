import { useDisclosure } from '@mantine/hooks'
import { useLocation, useParams } from '@remix-run/react'
import { regionAustria } from '@rma-mono/rma-regions'
import { createContext, useContext, useMemo } from 'react'
import { usePageType } from './usePageType'
import type { ReactNode } from 'react'
import type { FullRegionDataType } from 'services/regions'
import type { RegionNameType } from 'strings/regions'
import type { RegionHelpersType } from 'utils/regionsHelpers'
import { strings } from 'strings'
import { regionsHelpers } from 'utils/regionsHelpers'

type Region = { title: RegionNameType | undefined; slug: string }

interface RegionContextValues {
  region: Region | undefined
  regionHelpers?: RegionHelpersType
  opened: boolean
  openRegionMenu: () => void
  closeRegionMenu: () => void
}

export const Region = createContext<RegionContextValues>({
  region: undefined,
  regionHelpers: undefined,
  opened: false,
  openRegionMenu: () => undefined,
  closeRegionMenu: () => undefined,
})

const useRegionParams = () => {
  const pageType = usePageType()
  const location = useLocation()
  const params = useParams()

  if (pageType === 'epaper') {
    // match `/s/epaper-${regionSlug}`
    const match = location.pathname.match(/^\/s\/epaper-(.*)$/)
    if (match) {
      return match[1]
    }
  }

  if (pageType === 'imprintPage') {
    // match `/s/impressum-${regionSlug}`
    const match = location.pathname.match(/^\/s\/impressum-(.*)$/)
    if (match) {
      return match[1]
    }
  }

  if (pageType === 'contact') {
    // match `/s/ueber-uns-${regionSlug}`
    const match = location.pathname.match(/^\/s\/ueber-uns-(.*)$/)
    if (match) {
      return match[1]
    }
  }

  if (pageType === 'newsletter') {
    // match `/s/newsletter-${regionSlug}`
    const match = location.pathname.match(/^\/s\/newsletter-(.*)$/)
    if (match) {
      return match[1]
    }
  }

  return params.region
}

export const RegionContext = ({
  children,
  regionData,
}: {
  children: ReactNode
  regionData: FullRegionDataType
}) => {
  const regionSlug = useRegionParams()
  const regionHelpers = useMemo(() => regionsHelpers(regionData), [regionData])
  const [opened, handlers] = useDisclosure(false)

  const region = useMemo(() => {
    if (!regionSlug) {
      return {
        title: strings.regions.regions.oesterreich,
        slug: regionAustria,
      }
    }

    const regionTitle = strings.regions.regionBySlug({ slug: regionSlug })

    if (!regionTitle) {
      return {
        title: strings.regions.regions.oesterreich,
        slug: regionAustria,
      }
    }

    return {
      title: regionTitle,
      slug: regionSlug,
    }
  }, [regionSlug])

  return (
    <Region.Provider
      value={{
        region,
        opened,
        regionHelpers,
        openRegionMenu: handlers.open,
        closeRegionMenu: handlers.close,
      }}
    >
      {children}
    </Region.Provider>
  )
}

export const useRegion = () => {
  const { region, regionHelpers, opened, openRegionMenu, closeRegionMenu } = useContext(Region)

  return {
    regionHelpers,
    region,
    opened,
    openMenu: openRegionMenu,
    closeMenu: closeRegionMenu,
  }
}

/**
 * If the current region
 * - is Austria, returns undefined
 * - is a state, returns the current region
 * - is a district, returns the state of the current region
 *
 * @returns string | undefined
 */
export const useStateRegion = () => {
  const regionData = useRegion()
  if (!regionData.region?.slug) return undefined

  if (regionData.region?.slug === regionAustria) return undefined

  const parentRegion = regionData.regionHelpers?.getParentBySlug(regionData.region?.slug)

  if (!parentRegion?.slug) return undefined

  if (parentRegion.slug === regionAustria) return regionData.region

  return parentRegion
}
