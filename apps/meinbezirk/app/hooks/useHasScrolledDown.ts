import { useWindowEvent } from '@mantine/hooks'
import { useState } from 'react'

export const useHasScrolledDown = () => {
  const [hasScrolledDown, setHasScrolledDown] = useState(false)
  const [lastScrollY, setLastScrollY] = useState(0)

  const handler = () => {
    setHasScrolledDown(window.scrollY > lastScrollY)
    setLastScrollY(window.scrollY)
  }

  useWindowEvent('scroll', handler)

  return hasScrolledDown
}
