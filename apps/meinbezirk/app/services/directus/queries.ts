import { readItems } from '@rma-mono/directus-client'
import { directusClient } from './client'
import type { SerializeFrom } from '@remix-run/node'
import type { BasicText } from '@rma-mono/directus-client'
import { DIRECTUS_CACHE_TTL, getCache } from 'utils/cache'

export const getBasicText = async (slug: string): Promise<BasicText | null> => {
  const data = await directusClient.request(
    readItems('basic_text', {
      fields: ['*'],
      filter: {
        slug: {
          _eq: slug,
        },
      },
    }),
  )

  return data.length > 0 ? data[0] : null
}

export const getMenus = async (slugs: string[]) => {
  const cache = await getCache()
  const data = await cache.getWithFunction({
    key: 'menus',
    storeFunction: async () =>
      await directusClient.request(
        readItems('menus', {
          fields: ['*'],
          filter: {
            slug: {
              _in: slugs,
            },
          },
        }),
      ),
    ttl: DIRECTUS_CACHE_TTL,
  })

  if (data.length === 0) {
    return null
  }

  return data
}

export const getContactPage = async (state: string) => {
  const cache = await getCache()
  const cacheKey = `contact_page_${state}`

  const data = await cache.getWithFunction({
    key: cacheKey,
    storeFunction: async () =>
      await directusClient.request(
        readItems('contact_page', {
          fields: [
            '*',
            {
              tree: ['slug'],
            },
            {
              locations: ['*'],
            },
            {
              documents: [
                '*',
                {
                  contact_page_documents_id: [
                    '*',
                    {
                      file: ['*'],
                    },
                  ],
                },
              ],
            },
            {
              locations: [
                '*',
                {
                  people: ['*', { contact_page_person_id: ['*'] }],
                },
              ],
            },
          ],
          filter: {
            tree: {
              slug: {
                _eq: state,
              },
            },
          },
        }),
      ),
    ttl: DIRECTUS_CACHE_TTL,
  })

  return data.length > 0 ? data[0] : null
}

export type WeatherSponsoringType = SerializeFrom<Awaited<ReturnType<typeof getWeatherSponsoring>>>

export const getWeatherSponsoring = async (region: string) => {
  const data = await directusClient.request(
    readItems('weather_sponsoring', {
      fields: ['*', { sponsor_logo: ['*'] }],
      filter: {
        _and: [
          {
            region: {
              _eq: region,
            },
          },
          {
            is_active: {
              _eq: true,
            },
          },
        ],
      },
    }),
  )

  // querying dates via directus is not working as expected, so we need to do it here
  const sponsor = data.filter((sponsor) => {
    const now = new Date()
    const sponsorStart = sponsor.sponsor_start ? new Date(sponsor.sponsor_start) : null
    const sponsorEnd = sponsor.sponsor_end ? new Date(sponsor.sponsor_end) : null

    if (!sponsorStart || !sponsorEnd) {
      return false
    }

    return now >= sponsorStart && now <= sponsorEnd
  })

  return sponsor.length > 0 ? sponsor[0] : null
}
