import { Cache } from '@regionalmedienaustria/cache-util'

import { DirectusRegions } from '@rma-mono/rma-regions'

import { weatherQuery } from './weatherQuery'
import type { CurrentWeatherType } from './types'
import type { FeatureCollection, MultiPolygon } from 'geojson'
import { directusClient } from 'services/directus/client'
import { getCache } from 'utils/cache'

/**
 *
 * @param regionSlug - The SLUG of the region NOT the TITLE
 * @returns
 */
export async function getBoundsForRegion(
  regionSlug: string,
): Promise<FeatureCollection<MultiPolygon> | null> {
  const cache = await getCache()
  const regionResponse = await cache.getWithFunction({
    key: `bounds-${regionSlug}`,
    storeFunction: async () => {
      const directusRegions = new DirectusRegions(directusClient)

      return await directusRegions.getRegionBounds({
        regionSlug,
      })
    },
    ttl: Cache.CONSTANTS.ONE_DAY,
  })

  return regionResponse.bounds
}

export type DefaultCurrentWeatherType = Pick<
  CurrentWeatherType,
  'windSpeed' | 'humidity' | 'icon' | 'datetime'
>

export const buildWeatherQuery = ({
  region,
  isDistrict,
  forceCacheUpdate,
}: {
  region: string
  isDistrict?: boolean
  forceCacheUpdate?: boolean
}) => {
  return weatherQuery({
    location: region,
    forecastDays: isDistrict ? 15 : 1,
    include: isDistrict ? ['location', 'daily', 'hourly'] : ['location', 'daily', 'current'],
    pick: !isDistrict
      ? [
          'windSpeed',
          'humidity',
          'icon',
          'minTemp',
          'maxTemp',
          'datetime',
          'description',
          'shortDescription',
        ]
      : undefined,
    forceCacheUpdate,
  })
}
