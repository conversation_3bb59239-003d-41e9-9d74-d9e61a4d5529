export type DefaultIncludeType = ['current', 'daily', 'hourly', 'location']
export type IncludeType = DefaultIncludeType[number][]

export type DefaultPropertiesType = [
  'minTemp',
  'maxTemp',
  'humidity',
  'windSpeed',
  'icon',
  'location',
  'datetime',
  'dayTemp',
  'sunrise',
  'sunset',
  'pressure',
  'rain',
  'uvi',
  'windDeg',
  'day',
  'dayOfWeek',
  'clouds',
  'feelsLikeTemp',
  'temp',
  'description',
  'shortDescription',
]
export type PropertiesType = DefaultPropertiesType[number][]

export interface LocationType<TInclude extends IncludeType, TProperties extends PropertiesType> {
  current: 'current' extends TInclude[number]
    ? Pick<CurrentWeatherType, Extract<keyof CurrentWeatherType, TProperties[number]>>
    : never
  daily: 'daily' extends TInclude[number]
    ? DailyWeatherType<'hourly' extends TInclude[number] ? true : false, TProperties>[]
    : never
  location: 'location' extends TInclude[number]
    ? {
        name: string
      }
    : never
  description: string
  shortDescription: string
}

export interface BaseWeatherType {
  clouds: number
  datetime: number
  description: string
  humidity: number
  icon: string
  pressure: number
  rain: number
  uvi: number
  windDeg: number
  windSpeed: number
}

export interface CurrentWeatherType extends BaseWeatherType {
  feelsLikeTemp: number
  sunrise: number
  sunset: number
  temp: number
}

export interface DailyWeatherType<
  TIncludeHourly extends boolean | undefined,
  TProperties extends PropertiesType | undefined,
> extends BaseWeatherType {
  day: string
  dayOfWeek: string
  dayTemp: number
  maxTemp: number
  minTemp: number
  sunrise: number
  sunset: number
  hourly: TIncludeHourly extends true | undefined ? HourlyWeatherType[] : never
}

export type DailyWeatherWithHourlyType<TProperties extends PropertiesType | undefined> =
  DailyWeatherType<true, TProperties>

export interface HourlyWeatherType extends BaseWeatherType {
  feelsLikeTemp: number
  temp: number
}
