import type {
  DefaultIncludeType,
  PropertiesType,
  IncludeType,
  LocationType,
  DefaultPropertiesType,
} from 'services/weather/types'
import { API_GATEWAY, MEINBEZIRK_API_KEY } from 'config'

/**
 * @param location - The location to get the weather for
 * @param include - The top level properties to include in the response
 * @param omit - The properties to omit from the response
 * @param pick - The properties to pick from the response
 */
export const weatherQuery = async <
  TInclude extends IncludeType | undefined,
  TPick extends PropertiesType | undefined,
>({
  location,
  forecastDays,
  include,
  pick,
  forceCacheUpdate,
}: {
  location: string
  include?: TInclude
  forecastDays?: number
  pick?: TPick
  forceCacheUpdate?: boolean
}): Promise<{
  success: boolean
  description: string
  shortDescription: string
  locations: LocationType<
    TInclude extends undefined ? DefaultIncludeType : TInclude,
    TPick extends PropertiesType ? TPick : DefaultPropertiesType
  >[]
} | null> => {
  if (!MEINBEZIRK_API_KEY) {
    throw new Error('Weather API key is missing')
  }

  const apiUrl = `${API_GATEWAY}/v1/weather/${location}`
  const url = new URL(apiUrl)
  const params = new URLSearchParams(url.search)

  const includePick = (include ?? defaultInclude).join(',')
  const pickParam = (pick ?? defaultProperties).join(',') + ',' + includePick

  params.set('key', MEINBEZIRK_API_KEY)
  params.set('pick', pickParam)
  forecastDays && params.set('forecastDays', forecastDays.toString())
  forceCacheUpdate && params.set('forceCacheUpdate', 'true')
  url.search = params.toString()

  try {
    const response = await fetch(url.toString(), {
      headers: {
        'Content-Type': 'application/json',
        Referer: 'https://www.meinbezirk.at/',
      },
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message)
    }

    return await response.json()
  } catch (error) {
    console.error(error)
    return null
  }
}

const defaultInclude = ['current', 'daily', 'hourly', 'location']

const defaultProperties = [
  'minTemp',
  'maxTemp',
  'humidity',
  'windSpeed',
  'icon',
  'location',
  'datetime',
  'dayTemp',
  'sunrise',
  'sunset',
  'pressure',
  'rain',
  'uvi',
  'windDeg',
  'day',
  'dayOfWeek',
  'clouds',
  'feelsLikeTemp',
  'temp',
  'description',
  'shortDescription',
  'snow',
]
