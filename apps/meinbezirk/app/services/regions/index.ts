import { DirectusRegions } from '@rma-mono/rma-regions'
import type { SerializeFrom } from '@remix-run/node'

import type { RegionHelpersType } from 'utils/regionsHelpers'
import { directusClient } from 'services/directus/client'
import { DIRECTUS_CACHE_TTL, getCache } from 'utils/cache'
import { regionsHelpers } from 'utils/regionsHelpers'

// First of all, fuck you Remix (and/or Directus) for making me do this shit
export type FullRegionDataType = SerializeFrom<Awaited<ReturnType<typeof getRegionData>>>
export type RegionTreeChildrenType = FullRegionDataType['children']

export type RegionDataType = SerializeFrom<
  Awaited<ReturnType<RegionHelpersType['getRegionBySlug']>>
>

export const getRegionData = async () => {
  return (await getRegions()) ?? {}
}

/**
 * Wrapper function which fetches all regions and returns them with
 * some helper functions
 */
export const allRegions = async () => {
  const regions = await getRegionData()

  return regionsHelpers(regions)
}

export type AllRegions = Awaited<ReturnType<typeof allRegions>>

const getRegions = async () => {
  const cache = await getCache()
  const data = await cache.getWithFunction({
    key: 'allRegions',
    storeFunction: async () => {
      const directusRegions = new DirectusRegions(directusClient)

      return await directusRegions.fetchRegionTree()
    },
    ttl: DIRECTUS_CACHE_TTL,
  })

  if (!data) {
    throw new Error('No region data found')
  }

  return data
}
