import { API_GATEWAY, MEINBEZIRK_API_KEY } from 'config'

export interface ArticleItemType {
  id?: number
  visit_count?: number
  rank: number
  url: string
  kicker: string
  title: string
}

export interface ArticleItemsType {
  success: boolean
  page?: number
  page_size?: number
  total_pages?: number
  total_results?: number
  data: ArticleItemType[]
}

export const getMostReadArticles = async ({
  region,
  page = 1,
  pageSize = 6,
}: {
  region?: string
  page?: number
  pageSize?: number
}) => {
  const url = `${API_GATEWAY}/v1/search/most-read/`
  const params: {
    page: number
    pageSize: number
    userSegments: string
    key: string
    [key: string]: string | number
  } = {
    page,
    pageSize,
    userSegments:
      'redakteur,oesterreich,freier-mitarbeiter,regionaut,kommerzieller-regionaut,ib-schreiber',
    key: MEINBEZIRK_API_KEY,
  }

  const queryString = Object.keys(params)
    .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
    .join('&')

  const fullUrl = `${url}${region}?${queryString}`

  try {
    const response = await fetch(fullUrl, {
      headers: {
        'Content-Type': 'application/json',
        Referer: 'https://www.meinbezirk.at/',
      },
    })
    if (!response.ok) {
      const error = (await response.json()) as { message: string; status: number }
      console.error(`${response.status} – ${error.message}`)

      return []
    }

    const data = (await response.json()) as ArticleItemsType

    return data.data ?? []
  } catch (error) {
    return []
  }
}
