import { breadcrumbStrings } from './breadcrumb'
import { contactStrings } from './contact'
import { errorStrings } from './errors'
import { globalStrings } from './global'
import { regionStrings } from './regions'
import { weatherStrings } from './weather'

export const strings = {
  global: globalStrings,
  regions: regionStrings,
  weather: weatherStrings,
  breadcrumb: breadcrumbStrings,
  contact: contactStrings,
  errors: errorStrings,
} as const
