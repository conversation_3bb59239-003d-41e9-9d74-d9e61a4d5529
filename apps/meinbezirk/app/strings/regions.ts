import { regions } from '@rma-mono/rma-regions'
import { getByKey } from 'utils/getByKey'

export type RegionNameType =
  | (typeof regionStrings.regions)[keyof typeof regionStrings.regions]
  | typeof regionStrings.oesterreichLabel

export const regionStrings = {
  oesterreichLabel: 'Bezirk auswählen',
  regions: regions,
  regionBySlug: ({ slug }: { slug: string }) => getByKey(slug, regions),
} as const
