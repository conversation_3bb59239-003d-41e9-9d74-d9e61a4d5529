import { describe, expect, it } from 'vitest'

import FuelArticles from '../src/updateFuelArticles.js'
import UpdatePharmacyArticles from '../src/updatePharmacyArticles.js'

describe('articleUpdate-service', () => {
  describe('refresh fuel articles, dryrun', () => {
    it('should not throw error', async () => {
      const articles = new FuelArticles()
      articles.updateMany([])
      expect(true, 'function passed...')
    })
  })

  describe('refresh pharmacy articles, dryrun', () => {
    it('should not throw error', async () => {
      const articles = new UpdatePharmacyArticles()
      articles.updateMany([])
      expect(true, 'function passed...')
    })
  })
})
/*
describe('articleUpdate-service', () => {
  describe('date', () => {
    it('should not throw error', async () => {
      const now = new Date()
      let isoString = now.toISOString()
      isoString = isoString.slice(0, isoString.indexOf('.')) + '+01:00'
      let startDate = new Date(now)
      startDate.setFullYear(now.getFullYear() - 10)
      let endDate = new Date(now)
      endDate.setFullYear(now.getFullYear() - 1)

      logger.info(now)

      assert.ok(isoString, 'function passed...')
    })
  })
})

describe('fuel articles', () => {
  describe('date offset', () => {
    it('should not throw error', async () => {
      const articles = new FuelArticles()
      const date = articles.getRefreshDateString()
      logger.info(date)

      assert.ok(date, 'function passed...')
    })
  })
})

describe('news articles', () => {
  describe('fetch', () => {
    it('should not throw error', async () => {
      const articles = new NewsArticles()
      const result = await articles.fetchArticles()
      logger.info(result)

      assert.ok(result, 'function passed...')
    })
  })
})

describe('news articles', () => {
  describe('refresh', () => {
    it('should not throw error', async () => {
      const articles = new NewsArticles()
      const result = await articles.refresh(true)
      logger.info(result)

      assert.ok(result, 'function passed...')
    })
  })
})

describe('news articles', () => {
  describe('refresh', () => {
    it('should not throw error', async () => {
      const articles = new NewsArticles()
      let pass = true
      try {
        await articles.update()
      } catch (error) {
        pass = false
      }

      assert.ok(pass, 'function passed...')
    })
  })
})

describe('states', () => {
  describe('check', () => {
    it('should not throw error', async () => {
      let pass = true
      for (let i = 0; i < STATES.length; i++) {
        if (STATES[i].id < 1) {
          pass = false
        }
      }
      assert.ok(pass, 'function passed...')
    })
  })
})

describe('obsolete event', () => {
  describe('check', () => {
    it('should not throw error', async () => {
      let pass = true

      const events = new ObsoleteEvents()
      events.refresh(true)

      assert.ok(pass, 'function passed...')
    })
  })
})*/
