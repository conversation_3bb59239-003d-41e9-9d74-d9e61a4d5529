/* eslint-disable @typescript-eslint/dot-notation */

import { logger } from '@regionalmedienaustria/microservice-utils'
import { describe, expect, it } from 'vitest'

import { STATES } from '../src/states.js'
import FuelArticles from '../src/updateFuelArticles.js'
import NewsArticles from '../src/updateNewsArticles.js'

import article1False from './testdata/fuelFalse1.json'
import article2False from './testdata/fuelFalse2.json'
import article1True from './testdata/fuelTrue1.json'
import article2True from './testdata/fuelTrue2.json'
import article3True from './testdata/fuelTrue3.json'
import article4True from './testdata/fuelTrue4.json'

describe('articleUpdate-service', () => {
  describe('should be true for fuel container', () => {
    it('should not throw error', async () => {
      const articles = new FuelArticles()
      expect(articles['hasFuelContainer'](article1True)).toBeTruthy()
      expect(articles['hasFuelContainer'](article2True)).toBeTruthy()
      expect(articles['hasFuelContainer'](article3True)).toBeTruthy()
      expect(articles['hasFuelContainer'](article4True)).toBeTruthy()
    })
  })

  describe('should be false for fuel container', () => {
    it('should not throw error', async () => {
      const articles = new FuelArticles()
      expect(articles['hasFuelContainer'](article1False)).toBeFalsy()
      expect(articles['hasFuelContainer'](article2False)).toBeFalsy()
    })
  })

  describe('should be true', () => {
    it('should not throw error', async () => {
      let pass = true

      for (let i = 0; i < STATES.length; i++) {
        if (STATES[i].id < 0) {
          pass = false
        }
      }

      expect(pass, 'function passed...')
    })
  })

  describe('articleUpdate-service', () => {
    describe('date', () => {
      it('should not throw error', async () => {
        const now = new Date()

        let isoString = now.toISOString()

        isoString = isoString.slice(0, isoString.indexOf('.')) + '+01:00'
        let startDate = new Date(now)

        startDate.setFullYear(now.getFullYear() - 10)
        let endDate = new Date(now)

        endDate.setFullYear(now.getFullYear() - 1)
        logger.info(now)

        expect(isoString, 'function passed...')
      })
    })
  })

  describe('fuel articles', () => {
    describe('date offset', () => {
      it('should not throw error', async () => {
        const articles = new FuelArticles()

        const date = articles.getRefreshDateString()

        logger.info(date)

        expect(date, 'function passed...')
      })
    })
  })

  describe('news articles', () => {
    describe('fetch', () => {
      it('should not throw error', async () => {
        const articles = new NewsArticles()

        const result = await articles.fetchArticles()

        logger.info(result)

        expect(result, 'function passed...')
      })
    })
  })

  describe('news articles', () => {
    describe('refresh', () => {
      it('should not throw error', async () => {
        const articles = new NewsArticles()

        const result = await articles.refresh(true)

        logger.info(result)

        expect(result, 'function passed...')
      })
    })
  })

  describe('news articles', () => {
    describe('refresh', () => {
      it('should not throw error', async () => {
        const articles = new NewsArticles()

        let pass = true

        try {
          await articles.updateMany()
        } catch (error) {
          pass = false
        }

        expect(pass, 'function passed...')
      })
    })
  })
})
