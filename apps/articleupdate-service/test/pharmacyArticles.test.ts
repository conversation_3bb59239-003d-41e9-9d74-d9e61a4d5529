/* eslint-disable @typescript-eslint/dot-notation */

import { logger } from '@regionalmedienaustria/microservice-utils'
import { describe, expect, it, vi } from 'vitest'

import { PeiqApiWrapper } from '../src/peiqApiWrapper.js'
import UpdatePharmacyArticles from '../src/updatePharmacyArticles.js'

import type { PeiqApiWrapper as PeiqApiWrapperType } from '@regionalmedienaustria/peiq-api-wrapper'

// Mock article with pharmacy widget
const pharmacyArticleTrue = {
  id: '7053122',
  embeds: {
    embedcode: {
      'embed-1': {
        content: '<div data-widget="pharmacy">Pharmacy content</div>',
      },
    },
  },
} as unknown as PeiqApiWrapperType.ArticleData

// Mock article without pharmacy widget
const pharmacyArticleFalse = {
  id: '7053123',
  embeds: {
    embedcode: {
      'embed-1': {
        content: '<div>Regular content without pharmacy widget</div>',
      },
    },
  },
} as unknown as PeiqApiWrapperType.ArticleData

describe('UpdatePharmacyArticles', () => {
  describe('hasPharmacyContainer', () => {
    it('should return true for articles with pharmacy widget', () => {
      const pharmacyArticles = new UpdatePharmacyArticles()
      expect(pharmacyArticles['hasPharmacyContainer'](pharmacyArticleTrue)).toBeTruthy()
    })

    it('should return false for articles without pharmacy widget', () => {
      const pharmacyArticles = new UpdatePharmacyArticles()
      expect(pharmacyArticles['hasPharmacyContainer'](pharmacyArticleFalse)).toBeFalsy()
    })
  })

  describe('getRefreshDateString', () => {
    it('should return a formatted timestamp set to 6:00 AM', () => {
      const pharmacyArticles = new UpdatePharmacyArticles()
      const refreshDate = pharmacyArticles.getRefreshDateString()

      // Verify format
      expect(refreshDate).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\+00:00$/)

      // Verify time is set to 6:00 AM (with timezone adjustments)
      const date = new Date(refreshDate)
      const hours = date.getUTCHours()
      expect(hours).toBe(5) // If using UTC+1, this would be 6, adjust as needed
      expect(date.getUTCMinutes()).toBe(0)
      expect(date.getUTCSeconds()).toBe(0)

      logger.info(`Refresh date: ${refreshDate}`)
    })
  })

  describe('refresh', () => {
    it('should not throw error in dry run mode', async () => {
      // Mock the API response
      vi.spyOn(PeiqApiWrapper.ArticleApi, 'fetchArticles').mockResolvedValue({
        data: [pharmacyArticleTrue],
        total_pages: 1,
        total_results: 1,
      } as any)

      const pharmacyArticles = new UpdatePharmacyArticles()
      const result = await pharmacyArticles.refresh(true)

      expect(result).toHaveLength(1)
      expect(result[0]).toBe(Number(pharmacyArticleTrue.id))
    })
  })

  describe('updateSingleArticle', () => {
    it('should retry on failure and eventually succeed', async () => {
      // Mock API to fail on first attempt, succeed on second
      let attempts = 0
      vi.spyOn(PeiqApiWrapper.ArticleApi, 'updateArticle').mockImplementation(async () => {
        attempts++
        if (attempts === 1) {
          return { success: false, status: 500, info: 'Server error' }
        } else {
          return { success: true, status: 204, info: 'Success' }
        }
      })

      const pharmacyArticles = new UpdatePharmacyArticles()
      const result = await pharmacyArticles['updateSingleArticle'](
        7053122,
        '2025-03-25T06:00:00+00:00',
      )

      expect(result).toBeTruthy()
      expect(attempts).toBe(2) // Verify it retried
    })
  })
})
