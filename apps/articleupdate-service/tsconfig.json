{"extends": "@rma-mono/tsconfig-shared/base.json", "compilerOptions": {"lib": ["es2022"], "outDir": "lib", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "typeRoots": ["@types", "node_modules/@types"], "rootDirs": ["./src/", "./test/"], "inlineSourceMap": true, "inlineSources": true, "skipLibCheck": true, "emitDecoratorMetadata": true, "downlevelIteration": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@test/*": ["test/*"]}}, "include": ["./src/**/*.ts", "./test/**/*", "./src/locales/*.json", "vitest.config.ts"], "exclude": ["node_modules", "types", "lib", "**/*.spec.ts"]}