[{"_id": "c6b9167a-150a-4e7c-a3a0-2939bb9ed9ce", "colId": "history", "containerId": "", "name": "localhost:8080/v1/articleupdateservice/refresh?articles=fuel&dryrun=true&key=asd", "url": "localhost:8080/v1/articleupdateservice/refresh?articles=fuel&dryrun=true&key=asd", "method": "GET", "sortNum": 0, "created": "2023-09-26T08:19:33.331Z", "modified": "2023-09-26T08:19:33.331Z", "headers": [], "params": [{"name": "articles", "value": "fuel", "isPath": false}, {"name": "dryrun", "value": "true", "isPath": false}, {"name": "key", "value": "asd", "isPath": false}], "tests": []}, {"_id": "e180f046-6fd1-4730-81e0-8014952c8868", "colId": "history", "containerId": "", "name": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=true&offset=0&key=asd", "url": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=true&offset=0&key=asd", "method": "GET", "sortNum": 0, "created": "2023-09-26T08:23:03.179Z", "modified": "2023-09-26T08:23:03.179Z", "headers": [], "params": [{"name": "type", "value": "article", "isPath": false}, {"name": "dryrun", "value": "true", "isPath": false}, {"name": "offset", "value": "0", "isPath": false}, {"name": "key", "value": "asd", "isPath": false}], "tests": []}, {"_id": "c073992d-d723-4413-b756-a3d89e01b781", "colId": "history", "containerId": "", "name": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=true&offset=3650&key=asd", "url": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=true&offset=3650&key=asd", "method": "GET", "sortNum": 0, "created": "2023-09-26T08:24:58.013Z", "modified": "2023-09-26T08:24:58.013Z", "headers": [], "params": [{"name": "type", "value": "article", "isPath": false}, {"name": "dryrun", "value": "true", "isPath": false}, {"name": "offset", "value": "3650", "isPath": false}, {"name": "key", "value": "asd", "isPath": false}], "tests": []}, {"_id": "994e4521-c3ee-4c68-8c40-05419096519a", "colId": "history", "containerId": "", "name": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=true&offsetDays=365&key=asd", "url": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=true&offsetDays=365&key=asd", "method": "GET", "sortNum": 0, "created": "2023-09-26T08:31:39.100Z", "modified": "2023-09-26T08:31:39.100Z", "headers": [], "params": [{"name": "type", "value": "article", "isPath": false}, {"name": "dryrun", "value": "true", "isPath": false}, {"name": "offsetDays", "value": "365", "isPath": false}, {"name": "key", "value": "asd", "isPath": false}], "tests": []}, {"_id": "5f172934-a2d8-405d-85bf-accc4cffd47d", "colId": "history", "containerId": "", "name": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=false&offsetDays&key=asd", "url": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=false&offsetDays&key=asd", "method": "GET", "sortNum": 0, "created": "2023-09-26T08:37:50.433Z", "modified": "2023-09-26T08:37:50.433Z", "headers": [], "params": [{"name": "type", "value": "article", "isPath": false}, {"name": "dryrun", "value": "false", "isPath": false}, {"name": "offsetDays", "value": "", "isPath": false}, {"name": "key", "value": "asd", "isPath": false}], "tests": []}, {"_id": "6efb88ce-8d93-48bc-a828-77825b21488f", "colId": "history", "containerId": "", "name": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=false&offsetDays=0&key=asd", "url": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=false&offsetDays=0&key=asd", "method": "GET", "sortNum": 0, "created": "2023-09-26T08:57:36.558Z", "modified": "2023-09-26T08:57:36.558Z", "headers": [], "params": [{"name": "type", "value": "article", "isPath": false}, {"name": "dryrun", "value": "false", "isPath": false}, {"name": "offsetDays", "value": "0", "isPath": false}, {"name": "key", "value": "asd", "isPath": false}], "tests": []}, {"_id": "78932002-3800-4a7b-87b9-af85b1535776", "colId": "history", "containerId": "", "name": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=true&offsetDays=0&key=asd", "url": "localhost:8080/v1/articleupdateservice/cleanup?type=events&dryrun=true&offsetDays=60&key=asd", "method": "GET", "sortNum": 0, "created": "2023-09-26T09:17:41.975Z", "modified": "2023-11-21T09:32:56.216Z", "headers": [], "params": [{"name": "type", "value": "events", "isPath": false}, {"name": "dryrun", "value": "true", "isPath": false}, {"name": "offsetDays", "value": "60", "isPath": false}, {"name": "key", "value": "asd", "isPath": false}], "tests": []}, {"_id": "94757361-f6f1-48e4-8258-2203f737a326", "colId": "history", "containerId": "", "name": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=true&offsetDays=30&key=asd", "url": "localhost:8080/v1/articleupdateservice/cleanup?type=article&dryrun=true&offsetDays=3650&key=asd", "method": "GET", "sortNum": 0, "created": "2023-09-26T09:17:52.458Z", "modified": "2023-09-28T14:10:17.436Z", "headers": [], "params": [{"name": "type", "value": "article", "isPath": false}, {"name": "dryrun", "value": "true", "isPath": false}, {"name": "offsetDays", "value": "3650", "isPath": false}, {"name": "key", "value": "asd", "isPath": false}], "tests": []}, {"_id": "09ee26fd-23df-4f3e-82d7-c48f60ab1915", "colId": "history", "containerId": "", "name": "localhost:8080/v1/articleupdateservice/cleanup?type=event&dryrun=true&offsetDays=0&key=asd", "url": "localhost:8080/v1/articleupdateservice/cleanup?type=articles&dryrun=true&offsetDays=60&key=asd", "method": "GET", "sortNum": 0, "created": "2023-09-26T09:51:50.094Z", "modified": "2023-11-21T09:32:29.662Z", "headers": [], "params": [{"name": "type", "value": "articles", "isPath": false}, {"name": "dryrun", "value": "true", "isPath": false}, {"name": "offsetDays", "value": "60", "isPath": false}, {"name": "key", "value": "asd", "isPath": false}, {"name": "rollback", "value": "true", "isDisabled": true, "isPath": false}], "tests": []}, {"_id": "09ee26fd-23df-4f3e-82d7-c48f60ab1915", "colId": "history", "containerId": "", "name": "localhost:8080/v1/articleupdateservice/cleanup?type=event&dryrun=true&offsetDays=0&key=asd", "url": "localhost:8080/v1/articleupdateservice/cleanup?type=events&dryrun=true&offsetDays=3650&key=asd", "method": "GET", "sortNum": 0, "created": "2023-09-26T09:51:50.094Z", "modified": "2023-11-07T13:50:34.794Z", "headers": [], "params": [{"name": "type", "value": "events", "isPath": false}, {"name": "dryrun", "value": "true", "isPath": false}, {"name": "offsetDays", "value": "3650", "isPath": false}, {"name": "key", "value": "asd", "isPath": false}, {"name": "rollback", "value": "true", "isDisabled": true, "isPath": false}], "tests": []}]