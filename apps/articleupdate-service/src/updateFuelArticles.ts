import { logger } from '@regionalmedienaustria/microservice-utils'

import { PeiqApiWrapper } from './peiqApiWrapper.js'

import type { UpdateArticles } from './updateArticles.js'
import type {
  <PERSON>eiq<PERSON><PERSON><PERSON>ara<PERSON>,
  PeiqApiWrapper as PeiqApiWrapperType,
} from '@regionalmedienaustria/peiq-api-wrapper'

const FUEL_IDS = [
  5898517, 5898514, 5857624, 5849095, 5849092, 5849089, 5849080, 5849074, 5849068, 5849065, 5849056,
  5849053, 5849044, 5849041, 5849038, 5849029, 5849014, 5849011, 5849005, 5848987, 5848954, 5848951,
  5848948, 5848942, 5848939, 5848927, 5848924, 5848912, 5848897, 5848894, 5848765, 5848135, 5848132,
  5848129, 5848123, 5848117, 5848114, 5848108, 5848102, 5822188, 5822182, 5822164, 5822155, 5822020,
  5821969, 5821957, 5821951, 5821906, 5821870, 5821738, 5821723, 5821582, 5821576, 5821489, 5794537,
  5794519, 5794483, 5794441, 5794387, 5794357, 5794327, 5794294, 5794234, 5794201, 5794174, 5794105,
  5784748, 5775898, 5775877, 5775856, 5775832, 5775817, 5775706, 5775628, 5771248, 5771239, 5771203,
  5771140, 5770954, 5770930, 5770417, 5770102, 5748325, 5748319, 5748307, 5748292, 5748280, 5748268,
  5748262, 5748214, 5748175, 5748142, 5748130, 5748112, 5748016, 5642977, 5642962, 5642905, 5603455,
  5590702, 5548363,
]

export default class UpdateFuelArticles implements UpdateArticles {
  private getPeiqApiParams(): PeiqApiParams {
    const today = new Date()
    today.setDate(today.getDate() - 4)
    const isoString = today.toISOString().slice(0, 10) // + 'T00:00:00+00:00'
    return {
      page: 1,
      pageSize: 75,
      dateStart: isoString,
      userSegments: ['oesterreich', 'redakteur'],
      searchString: 'Tankstellen',
      sortBy: 'id',
    }
  }

  /**
   * Fetches a list of all two days old articles
   * @returns a list of fuel article Ids
   */
  async fetchArticles(): Promise<number[]> {
    const apiParams = this.getPeiqApiParams()
    logger.info(`Article dateStart fetch date: ${apiParams.dateStart}`)
    let totalPages = 1
    const articleIds: number[] = []
    apiParams.page = 1

    while (apiParams.page <= totalPages) {
      const result: PeiqApiWrapperType.PeiqArticlesResult =
        await PeiqApiWrapper.ArticleApi.fetchArticles(apiParams, false, false, false)

      totalPages = result.total_pages

      logger.info(
        `Page ${apiParams.page} from ${totalPages} with ${result.total_results} total results requested.`,
      )

      for (let i = 0; i < result.data.length; i++) {
        const article: any = result.data[i]
        if (this.hasFuelContainer(article)) {
          articleIds.push(article.id)
        } else if (FUEL_IDS.includes(+article.id)) {
          logger.info(`Miss! Article ID found in FUEL_IDS List. ${article.id}`)
        }
      }
      apiParams.page++
    }

    // sort array and remove duplicates
    const cleanedIds = await this.uniq(articleIds)
    logger.info(`Total articles found: ${cleanedIds.length}`)

    return cleanedIds
  }

  private hasFuelContainer(article: any): boolean {
    const codes = article.embeds?.embedcode || {}
    const fuelCodes = Object.entries(codes).filter(
      (data: any) =>
        data[1]?.content?.replaceAll("'", '"').indexOf('<div id="fuelcontainer">') !== -1,
    )
    return fuelCodes.length >= 1
  }

  private async uniq(arr: number[]) {
    const sortedIds: number[] = arr.sort((n1, n2) => n1 - n2).reverse()
    const filtered = sortedIds.filter(function (item, pos, ary) {
      return !pos || item !== ary[pos - 1]
    })
    const missing = FUEL_IDS.filter((item) => filtered.indexOf(item) < 0)
    if (missing.length > 0) {
      logger.info(`Missing IDs: ${missing}. Adding them to article list.`)
      await this.logMissing(missing)
      filtered.push(...missing)
      logger.info(`New amount of articles marked for refreshing: ${filtered.length}`)
    }
    return filtered
  }

  private async logMissing(articleIds: number[]) {
    try {
      for (let i = 0; i < articleIds.length; i++) {
        const article = await PeiqApiWrapper.ArticleApi.fetchArticle(articleIds[i])
        logger.info(article)
      }
    } catch (error) {
      logger.error(error)
    }
  }

  /**
   * Updates the published date for a list of article ids
   * @param articleIds - A list of articles
   */
  async updateMany(articleIds: number[]): Promise<void> {
    const refreshDateString = this.getRefreshDateString()
    logger.info(`Article refresh date: ${refreshDateString}`)
    let remaining = articleIds.length
    for (let i = 0; i < articleIds.length; i++) {
      try {
        const articleData: PeiqApiWrapperType.ArticleUpdateParams = {
          articleId: articleIds[i],
          updateParams: {
            published: refreshDateString,
          },
        }
        const result = await PeiqApiWrapper.ArticleApi.updateArticle(articleData)
        remaining -= 1
        if (result.status === 204) {
          logger.info(
            `Updating article ${articleIds[i]} succeeded. Articles remaining: ${remaining}`,
          )
        } else {
          logger.info(
            `Updating article ${articleIds[i]} failed. Info: ${result.status} ${result.info} - Articles remaining: ${remaining}`,
          )
        }
      } catch (error) {
        remaining -= 1
        logger.info(
          `Error while updating article ${articleIds[i]}. Info: ${error} - Articles remaining: ${remaining}`,
        )
      }
    }
  }

  /**
   * Refreshes fuel articles
   * @param dryrun - Won't update articles when set to true
   */
  async refresh(dryrun: boolean): Promise<number[]> {
    logger.info('Fuel-Articles refresh run start.')
    const articleIds = await this.fetchArticles()

    if (!dryrun) {
      await this.updateMany(articleIds)
    } else {
      logger.info(
        `DRYRUN - Total Articles marked for refreshing: ${
          articleIds.length
        }. Date: ${this.getRefreshDateString()}`,
      )
      logger.info(`DRYRUN - Article marked for refreshing: ${articleIds.join(',')}`)
    }
    logger.info('Fuel-Articles refresh run finished.')
    return articleIds
  }

  public getRefreshDateString(): string {
    const now = new Date()
    now.setHours(now.getHours() + 2)
    let isoString = now.toISOString()
    isoString = isoString.slice(0, isoString.indexOf('.')) + '+00:00'
    return isoString
  }

  rollback(articleIds: number[]): void {
    logger.debug(articleIds)
  }
}
