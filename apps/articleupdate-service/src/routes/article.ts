import { controller } from '@regionalmedienaustria/microservice-utils'
import express from 'express'

import ArticleUpdateController from '@/controllers/articleUpdateController.js'

const router = express.Router()

router.get('/refresh', controller.wrapController(ArticleUpdateController.refresh))

router.get('/cleanup', controller.wrapController(ArticleUpdateController.cleanup))

export default router
