import type { app } from '@regionalmedienaustria/microservice-utils'

const config: app.baseConfig.Serverless & { PEIQ_CLIENT_ID: string; PEIQ_CLIENT_SECRET: string } = {
  machine: process.env.MACHINE || 'local',
  port: process.env.PORT || '8080',
  serviceName: process.env.serviceName || 'articleUpdate-service',
  serviceApiKey: '',
  PEIQ_CLIENT_ID: process.env.PEIQ_CLIENT_ID || '',
  PEIQ_CLIENT_SECRET: process.env.PEIQ_CLIENT_SECRET || '',
}

export default config
