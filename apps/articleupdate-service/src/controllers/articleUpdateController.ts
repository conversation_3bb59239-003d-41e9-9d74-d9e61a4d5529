import { requestUtil } from '@regionalmedienaustria/microservice-utils'

import ArticleUpdateService from '@/services/articleUpdateService.js'

import type { Request, Response } from 'express'

export default class ArticleUpdateController {
  static async refresh(req: Request, res: Response) {
    const articles = requestUtil.getParameter(req, 'articles', '')
    const dryrun = requestUtil.getParameter(req, 'dryrun', false)

    const responseData = await ArticleUpdateService.refreshArticles(articles, dryrun)
    res.status(200).send(responseData)
  }

  static async cleanup(req: Request, res: Response) {
    const articleType = requestUtil.getParameter(req, 'type', '')
    const dryrun = requestUtil.getParameter(req, 'dryrun', false)
    const offsetDays = +requestUtil.getParameter(req, 'offsetDays', 0)
    const rollback = requestUtil.getParameter(req, 'rollback', false)

    if (['articles', 'events'].includes(articleType)) {
      if (rollback) {
        const articleIds = await ArticleUpdateService.getArticleIdsFromCSV(articleType)
        const responseData = await ArticleUpdateService.rollback(articleType, articleIds)
        res.status(200).send(responseData)
      } else {
        const responseData = await ArticleUpdateService.cleanup(articleType, dryrun, offsetDays)
        res.status(200).send(responseData)
      }
    } else {
      res.status(400).send('Invalid "type" parameter value. Must be either "articles" or "events".')
    }
  }
}
