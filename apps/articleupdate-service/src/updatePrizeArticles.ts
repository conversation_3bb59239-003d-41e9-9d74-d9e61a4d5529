import { logger } from '@regionalmedienaustria/microservice-utils'

import { PeiqApiWrapper } from './peiqApiWrapper.js'
import ArticleUpdateService from './services/articleUpdateService.js'
import { STATES } from './states.js'

import type { FetchArticleParams, UpdateArticles } from './updateArticles.js'
import type {
  PeiqApiParams,
  PeiqApiWrapper as PeiqApiWrapperType,
} from '@regionalmedienaustria/peiq-api-wrapper'

const DAYS_OF_YEAR = 365

export default class UpdatePrizeArticles implements UpdateArticles {
  async refresh(dryrun: boolean): Promise<number[]> {
    logger.info('Prize-Articles refresh run start.')
    logger.debug(`dryrun: ${dryrun}`)
    return []
  }

  async cleanup(offsetDays: number, dryrun: boolean) {
    const timestamp = new Date().toISOString()

    for (let i = 0; i < STATES.length; i++) {
      logger.info(`Fetching prize articles from ${STATES[i].name}`)

      const articleIds = await this.fetchArticles({
        locationId: STATES[i].id,
        offsetDays: offsetDays,
      })
      logger.info(`${articleIds.length} prize articles found.`)

      if (!dryrun) {
        await this.updateMany(articleIds, STATES[i].name)
      } else {
        logger.info(`DRYRUN - Updating ${articleIds.length} from ${STATES[i].name}`)

        for (let y = 0; y < articleIds.length; y++) {
          ArticleUpdateService.writeToLog('articles', articleIds[y], STATES[i].name, timestamp)
        }
        logger.info(`${articleIds.toString()}`)
      }
    }
  }

  /**
   *
   * @param offsetDays
   * @returns
   */
  async fetchArticles(
    params: FetchArticleParams /*locationId: number, offsetDays: number*/,
  ): Promise<number[]> {
    const offset = params.offsetDays ?? 0
    const isoDateStart = ArticleUpdateService.getDateIsoString(DAYS_OF_YEAR + 1 + offset)
    const isoDateEnd = ArticleUpdateService.getDateIsoString(DAYS_OF_YEAR)
    const apiParams: PeiqApiParams = {
      page: 1,
      pageSize: 100,
      dateStart: isoDateStart,
      dateEnd: isoDateEnd,
      tags: ['Gewinnspiel'],
      locationId: params.locationId,
      sortDir: 'asc',
    }

    logger.info(`Article dateStart fetch date: ${apiParams.dateStart}`)
    logger.info(`Article dateEnd fetch date: ${apiParams.dateEnd}`)

    let totalPages = 1
    const articleIds: number[] = []

    try {
      do {
        const result = await PeiqApiWrapper.ArticleApi.fetchArticles(
          apiParams,
          false,
          false,
          false,
          false,
        )
        totalPages = result.total_pages
        logger.info(`Page ${apiParams.page} from ${totalPages} requested.`)

        if (result.data) {
          result.data.forEach((article) => {
            if (
              +article.image_count > 0 &&
              +article.image_count < 4 &&
              !article.static_tags.includes('commercial')
            ) {
              articleIds.push(+article.id)
            }
          })
        }
        apiParams.page = (apiParams.page || 1) + 1
      } while (apiParams.page <= totalPages)
    } catch (error) {
      logger.error(error)
    }

    return articleIds
  }

  /**
   *
   * @param articleIds A List of article IDs
   * @param peiqImage A Peiq image object
   */
  async updateMany(articleIds: number[], state: string): Promise<void> {
    const timestamp = new Date().toISOString()

    const articleQueue: Promise<{ success: boolean; status?: number; info: any }>[] = []
    let updated = 0

    try {
      for (let i = 0; i < articleIds.length; i++) {
        const article: PeiqApiWrapperType.ArticleUpdateParams = {
          articleId: articleIds[i],
          updateParams: {
            status: 'rejected',
          },
        }

        articleQueue.push(PeiqApiWrapper.ArticleApi.updateArticle(article))
      }

      const results = await Promise.all(articleQueue)

      for (let i = 0; i < results.length; i++) {
        if (results[i].status !== 204) {
          logger.error(
            `Article ${articleIds[i]} update failed. ${results[i].status} - ${results[i].info} `,
          )
        } else {
          updated++
        }
      }

      logger.info(`${updated} ${state} articles updated at ${timestamp}`)
    } catch (error) {
      logger.error(error)
    }
  }

  async rollback(articleIds: number[]): Promise<void> {
    logger.info(`Rolling back ${articleIds.length} prize articles.`)

    const updated: number[] = []
    const failed: number[] = []
    const articleQueue: Promise<{ success: boolean; status?: number; info: any }>[] = []

    try {
      for (let i = 0; i < articleIds.length; i++) {
        const params: PeiqApiWrapperType.ArticleUpdateParams = {
          articleId: articleIds[i],
          updateParams: {
            status: 'published',
          },
        }

        articleQueue.push(PeiqApiWrapper.ArticleApi.updateArticle(params))
      }

      const results = await Promise.all(articleQueue)

      for (let i = 0; i < results.length; i++) {
        if (results[i].status !== 204) {
          failed.push(articleIds[i])
        } else {
          updated.push(articleIds[i])
        }
      }
    } catch (error) {
      logger.error(error)
    }

    logger.info(`Rollback results - Updated: ${updated.length}, Failed: ${failed.length}`)
    logger.info(`Failed article IDs: ${failed.toString}`)
  }
}
