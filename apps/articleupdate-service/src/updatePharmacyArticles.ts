import { logger } from '@regionalmedienaustria/microservice-utils'

import { PeiqApiWrapper } from './peiqApiWrapper.js'

import type { UpdateArticles } from './updateArticles.js'
import type {
  PeiqApiParams,
  PeiqApiWrapper as PeiqApiWrapperType,
} from '@regionalmedienaustria/peiq-api-wrapper'

/**
 * Manages automatic updates for articles containing pharmacy widgets
 *
 * This class finds articles that:
 * 1. Were created or updated within the last 24 hours (plus 2-hour buffer)
 * 2. Contain the pharmacy widget (<div data-widget="pharmacy">)
 *
 * It then updates their "last updated" timestamp to 6:00 AM of the current day,
 * so they display "zuletzt aktualisiert am" in the frontend.
 */
export default class UpdatePharmacyArticles implements UpdateArticles {
  private readonly MAX_ARTICLES_TO_UPDATE = 500

  // Parameters for articles that were created in the last 24h+buffer
  private getRecentArticlesParams(filterType: 'published' | 'updated'): PeiqApiParams {
    const cutoffDate = new Date()
    cutoffDate.setHours(cutoffDate.getHours() - 26) // 24h + 2h buffer

    const isoString = cutoffDate.toISOString().slice(0, 10)
    logger.info(`Using date_start for ${filterType}: ${isoString}`)

    const params: PeiqApiParams = {
      page: 1,
      pageSize: 75,
      status: 'published',
      dateFilterType: filterType,
      dateStart: isoString,
      sortBy: 'id',
    }

    // Only add userSegments for updated articles
    if (filterType === 'updated') {
      params.userSegments = ['oesterreich', 'redakteur']
    }

    return params
  }

  // Checks if an article contains the Pharmacy-Widget
  private hasPharmacyContainer(article: PeiqApiWrapperType.ArticleData): boolean {
    const codes = article.embeds?.embedcode || {}
    const pharmacyCodes = Object.entries(codes).filter(([, data]) => {
      const content = data as { content?: string }
      return content?.content?.replaceAll("'", '"').indexOf('<div data-widget="pharmacy"') !== -1
    })
    return pharmacyCodes.length >= 1
  }

  /**
   * Fetches a list of pharmacy articles that were created or updated in the last 24h+buffer
   * Combines both recently published and recently updated articles, removes duplicates,
   * and limits the total number of articles to process
   * @returns {number[]} a list of pharmacy article Ids
   */
  async fetchArticles(): Promise<number[]> {
    const recentlyPublishedArticles = await this.findRecentArticles(
      this.getRecentArticlesParams('published'),
      'published',
    )

    const recentlyUpdatedArticles = await this.findRecentArticles(
      this.getRecentArticlesParams('updated'),
      'updated',
    )

    const allRecentArticleIds = [
      ...new Set([...recentlyPublishedArticles, ...recentlyUpdatedArticles]),
    ]

    const limitedArticles = allRecentArticleIds.slice(0, this.MAX_ARTICLES_TO_UPDATE)

    logger.info(
      `Found ${allRecentArticleIds.length} recent pharmacy articles, limited to ${limitedArticles.length}`,
    )

    return limitedArticles
  }

  /**
   * Finds articles for the given parameters and filters by the Pharmacy-Widget
   * @param {PeiqApiParams} params - The parameters for the API request
   * @param {string} type - The type of articles to search for (published or updated)
   * @returns {Promise<number[]>} A list of article IDs that contain the Pharmacy-Widget
   */
  private async findRecentArticles(params: PeiqApiParams, type: string): Promise<number[]> {
    let totalPages = 1
    const articleIds: number[] = []
    params.page = 1

    logger.info(`Searching for recently ${type} pharmacy articles since ${params.dateStart}`)

    while (params.page <= totalPages) {
      try {
        const result = await PeiqApiWrapper.ArticleApi.fetchArticles(params, false, false, false)
        totalPages = result.total_pages

        for (const article of result.data) {
          if (this.hasPharmacyContainer(article)) {
            articleIds.push(Number(article.id))
          }
        }
        params.page++
      } catch (error) {
        logger.error(`Error fetching page ${params.page} for ${type} articles: ${error}`)
        break
      }
    }

    logger.info(`Found ${articleIds.length} recently ${type} pharmacy articles`)
    return articleIds
  }

  async updateMany(articleIds: number[]): Promise<void> {
    const refreshDateString = this.getRefreshDateString()
    logger.info(`Pharmacy article refresh date: ${refreshDateString}`)

    const BATCH_SIZE = 10 // Process 10 articles concurrently
    const total = articleIds.length

    for (let i = 0; i < total; i += BATCH_SIZE) {
      const batch = articleIds.slice(i, i + BATCH_SIZE)
      const results = await Promise.allSettled(
        batch.map((id) => this.updateSingleArticle(id, refreshDateString)),
      )

      const succeeded = results.filter((r) => r.status === 'fulfilled' && r.value).length
      const failed = batch.length - succeeded

      logger.info(
        `Batch processed: ${succeeded} succeeded, ${failed} failed. Progress: ${
          i + batch.length
        }/${total} (${Math.round(((i + batch.length) / total) * 100)}%)`,
      )
    }
  }

  async refresh(dryrun: boolean): Promise<number[]> {
    logger.info('Pharmacy-Articles refresh run start.')
    const articleIds = await this.fetchArticles()

    if (!dryrun) {
      await this.updateMany(articleIds)
    } else {
      logger.info(
        `DRYRUN - Total Pharmacy Articles marked for refreshing: ${
          articleIds.length
        }. Date: ${this.getRefreshDateString()}`,
      )
      logger.info(`DRYRUN - Pharmacy Articles marked for refreshing: ${articleIds.join(',')}`)
    }

    logger.info('Pharmacy-Articles refresh run finished.')
    return articleIds
  }

  // Create date string for the refresh (6:00 AM)
  public getRefreshDateString(): string {
    const now = new Date()
    now.setHours(6 + 1) // Set to 6:00 AM
    now.setMinutes(0)
    now.setSeconds(0)
    now.setMilliseconds(0)
    let isoString = now.toISOString()
    isoString = isoString.slice(0, isoString.indexOf('.')) + '+00:00'
    return isoString
  }

  // Update a single article with retry logic
  private async updateSingleArticle(
    articleId: number,
    refreshDateString: string,
  ): Promise<boolean> {
    const maxRetries = 3
    let retries = 0

    while (retries < maxRetries) {
      try {
        const articleData: PeiqApiWrapperType.ArticleUpdateParams = {
          articleId,
          updateParams: {
            published: refreshDateString,
          },
        }

        const result = await PeiqApiWrapper.ArticleApi.updateArticle(articleData)

        if (result.status === 204) {
          return true
        }

        logger.warn(
          `Failed to update pharmacy article ${articleId}. Info: ${result.status} ${
            result.info
          }. Retry ${retries + 1}/${maxRetries}`,
        )
        retries++
      } catch (error) {
        logger.error(
          `Error updating pharmacy article ${articleId}: ${error}. Retry ${
            retries + 1
          }/${maxRetries}`,
        )
        retries++
      }
    }

    return false
  }

  rollback(articleIds: number[]): void {
    logger.debug(articleIds)
  }
}
