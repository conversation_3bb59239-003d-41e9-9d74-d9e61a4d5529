import { logger } from '@regionalmedienaustria/microservice-utils'

import { PeiqApiWrapper } from './peiqApiWrapper.js'
import ArticleUpdateService from './services/articleUpdateService.js'
import { STATES } from './states.js'

import type { FetchArticleParams, UpdateArticles } from './updateArticles.js'
import type {
  PeiqApiParams,
  PeiqApiWrapper as PeiqApiWrapperType,
} from '@regionalmedienaustria/peiq-api-wrapper'

export default class UpdateObsoleteEvents implements UpdateArticles {
  refresh(dryrun: boolean): void {
    logger.debug(`dryrun: ${dryrun}`)
  }

  /**
   *
   * @param offsetDays - Number of days to go into the past
   * @param dryrun - Prints out event ids when true.
   */
  async cleanup(offsetDays: number, dryrun: boolean) {
    // Update events for each state
    for (let i = 0; i < STATES.length; i++) {
      logger.info(STATES[i].name)
      const state = STATES[i]

      let startDate = new Date()
      startDate.setMonth(startDate.getMonth() - 12)
      startDate.setHours(startDate.getHours() - 24 * (offsetDays + 1))
      const endDate = new Date()
      endDate.setMonth(endDate.getMonth() - 12)

      logger.info(startDate)
      logger.info(endDate)

      try {
        // timespan only one month or less
        if (offsetDays < 31) {
          await this.updateEvents(
            state,
            startDate.toISOString().slice(0, 10),
            endDate.toISOString().slice(0, 10),
            dryrun,
          )
        } else {
          await this.updateEventsMonthly(state, dryrun, offsetDays)
        }
      } catch (error) {
        // continue with next state
        logger.error(error)
        logger.info(
          `Error occured while updating events in ${STATES[i].name}. Continue with next state.`,
        )
      }
    }
  }

  private async updateEvents(
    state: {
      name: string
      id: number
    },
    startDate: string,
    endDate: string,
    dryrun: boolean,
  ) {
    try {
      const timestamp = new Date().toISOString()

      const stateEventIds = await this.fetchArticles({
        locationId: state.id,
        dateStart: startDate,
        dateEnd: endDate,
      })

      if (!dryrun) {
        await this.updateMany(stateEventIds, state.name)
      } else {
        logger.info(`DRYRUN - Updating ${stateEventIds.length} events in ${state.name}.`)

        for (let y = 0; y < stateEventIds.length; y++) {
          ArticleUpdateService.writeToLog('events', stateEventIds[y], state.name, timestamp)
        }
        //logger.info(`${stateEventIds.toString()}`)
      }
    } catch (error: any) {
      if (error.response) {
        logger.error('404')
      } else {
        throw error
      }
    }
  }

  private async updateEventsMonthly(
    state: {
      name: string
      id: number
    },
    dryrun: boolean,
    offsetDays: number,
  ) {
    let startDate = new Date()
    startDate.setMonth(startDate.getMonth() - 12)
    startDate.setHours(startDate.getHours() - 24 * (offsetDays + 1))
    const endDate = new Date()
    endDate.setMonth(endDate.getMonth() - 12)

    while (startDate < endDate) {
      const start = startDate.toISOString().slice(0, 10)
      startDate.setMonth(startDate.getMonth() + 1)

      if (startDate >= endDate) {
        continue
      }

      const end = startDate.toISOString().slice(0, 10)
      logger.info(`Continue until ${end}`)
      try {
        await this.updateEvents(state, start, end, dryrun)
      } catch (error) {
        // ECONNRESET or ECONNREFUSED
        // move on to next state
        throw error
      }
    }
  }

  async fetchArticles(params: FetchArticleParams): Promise<number[]> {
    const apiParams: PeiqApiParams = {
      pageSize: 100,
      dateStart: params.dateStart,
      dateEnd: params.dateEnd,
      status: 'published',
      locationId: params.locationId,
      userSegments: ['oesterreich', 'regionaut', 'redakteur', 'freier-mitarbeiter'],
      sortDir: 'asc',
      sortBy: 'created',
    }

    const result = await PeiqApiWrapper.EventApi.fetchEvents(apiParams, false, false)
    logger.info(`Total pages: ${result.total_pages}. Total results: ${result.total_results}`)

    const eventQueue: Promise<any>[] = []
    const eventIds: number[] = []

    try {
      for (let i = 0; i < result.total_pages; i++) {
        apiParams.page = i + 1
        eventQueue.push(PeiqApiWrapper.EventApi.fetchEvents(apiParams, false, false))
      }

      const results = await Promise.all(eventQueue)
      const now = new Date()

      results.forEach((eventResult) => {
        for (let i = 0; i < eventResult.data.length; i++) {
          const event = eventResult.data[i]

          const hasFutureEventItem = event.eventitem_dates.some((dateString: string) => {
            const date = new Date(dateString)
            return date > now
          })

          if (event.image_count > 0 && !hasFutureEventItem) {
            eventIds.push(event.id)
          }
        }
      })
    } catch (error) {
      logger.error(error)
    }

    return eventIds
  }

  /**
   * Update a list of events. Set status rejected
   * @param eventIds List of event ids
   * @returns Update response. success, status, info
   */
  async updateMany(eventIds: number[], state: string) {
    const timestamp = new Date().toISOString()

    const eventQueue: Promise<{ success: boolean; status?: number; info: any }>[] = []
    let updated = 0

    try {
      for (let i = 0; i < eventIds.length; i++) {
        const event: PeiqApiWrapperType.EventUpdateParams = {
          id: eventIds[i],
          updateParams: {
            status: 'rejected',
          },
        }
        eventQueue.push(PeiqApiWrapper.EventApi.updateEvent(event))
      }

      const results = await Promise.all(eventQueue)

      for (let i = 0; i < results.length; i++) {
        if (results[i].status !== 204) {
          logger.error(
            `Event ${eventIds[i]} update failed. ${results[i].status} - ${results[i].info}`,
          )
        } else {
          updated++
        }
      }

      logger.info(`${updated} ${state} events updated at ${timestamp}`)
    } catch (error) {
      logger.error(error)
    }
  }

  async rollback(articleIds: number[]): Promise<void> {
    logger.info(`Rolling back ${articleIds.length} events.`)

    const updated: number[] = []
    const failed: number[] = []
    const eventQueue: Promise<{ success: boolean; status?: number; info: any }>[] = []

    try {
      for (let i = 0; i < articleIds.length; i++) {
        const params: PeiqApiWrapperType.EventUpdateParams = {
          id: articleIds[i],
          updateParams: {
            status: 'published',
          },
        }

        eventQueue.push(PeiqApiWrapper.EventApi.updateEvent(params))
      }

      const results = await Promise.all(eventQueue)

      for (let i = 0; i < results.length; i++) {
        if (results[i].status !== 204) {
          failed.push(articleIds[i])
        } else {
          updated.push(articleIds[i])
        }
      }
    } catch (error) {
      logger.error(error)
    }

    logger.info(`Rollback results - Updated: ${updated.length}, Failed: ${failed.length}`)
    logger.info(`Failed event IDs: ${failed.toString}`)
  }
}
