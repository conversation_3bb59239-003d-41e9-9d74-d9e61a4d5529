import fs from 'fs'

import { logger } from '@regionalmedienaustria/microservice-utils'

import UpdateFuelArticles from '@/updateFuelArticles.js'
import UpdateNewsArticles from '@/updateNewsArticles.js'
import UpdateObsoleteEvents from '@/updateObsoleteEvents.js'
import UpdatePharmacyArticles from '@/updatePharmacyArticles.js'
import UpdatePrizeArticles from '@/updatePrizeArticles.js'

export default class ArticleUpdateService {
  public static async refreshArticles(articles: string, dryrun: boolean) {
    const refreshTypes = articles.split(',')
    let retval = '' // testing/debugging variable
    let updatedIds: number[] = []

    for (let i = 0; i < refreshTypes.length; i++) {
      const refreshType = refreshTypes[i]
      switch (refreshType) {
        case 'fuel':
          {
            const fuelArticles = new UpdateFuelArticles()
            updatedIds = await fuelArticles.refresh(dryrun)
            retval += ' refreshed fuel'
          }
          break
        case 'news':
          {
            const newsArticles = new UpdateNewsArticles()
            updatedIds = await newsArticles.refresh(dryrun)
            retval += ' refreshed news'
          }
          break
        case 'pharmacy':
          {
            const pharmacyArticles = new UpdatePharmacyArticles()
            updatedIds = await pharmacyArticles.refresh(dryrun)
            retval += ' refreshed pharmacy'
          }
          break
        default:
          break
      }
    }
    return { updateService: retval, ids: updatedIds }
  }

  /**
   *
   * @param articleType
   * @param dryrun
   * @param offsetDays
   */
  public static async cleanup(
    articleType: 'articles' | 'events',
    dryrun: boolean,
    offsetDays: number,
  ) {
    if (articleType === 'articles') {
      const prizeArticles = new UpdatePrizeArticles()
      await prizeArticles.cleanup(offsetDays, dryrun)
    } else if (articleType === 'events') {
      const events = new UpdateObsoleteEvents()
      await events.cleanup(offsetDays, dryrun)
    }
    logger.info('Cleanup finished.')
    return { msg: 'update finished' }
  }

  /**
   * getDateIsoString
   *
   * @param offsetDays - Number of past days from date
   * @returns The date as iso string
   */
  public static getDateIsoString(offsetDays: number): string {
    const date = new Date()
    date.setDate(date.getDate() - offsetDays)
    let isoString = date.toISOString()
    isoString = isoString.slice(0, 10) + 'T00:00:00+00:00'
    return isoString
  }

  public static async rollback(articleType: string, articleIds: number[]) {
    if (articleType === 'articles') {
      const prizeArticles = new UpdatePrizeArticles()
      prizeArticles.rollback(articleIds)
    } else if (articleType === 'events') {
      const events = new UpdateObsoleteEvents()
      await events.rollback(articleIds)
    }
  }

  public static writeToLog(articleType: string, id: number, state: string, timestamp: string) {
    fs.appendFile(`${articleType}.csv`, `${id};${state};${timestamp}\n`, function (err) {
      if (err) {
        return logger.error(err)
      }
    })
  }

  public static async getArticleIdsFromCSV(articleType: string): Promise<number[]> {
    try {
      const csvFile = fs.readFileSync(`./${articleType}.csv`)

      return csvFile
        .toString()
        .split('\n')
        .map((line) => +line.split(';')[0])
    } catch (error) {
      logger.error('No values found.')
      return []
    }
  }
}
