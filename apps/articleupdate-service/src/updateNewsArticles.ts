import { logger } from '@regionalmedienaustria/microservice-utils'

import type { UpdateArticles } from './updateArticles.js'

export default class UpdateNewsArticles implements UpdateArticles {
  async refresh(dryrun: boolean = true): Promise<number[]> {
    logger.debug(dryrun)
    return []
  }

  async fetchArticles(): Promise<number[]> {
    return []
  }

  async updateMany(): Promise<void> {
    return
  }

  rollback(articleIds: number[]): void {
    logger.debug(articleIds)
  }
}
