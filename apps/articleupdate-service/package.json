{"name": "@rma-mono/articlepdate-service", "version": "0.0.1", "description": "", "main": "lib/src/index.js", "keywords": [], "author": "", "license": "ISC", "type": "module", "scripts": {"build": "tsc --p ./tsconfig.json && tsc-alias -p ./tsconfig.json", "debug": "source $HOME/.nvm/nvm.sh; nvm use && run-p lint && run-p build && main=lib/src/index.js NODE_ENV=local nodemon -e tsc lib/src/index.js && tsc-alias src/index.ts --inspect", "dev": "source $HOME/.nvm/nvm.sh; nvm use && NODE_ENV=local tsx watch src/index.ts", "lint": "eslint \"**/*.{ts,tsx,js,jsx}\"", "lint:fix": "npm run lint -- --fix", "start": "node lib/src/index.js", "pretest": "run-p typecheck lint", "test": "vitest", "typecheck": "tsc --noEmit"}, "dependencies": {"@regionalmedienaustria/microservice-utils": "~1.6.0", "@regionalmedienaustria/peiq-api-wrapper": "^2.2.3-alpha.1", "@types/jest": "^29.5.3", "body-parser": "^1.19.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "fast-glob": "^3.2.2", "lodash": "^4.17.15", "module-alias": "^2.2.2", "normalize-path": "^3.0.0", "npm-run-all": "^4.1.5", "path": "^0.12.7", "path-to-regexp": "^6.1.0", "promise": "^8.3.0", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.20.2", "@babel/node": "^7.20.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.20.2", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@babel/register": "^7.18.9", "@rma-mono/eslint-config-backend": "^1.13", "@rma-mono/tsconfig-shared": "^1.0.0", "@types/cors": "^2.8.12", "@types/express": "^4.17.14", "@types/lodash": "^4.14.189", "@types/node": "^14.18.33", "@types/sinon": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.44.0", "@typescript-eslint/parser": "^5.44.0", "axios": "~1.6.7", "babel-plugin-transform-typescript-metadata": "^0.3.2", "copyfiles": "^2.3.0", "eslint": "^8.28.0", "google-artifactregistry-auth": "^3.0.2", "helmet": "^6.0.1", "nodemon": "^2.0.20", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "tsc-alias": "^1.8.7", "tsconfig-paths": "^4.2.0", "tsx": "^3.12.10", "typescript": "^4.9.4", "vitest": "^2.0.5"}, "engines": {"node": ">=18.0.0"}}