{"typescript.tsserver.experimental.enableProjectDiagnostics": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnPaste": false, "prettier.useEditorConfig": false, "prettier.useTabs": false, "prettier.configPath": ".prettier<PERSON>", "editor.formatOnSaveMode": "file", "prettier.requireConfig": true, "eslint.workingDirectories": [{"mode": "auto"}], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.ignoreUntitled": true, "eslint.validate": ["javascript", "typescript"], "eslint.format.enable": true, "eslint.run": "onSave", "javascript.preferences.importModuleSpecifier": "non-relative", "javascript.updateImportsOnFileMove.enabled": "always", "typescript.updateImportsOnFileMove.enabled": "always", "html.format.indentHandlebars": true, "[handlebars]": {"editor.formatOnSave": false, "editor.formatOnPaste": false}}