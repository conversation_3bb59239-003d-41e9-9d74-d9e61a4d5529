{"name": "rma-mono", "private": true, "scripts": {"format": "prettier --write .", "format:check": "prettier --check .", "mono:reinstall": "npm exec --workspaces -c 'rm -rf node_modules dist' && npm run gcpInstall", "mono:init": "npm run gcpInstall && npm run build:packages && npm run build:apps", "mono:reset": "npm run mono:reinstall && npm run mono:init", "gcpInstall": "export GOOGLE_APPLICATION_CREDENTIALS=./gcp-sa-key.json && npx google-artifactregistry-auth && npm install", "postinstall": "chmod +x ./ci/build-packages.sh", "build:packages": "./ci/build-packages.sh", "build:apps": "npm run build -w=apps", "typecheck": "npm run typecheck:packages && npm run typecheck:apps", "typecheck:packages": "npm run typecheck -w=packages", "typecheck:apps": "npm run typecheck -w=apps", "build": "npm run build:packages && npm run build:apps", "gcpInstallCi": "export GOOGLE_APPLICATION_CREDENTIALS=./gcp-sa-key.json && npx google-artifactregistry-auth && npm ci", "panda:studio": "npm run studio -w=packages/panda-shared", "prepare": "husky", "test:e2e": "npm test -w=e2e", "create-microservice": "plop microservice && npm run gcpInstall && echo \"\n \\033[0;31m Do not forget to add the ServiceAccount in the Terraform factory\""}, "workspaces": ["apps/*", "packages/*", "functions/*", "misc/*", "e2e"], "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "husky": "^9.0.11", "npm-run-all": "^4.1.5", "plop": "^4.0.1"}, "dependencies": {"@directus/sdk": "^19.1.0"}}